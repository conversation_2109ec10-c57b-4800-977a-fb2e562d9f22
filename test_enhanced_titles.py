#!/usr/bin/env python3
"""
اختبار العناوين المحسنة
Test Enhanced Titles
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QPushButton, QLabel, QLineEdit, QComboBox, QTableWidget,
    QGroupBox, QCheckBox, QTabWidget, QTextEdit, QTableWidgetItem
)
from PySide6.QtCore import Qt


def create_titles_test_window():
    """إنشاء نافذة اختبار العناوين"""
    
    class TitlesTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار العناوين المحسنة")
            self.setGeometry(100, 100, 1000, 700)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # العنوان الرئيسي (20px)
            main_title = QLabel("🎨 العنوان الرئيسي للتطبيق")
            main_title.setObjectName("app_title")
            main_title.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(main_title)
            
            # عنوان النافذة (18px)
            window_title = QLabel("📋 عنوان النافذة أو الصفحة")
            window_title.setObjectName("window_title")
            window_title.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(window_title)
            
            # عنوان القسم (16px)
            section_title = QLabel("📂 عنوان القسم أو المجموعة")
            section_title.setObjectName("section_title")
            section_title.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(section_title)
            
            # التبويبات مع عناوين محسنة
            tabs = QTabWidget()
            
            # تبويب الأزرار
            buttons_tab = QWidget()
            buttons_layout = QVBoxLayout(buttons_tab)
            
            buttons_group = QGroupBox("مجموعة الأزرار")
            buttons_group_layout = QHBoxLayout(buttons_group)
            
            btn1 = QPushButton("زر أساسي")
            btn2 = QPushButton("زر ثانوي")
            btn3 = QPushButton("زر نجاح")
            
            buttons_group_layout.addWidget(btn1)
            buttons_group_layout.addWidget(btn2)
            buttons_group_layout.addWidget(btn3)
            
            buttons_layout.addWidget(buttons_group)
            tabs.addTab(buttons_tab, "الأزرار")
            
            # تبويب الجداول
            tables_tab = QWidget()
            tables_layout = QVBoxLayout(tables_tab)
            
            table_group = QGroupBox("مجموعة الجداول")
            table_group_layout = QVBoxLayout(table_group)
            
            table = QTableWidget(4, 3)
            table.setHorizontalHeaderLabels(["العمود الأول", "العمود الثاني", "العمود الثالث"])
            
            # ملء الجدول ببيانات تجريبية
            for row in range(4):
                for col in range(3):
                    item = QTableWidgetItem(f"خلية {row+1}-{col+1}")
                    table.setItem(row, col, item)
            
            table_group_layout.addWidget(table)
            tables_layout.addWidget(table_group)
            tabs.addTab(tables_tab, "الجداول")
            
            # تبويب الحقول
            inputs_tab = QWidget()
            inputs_layout = QVBoxLayout(inputs_tab)
            
            inputs_group = QGroupBox("مجموعة حقول الإدخال")
            inputs_group_layout = QVBoxLayout(inputs_group)
            
            line_edit = QLineEdit("حقل نص تجريبي")
            combo_box = QComboBox()
            combo_box.addItems(["خيار 1", "خيار 2", "خيار 3"])
            text_edit = QTextEdit("منطقة نص متعددة الأسطر")
            text_edit.setMaximumHeight(100)
            
            inputs_group_layout.addWidget(QLabel("حقل النص:"))
            inputs_group_layout.addWidget(line_edit)
            inputs_group_layout.addWidget(QLabel("القائمة المنسدلة:"))
            inputs_group_layout.addWidget(combo_box)
            inputs_group_layout.addWidget(QLabel("منطقة النص:"))
            inputs_group_layout.addWidget(text_edit)
            
            inputs_layout.addWidget(inputs_group)
            tabs.addTab(inputs_tab, "حقول الإدخال")
            
            main_layout.addWidget(tabs)
            
            # شريط التحكم
            control_panel = self.create_control_panel()
            main_layout.addWidget(control_panel)
        
        def create_control_panel(self) -> QWidget:
            """إنشاء لوحة التحكم"""
            panel = QGroupBox("التحكم في أحجام العناوين")
            layout = QHBoxLayout(panel)
            
            # أزرار اختبار الأحجام
            test_20_btn = QPushButton("اختبار 20px (رئيسي)")
            test_18_btn = QPushButton("اختبار 18px (نافذة)")
            test_16_btn = QPushButton("اختبار 16px (قسم)")
            reset_btn = QPushButton("إعادة تعيين")
            
            test_20_btn.clicked.connect(lambda: self.test_title_size(20))
            test_18_btn.clicked.connect(lambda: self.test_title_size(18))
            test_16_btn.clicked.connect(lambda: self.test_title_size(16))
            reset_btn.clicked.connect(self.reset_titles)
            
            layout.addWidget(test_20_btn)
            layout.addWidget(test_18_btn)
            layout.addWidget(test_16_btn)
            layout.addWidget(reset_btn)
            layout.addStretch()
            
            return panel
        
        def test_title_size(self, size: int):
            """اختبار حجم عنوان معين"""
            print(f"🔤 اختبار حجم العنوان: {size}px")
            
            # تطبيق الحجم على جميع العناوين مؤقتاً
            for child in self.findChildren(QLabel):
                if child.objectName() in ["app_title", "window_title", "section_title"]:
                    child.setStyleSheet(f"font-size: {size}px; font-weight: bold;")
        
        def reset_titles(self):
            """إعادة تعيين العناوين للأحجام الافتراضية"""
            print("🔄 إعادة تعيين العناوين للأحجام الافتراضية")
            
            # إزالة الأنماط المؤقتة
            for child in self.findChildren(QLabel):
                if child.objectName() in ["app_title", "window_title", "section_title"]:
                    child.setStyleSheet("")
    
    return TitlesTestWindow()


def test_title_sizes():
    """اختبار أحجام العناوين"""
    print("🔍 اختبار أحجام العناوين...")
    
    sizes = {
        "app_title": 20,
        "window_title": 18,
        "section_title": 16
    }
    
    for title_type, expected_size in sizes.items():
        print(f"   {title_type}: {expected_size}px")
    
    print("✅ تم تحديد أحجام العناوين")
    return True


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار العناوين المحسنة")
    print("=" * 50)
    
    # اختبار أحجام العناوين
    if not test_title_sizes():
        print("💥 فشل في اختبار أحجام العناوين")
        return 1
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط المحسنة
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    app.setStyleSheet(f.read())
                print("✅ تم تحميل الأنماط المحسنة")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # تهيئة نظام المظهر
        try:
            from src.utils.appearance_manager import get_appearance_manager
            appearance_manager = get_appearance_manager()
            appearance_manager.load_settings()
            print("✅ تم تهيئة نظام المظهر")
        except Exception as e:
            print(f"⚠️ فشل في تهيئة نظام المظهر: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_titles_test_window()
        window.show()
        
        print("✅ تم فتح نافذة اختبار العناوين!")
        print("\n🎯 أحجام العناوين الجديدة:")
        print("• العنوان الرئيسي (app_title): 20px")
        print("• عنوان النافذة (window_title): 18px")
        print("• عنوان القسم (section_title): 16px")
        print("• عناوين الجداول: حجم الخط العادي + 2px")
        print("• عناوين التبويبات: حجم الخط العادي + 1px")
        print("• عناوين المجموعات: 16px")
        
        print("\n🎨 الميزات:")
        print("• خط عريض للعناوين")
        print("• ألوان مميزة")
        print("• هوامش محسنة")
        print("• تطبيق تلقائي من CSS")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
