#!/usr/bin/env python3
"""
ملف اختبار تشغيل النظام
Test Run File
"""

import sys
import os
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    print("🚀 بدء تشغيل نظام إدارة شؤون الموظفين...")
    
    # اختبار الاستيرادات الأساسية
    print("📦 اختبار الاستيرادات...")
    
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import Qt
    print("✅ تم استيراد PySide6 بنجاح")
    
    from src.config import get_config, ensure_directories
    print("✅ تم استيراد إعدادات التطبيق بنجاح")
    
    from src.database import db_manager
    print("✅ تم استيراد مدير قاعدة البيانات بنجاح")
    
    # إنشاء التطبيق
    print("🖥️  إنشاء التطبيق...")
    app = QApplication(sys.argv)
    app.setApplicationName("نظام إدارة شؤون الموظفين")
    app.setLayoutDirection(Qt.RightToLeft)
    
    # التأكد من وجود المجلدات
    print("📁 التأكد من وجود المجلدات...")
    ensure_directories()
    
    # اختبار قاعدة البيانات
    print("🗄️  اختبار قاعدة البيانات...")
    try:
        db_manager.initialize()
        if db_manager.test_connection():
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            db_manager.create_tables()
            print("✅ تم إنشاء الجداول بنجاح")
        else:
            print("⚠️  فشل في الاتصال بقاعدة البيانات - سيتم استخدام SQLite")
            # التبديل إلى SQLite
            from src.config import update_config
            update_config("database", "type", "sqlite")
            update_config("database", "database", "hr_system.db")
            db_manager.initialize()
            db_manager.create_tables()
            print("✅ تم إنشاء قاعدة بيانات SQLite بنجاح")
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        print("🔄 محاولة استخدام SQLite...")
        try:
            from src.config import update_config
            update_config("database", "type", "sqlite")
            update_config("database", "database", "hr_system.db")
            db_manager.initialize()
            db_manager.create_tables()
            print("✅ تم إنشاء قاعدة بيانات SQLite بنجاح")
        except Exception as sqlite_error:
            print(f"❌ فشل في إنشاء قاعدة بيانات SQLite: {sqlite_error}")
            sys.exit(1)
    
    # اختبار الواجهة
    print("🎨 اختبار الواجهة...")
    try:
        from src.views import MainWindow
        print("✅ تم استيراد النافذة الرئيسية بنجاح")
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # عرض النافذة
        main_window.show()
        print("✅ تم عرض النافذة الرئيسية بنجاح")
        
        print("\n🎉 تم تشغيل النظام بنجاح!")
        print("📋 يمكنك الآن استخدام النظام...")
        
        # تشغيل حلقة الأحداث
        sys.exit(app.exec())
        
    except Exception as ui_error:
        print(f"❌ خطأ في الواجهة: {ui_error}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
        
except ImportError as import_error:
    print(f"❌ خطأ في الاستيراد: {import_error}")
    print("💡 تأكد من تثبيت جميع المتطلبات:")
    print("   pip install -r requirements.txt")
    sys.exit(1)
    
except Exception as general_error:
    print(f"❌ خطأ عام: {general_error}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
