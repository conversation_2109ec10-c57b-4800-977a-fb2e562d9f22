#!/usr/bin/env python3
"""
اختبار الشريط العلوي مع النصوص الكبيرة والتنسيق الجديد
Test Large Text Header with New Design
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QFrame, QHBoxLayout, QPushButton
from PySide6.QtCore import Qt


def create_large_text_header_test():
    """إنشاء نافذة اختبار الشريط العلوي مع النصوص الكبيرة"""
    
    class LargeTextHeaderTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار الشريط العلوي - نصوص كبيرة وتنسيق جديد")
            self.setGeometry(50, 50, 1400, 900)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            # إنشاء الشريط العلوي الجديد
            self.create_large_text_header()
            main_layout.addWidget(self.header)
            
            # محتوى الاختبار
            content_widget = QWidget()
            content_layout = QVBoxLayout(content_widget)
            content_layout.setContentsMargins(40, 40, 40, 40)
            content_layout.setSpacing(30)
            
            # عنوان الاختبار
            test_title = QLabel("🎯 اختبار الشريط العلوي - نصوص كبيرة وتنسيق جديد")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 28px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 25px;
                    padding: 25px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #e74c3c, stop: 0.5 #c0392b, stop: 1 #a93226);
                    color: white;
                    border-radius: 15px;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                }
            """)
            content_layout.addWidget(test_title)
            
            # معلومات التحسينات الجديدة
            improvements_info = QLabel("""
            🎨 التحسينات الجديدة مع النصوص الكبيرة:
            
            📏 أحجام النصوص المحسنة:
            • العنوان الرئيسي: 32px (كان 24px) - زيادة 33%
            • العنوان الفرعي: 18px (كان 14px) - زيادة 29%
            • نص الترحيب: 16px (كان 12px) - زيادة 33%
            • اسم المستخدم: 18px (كان 14px) - زيادة 29%
            • أيقونة المستخدم: 28px (كان 20px) - زيادة 40%
            • زر الإعدادات: 28px (كان 20px) - زيادة 40%
            
            🎯 التحسينات في التصميم:
            • ارتفاع الشريط: 110px (كان 90px)
            • عرض بطاقة المستخدم: 280px (كان 200px)
            • ارتفاع بطاقة المستخدم: 70px (كان 50px)
            • حجم زر الإعدادات: 65×65px (كان 50×50px)
            • حجم أيقونة المستخدم: 45×45px (كان 30×30px)
            
            🌈 الألوان والتأثيرات الجديدة:
            • خلفية متدرجة ثلاثية للشريط العلوي
            • حدود سفلية متدرجة (أزرق → بنفسجي)
            • بطاقة مستخدم بتدرج أخضر → أزرق → بنفسجي
            • أيقونة مستخدم بخلفية متدرجة ودائرية
            • زر إعدادات برتقالي متدرج مع تأثيرات hover
            • ظلال محسنة لجميع العناصر
            
            ✨ التفاصيل الإضافية:
            • تباعد أحرف محسن (3px للعنوان الرئيسي)
            • نص فرعي أطول وأكثر وصفية
            • أيقونات إضافية (🌟 للترحيب، 👨‍💼 للمدير، 💼 للنظام)
            • تأثيرات hover وpress محسنة
            • ظلال متدرجة وحدود ملونة
            """)
            improvements_info.setAlignment(Qt.AlignLeft)
            improvements_info.setStyleSheet("""
                QLabel {
                    font-size: 15px;
                    color: #2c3e50;
                    background-color: #f8f9fa;
                    padding: 30px;
                    border-radius: 15px;
                    border-left: 6px solid #3498db;
                    line-height: 1.7;
                    border: 2px solid #e9ecef;
                }
            """)
            content_layout.addWidget(improvements_info)
            
            # مقارنة الأحجام
            comparison_frame = QFrame()
            comparison_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 3px solid #bdc3c7;
                    border-radius: 15px;
                    padding: 25px;
                }
            """)
            
            comparison_layout = QVBoxLayout(comparison_frame)
            
            comparison_title = QLabel("📊 مقارنة أحجام النصوص")
            comparison_title.setAlignment(Qt.AlignCenter)
            comparison_title.setStyleSheet("""
                QLabel {
                    font-size: 22px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 20px;
                    padding: 15px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                              stop: 0 rgba(52, 152, 219, 0.1),
                                              stop: 1 rgba(155, 89, 182, 0.1));
                    border-radius: 10px;
                }
            """)
            comparison_layout.addWidget(comparison_title)
            
            # جدول المقارنة
            comparison_text = QLabel("""
            📏 جدول مقارنة الأحجام:
            
            ┌─────────────────────────┬──────────┬──────────┬─────────────┐
            │ العنصر                  │ القديم    │ الجديد    │ نسبة الزيادة │
            ├─────────────────────────┼──────────┼──────────┼─────────────┤
            │ العنوان الرئيسي         │ 24px     │ 32px     │ +33%       │
            │ العنوان الفرعي          │ 14px     │ 18px     │ +29%       │
            │ نص الترحيب             │ 12px     │ 16px     │ +33%       │
            │ اسم المستخدم           │ 14px     │ 18px     │ +29%       │
            │ أيقونة المستخدم         │ 20px     │ 28px     │ +40%       │
            │ زر الإعدادات           │ 20px     │ 28px     │ +40%       │
            │ ارتفاع الشريط           │ 90px     │ 110px    │ +22%       │
            │ عرض بطاقة المستخدم      │ 200px    │ 280px    │ +40%       │
            │ ارتفاع بطاقة المستخدم    │ 50px     │ 70px     │ +40%       │
            │ حجم زر الإعدادات        │ 50×50px  │ 65×65px  │ +30%       │
            └─────────────────────────┴──────────┴──────────┴─────────────┘
            
            🎯 النتيجة: تحسن كبير في الوضوح والقراءة مع زيادة متوسطة 33% في أحجام النصوص!
            """)
            comparison_text.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #34495e;
                    font-family: 'Courier New', monospace;
                    line-height: 1.5;
                    padding: 20px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    border: 1px solid #dee2e6;
                }
            """)
            comparison_layout.addWidget(comparison_text)
            
            content_layout.addWidget(comparison_frame)
            content_layout.addStretch()
            
            main_layout.addWidget(content_widget)
            
        def create_large_text_header(self):
            """إنشاء الشريط العلوي مع النصوص الكبيرة"""
            self.header = QFrame()
            self.header.setObjectName("enhanced_header")
            self.header.setFixedHeight(110)

            header_layout = QHBoxLayout(self.header)
            header_layout.setContentsMargins(50, 25, 50, 25)
            header_layout.setSpacing(40)

            # قسم العنوان الرئيسي المحسن
            title_section = QVBoxLayout()
            title_section.setSpacing(8)

            # العنوان الرئيسي مع نص أكبر
            main_title = QLabel("🏢 نظام إدارة الموارد البشرية")
            main_title.setObjectName("main_header_title")
            main_title.setAlignment(Qt.AlignLeft)

            # العنوان الفرعي مع نص أكبر
            subtitle = QLabel("💼 نظام شامل ومتطور لإدارة الموظفين والمعاملات المالية بكفاءة عالية")
            subtitle.setObjectName("header_subtitle")
            subtitle.setAlignment(Qt.AlignLeft)

            title_section.addWidget(main_title)
            title_section.addWidget(subtitle)

            # قسم معلومات المستخدم المحسن مع نصوص أكبر
            user_section = QHBoxLayout()
            user_section.setSpacing(20)

            # بطاقة المستخدم محسنة
            user_card = QFrame()
            user_card.setObjectName("user_card")
            user_card.setFixedSize(280, 70)
            
            user_card_layout = QHBoxLayout(user_card)
            user_card_layout.setContentsMargins(20, 12, 20, 12)
            user_card_layout.setSpacing(15)

            # أيقونة المستخدم أكبر
            user_icon = QLabel("👤")
            user_icon.setObjectName("user_icon")
            user_icon.setFixedSize(45, 45)
            user_icon.setAlignment(Qt.AlignCenter)

            # معلومات المستخدم مع نصوص أكبر
            user_info_layout = QVBoxLayout()
            user_info_layout.setSpacing(4)
            
            welcome_label = QLabel("🌟 مرحباً بك في النظام")
            welcome_label.setObjectName("welcome_label")
            
            user_name = QLabel("👨‍💼 المدير العام")
            user_name.setObjectName("user_name")

            user_info_layout.addWidget(welcome_label)
            user_info_layout.addWidget(user_name)

            user_card_layout.addWidget(user_icon)
            user_card_layout.addLayout(user_info_layout)
            user_card_layout.addStretch()

            # زر الإعدادات أكبر ومحسن
            settings_btn = QPushButton("⚙️")
            settings_btn.setObjectName("enhanced_settings_btn")
            settings_btn.setFixedSize(65, 65)
            settings_btn.setToolTip("إعدادات النظام والتحكم")
            settings_btn.clicked.connect(lambda: print("🔄 تم النقر على زر الإعدادات المحسن"))

            user_section.addWidget(user_card)
            user_section.addWidget(settings_btn)

            # تجميع الأقسام
            header_layout.addLayout(title_section)
            header_layout.addStretch()
            header_layout.addLayout(user_section)
    
    return LargeTextHeaderTest()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الشريط العلوي مع النصوص الكبيرة والتنسيق الجديد")
    print("=" * 80)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            enhanced_style_file = styles_dir / "enhanced_dashboard.qss"
            
            combined_styles = ""
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read() + "\n"
                    
            if enhanced_style_file.exists():
                with open(enhanced_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read()
                    
            app.setStyleSheet(combined_styles)
            print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_large_text_header_test()
        window.show()
        
        print("✅ تم فتح نافذة اختبار النصوص الكبيرة!")
        print("\n📏 أحجام النصوص الجديدة:")
        print("• العنوان الرئيسي: 32px (+33%)")
        print("• العنوان الفرعي: 18px (+29%)")
        print("• نص الترحيب: 16px (+33%)")
        print("• اسم المستخدم: 18px (+29%)")
        print("• أيقونة المستخدم: 28px (+40%)")
        print("• زر الإعدادات: 28px (+40%)")
        
        print("\n🎨 التحسينات في التصميم:")
        print("• ارتفاع الشريط: 110px (+22%)")
        print("• عرض بطاقة المستخدم: 280px (+40%)")
        print("• ارتفاع بطاقة المستخدم: 70px (+40%)")
        print("• حجم زر الإعدادات: 65×65px (+30%)")
        
        print("\n🌈 الألوان الجديدة:")
        print("• خلفية متدرجة ثلاثية")
        print("• حدود سفلية متدرجة")
        print("• بطاقة مستخدم بتدرج ملون")
        print("• زر إعدادات برتقالي متدرج")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
