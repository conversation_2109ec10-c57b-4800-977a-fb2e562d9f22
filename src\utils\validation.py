"""
دوال التحقق من صحة البيانات المحسنة
Enhanced Data Validation Functions
"""

import re
from datetime import datetime, date
from typing import Any, Optional, Union, List, Dict, Tuple
from decimal import Decimal, InvalidOperation


class ValidationError(Exception):
    """خطأ في التحقق من صحة البيانات"""
    pass


class Validator:
    """فئة التحقق من صحة البيانات"""
    
    @staticmethod
    def validate_required(value: Any, field_name: str) -> None:
        """التحقق من الحقول المطلوبة"""
        if value is None or (isinstance(value, str) and not value.strip()):
            raise ValidationError(f"الحقل '{field_name}' مطلوب")
    
    @staticmethod
    def validate_string_length(value: str, field_name: str, min_length: int = 0, max_length: int = None) -> None:
        """التحقق من طول النص"""
        if value is None:
            return
        
        length = len(value.strip())
        
        if length < min_length:
            raise ValidationError(f"الحقل '{field_name}' يجب أن يكون على الأقل {min_length} أحرف")
        
        if max_length and length > max_length:
            raise ValidationError(f"الحقل '{field_name}' يجب أن يكون أقل من {max_length} حرف")
    
    @staticmethod
    def validate_numeric_range(value: Union[int, float, Decimal], field_name: str, 
                             min_value: float = None, max_value: float = None) -> None:
        """التحقق من نطاق الأرقام"""
        if value is None:
            return
        
        try:
            numeric_value = float(value)
        except (ValueError, TypeError):
            raise ValidationError(f"الحقل '{field_name}' يجب أن يكون رقماً")
        
        if min_value is not None and numeric_value < min_value:
            raise ValidationError(f"الحقل '{field_name}' يجب أن يكون على الأقل {min_value}")
        
        if max_value is not None and numeric_value > max_value:
            raise ValidationError(f"الحقل '{field_name}' يجب أن يكون أقل من {max_value}")
    
    @staticmethod
    def validate_positive_number(value: Union[int, float, Decimal], field_name: str) -> None:
        """التحقق من الأرقام الموجبة"""
        Validator.validate_numeric_range(value, field_name, min_value=0.01)
    
    @staticmethod
    def validate_non_negative_number(value: Union[int, float, Decimal], field_name: str) -> None:
        """التحقق من الأرقام غير السالبة"""
        Validator.validate_numeric_range(value, field_name, min_value=0)
    
    @staticmethod
    def validate_phone_enhanced(phone: str, field_name: str = "رقم الهاتف") -> None:
        """التحقق المحسن من رقم الهاتف العراقي"""
        if not phone or not phone.strip():
            return  # اختياري
        
        # تنظيف الرقم
        cleaned_phone = re.sub(r'[\s\-\(\)]', '', phone)
        
        # أنماط أرقام الهواتف العراقية
        patterns = [
            r'^07[3-9]\d{8}$',  # أرقام الموبايل العراقية
            r'^964[0-9]\d{8}$',  # أرقام دولية
            r'^01\d{8}$',  # أرقام أرضية بغداد
            r'^0[2-6]\d{7}$',  # أرقام أرضية المحافظات
        ]
        
        if not any(re.match(pattern, cleaned_phone) for pattern in patterns):
            raise ValidationError(f"الحقل '{field_name}' يحتوي على رقم هاتف غير صحيح")
    
    @staticmethod
    def validate_email_enhanced(email: str, field_name: str = "البريد الإلكتروني") -> None:
        """التحقق المحسن من البريد الإلكتروني"""
        if not email or not email.strip():
            return  # اختياري
        
        # نمط البريد الإلكتروني المحسن
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        if not re.match(pattern, email.strip()):
            raise ValidationError(f"الحقل '{field_name}' يحتوي على بريد إلكتروني غير صحيح")
        
        # التحقق من طول البريد الإلكتروني
        if len(email.strip()) > 254:
            raise ValidationError(f"الحقل '{field_name}' طويل جداً")
    
    @staticmethod
    def validate_employee_number_enhanced(employee_number: str, field_name: str = "الرقم الوظيفي") -> None:
        """التحقق المحسن من الرقم الوظيفي"""
        Validator.validate_required(employee_number, field_name)
        
        cleaned_number = employee_number.strip()
        
        # يجب أن يكون بين 3-20 حرف
        Validator.validate_string_length(cleaned_number, field_name, 3, 20)
        
        # يجب أن يحتوي على أرقام وحروف إنجليزية فقط
        if not re.match(r'^[A-Za-z0-9]+$', cleaned_number):
            raise ValidationError(f"الحقل '{field_name}' يجب أن يحتوي على أحرف إنجليزية وأرقام فقط")
    
    @staticmethod
    def validate_national_id(national_id: str, field_name: str = "رقم الهوية") -> None:
        """التحقق من رقم الهوية العراقية"""
        if not national_id or not national_id.strip():
            return  # اختياري
        
        cleaned_id = national_id.strip()
        
        # رقم الهوية العراقية يجب أن يكون 12 رقم
        if not re.match(r'^\d{12}$', cleaned_id):
            raise ValidationError(f"الحقل '{field_name}' يجب أن يكون 12 رقماً")
    
    @staticmethod
    def validate_date_range(date_value: date, field_name: str, 
                          min_date: date = None, max_date: date = None) -> None:
        """التحقق من نطاق التاريخ"""
        if date_value is None:
            return
        
        if min_date and date_value < min_date:
            raise ValidationError(f"الحقل '{field_name}' يجب أن يكون بعد {min_date}")
        
        if max_date and date_value > max_date:
            raise ValidationError(f"الحقل '{field_name}' يجب أن يكون قبل {max_date}")
    
    @staticmethod
    def validate_hire_date(hire_date: date, field_name: str = "تاريخ المباشرة") -> None:
        """التحقق من تاريخ المباشرة"""
        Validator.validate_required(hire_date, field_name)
        
        # لا يمكن أن يكون في المستقبل
        today = date.today()
        if hire_date > today:
            raise ValidationError(f"الحقل '{field_name}' لا يمكن أن يكون في المستقبل")
        
        # لا يمكن أن يكون قبل 50 سنة (منطقي للعمل)
        min_date = date(today.year - 50, 1, 1)
        if hire_date < min_date:
            raise ValidationError(f"الحقل '{field_name}' قديم جداً")
    
    @staticmethod
    def validate_salary_range(min_salary: float, max_salary: float, 
                            field_name_min: str = "الحد الأدنى للراتب",
                            field_name_max: str = "الحد الأقصى للراتب") -> None:
        """التحقق من نطاق الراتب"""
        if min_salary is not None:
            Validator.validate_positive_number(min_salary, field_name_min)
        
        if max_salary is not None:
            Validator.validate_positive_number(max_salary, field_name_max)
        
        if min_salary is not None and max_salary is not None:
            if min_salary > max_salary:
                raise ValidationError(f"الحد الأدنى للراتب يجب أن يكون أقل من الحد الأقصى")
    
    @staticmethod
    def validate_code_format(code: str, field_name: str, min_length: int = 2, max_length: int = 10) -> None:
        """التحقق من تنسيق الرمز"""
        Validator.validate_required(code, field_name)
        
        cleaned_code = code.strip().upper()
        
        # التحقق من الطول
        Validator.validate_string_length(cleaned_code, field_name, min_length, max_length)
        
        # يجب أن يحتوي على أحرف وأرقام فقط
        if not re.match(r'^[A-Z0-9]+$', cleaned_code):
            raise ValidationError(f"الحقل '{field_name}' يجب أن يحتوي على أحرف إنجليزية كبيرة وأرقام فقط")


class FormValidator:
    """فئة التحقق من صحة النماذج"""
    
    def __init__(self):
        self.errors: List[str] = []
    
    def add_error(self, error: str):
        """إضافة خطأ"""
        self.errors.append(error)
    
    def validate_field(self, validation_func, *args, **kwargs):
        """التحقق من حقل مع التعامل مع الأخطاء"""
        try:
            validation_func(*args, **kwargs)
        except ValidationError as e:
            self.add_error(str(e))
    
    def is_valid(self) -> bool:
        """التحقق من صحة النموذج"""
        return len(self.errors) == 0
    
    def get_errors(self) -> List[str]:
        """الحصول على قائمة الأخطاء"""
        return self.errors.copy()
    
    def get_first_error(self) -> str:
        """الحصول على أول خطأ"""
        return self.errors[0] if self.errors else ""
    
    def clear_errors(self):
        """مسح الأخطاء"""
        self.errors.clear()


def validate_employee_data(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """التحقق من بيانات الموظف"""
    validator = FormValidator()
    
    # التحقق من الحقول المطلوبة
    validator.validate_field(Validator.validate_employee_number_enhanced, 
                           data.get('employee_number'), 'الرقم الوظيفي')
    validator.validate_field(Validator.validate_required, 
                           data.get('full_name'), 'الاسم الكامل')
    validator.validate_field(Validator.validate_required, 
                           data.get('department_id'), 'القسم')
    validator.validate_field(Validator.validate_required, 
                           data.get('job_title_id'), 'العنوان الوظيفي')
    validator.validate_field(Validator.validate_positive_number, 
                           data.get('basic_salary'), 'الراتب الأساسي')
    validator.validate_field(Validator.validate_hire_date, 
                           data.get('hire_date'), 'تاريخ المباشرة')
    
    # التحقق من الحقول الاختيارية
    if data.get('phone'):
        validator.validate_field(Validator.validate_phone_enhanced, 
                               data.get('phone'), 'رقم الهاتف')
    
    if data.get('email'):
        validator.validate_field(Validator.validate_email_enhanced, 
                               data.get('email'), 'البريد الإلكتروني')
    
    if data.get('national_id'):
        validator.validate_field(Validator.validate_national_id, 
                               data.get('national_id'), 'رقم الهوية')
    
    return validator.is_valid(), validator.get_errors()


def validate_department_data(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """التحقق من بيانات القسم"""
    validator = FormValidator()
    
    validator.validate_field(Validator.validate_code_format, 
                           data.get('code'), 'رمز القسم')
    validator.validate_field(Validator.validate_required, 
                           data.get('name'), 'اسم القسم')
    validator.validate_field(Validator.validate_string_length, 
                           data.get('name'), 'اسم القسم', 2, 100)
    
    return validator.is_valid(), validator.get_errors()


def validate_job_title_data(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """التحقق من بيانات العنوان الوظيفي"""
    validator = FormValidator()
    
    validator.validate_field(Validator.validate_code_format, 
                           data.get('code'), 'رمز الوظيفة')
    validator.validate_field(Validator.validate_required, 
                           data.get('title'), 'العنوان الوظيفي')
    validator.validate_field(Validator.validate_string_length, 
                           data.get('title'), 'العنوان الوظيفي', 2, 100)
    
    # التحقق من نطاق الراتب
    min_salary = data.get('min_salary')
    max_salary = data.get('max_salary')
    
    if min_salary is not None or max_salary is not None:
        validator.validate_field(Validator.validate_salary_range, 
                               min_salary, max_salary)
    
    return validator.is_valid(), validator.get_errors()


def validate_financial_transaction_data(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """التحقق من بيانات المعاملة المالية"""
    validator = FormValidator()
    
    validator.validate_field(Validator.validate_required, 
                           data.get('employee_id'), 'الموظف')
    validator.validate_field(Validator.validate_required, 
                           data.get('transaction_type'), 'نوع المعاملة')
    validator.validate_field(Validator.validate_positive_number, 
                           data.get('amount'), 'المبلغ')
    validator.validate_field(Validator.validate_required, 
                           data.get('transaction_date'), 'تاريخ المعاملة')
    
    # التحقق من المبلغ المدفوع
    amount = data.get('amount', 0)
    paid_amount = data.get('paid_amount', 0)
    
    if paid_amount is not None:
        validator.validate_field(Validator.validate_non_negative_number, 
                               paid_amount, 'المبلغ المدفوع')
        
        if amount and paid_amount > amount:
            validator.add_error("المبلغ المدفوع لا يمكن أن يكون أكبر من المبلغ الأصلي")
    
    return validator.is_valid(), validator.get_errors()
