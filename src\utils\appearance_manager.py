#!/usr/bin/env python3
"""
مدير المظهر المتقدم
Advanced Appearance Manager
"""

from PySide6.QtWidgets import QApplication
from PySide6.QtGui import QFont, QPalette, QColor
from PySide6.QtCore import QObject, Signal
from typing import Dict, Any
import json
from pathlib import Path


class AppearanceManager(QObject):
    """مدير المظهر المتقدم"""
    
    # إشارات التغيير
    font_changed = Signal(str, int)  # font_family, font_size
    theme_changed = Signal(str)      # theme_name
    colors_changed = Signal(dict)    # color_scheme
    
    def __init__(self):
        super().__init__()
        
        # الإعدادات الافتراضية
        self.default_settings = {
            'font_family': 'Arial',
            'font_size': 12,
            'theme_mode': 'light',  # light, dark, auto
            'color_scheme': 'blue',  # blue, green, purple, orange, red
            'custom_colors': {
                'primary': '#3498db',
                'secondary': '#2ecc71', 
                'background': '#ffffff',
                'surface': '#f8f9fa',
                'text_primary': '#2c3e50',
                'text_secondary': '#7f8c8d',
                'accent': '#e74c3c',
                'border': '#bdc3c7'
            }
        }
        
        # الإعدادات الحالية
        self.current_settings = self.default_settings.copy()
        
        # مخططات الألوان المحددة مسبقاً
        self.color_schemes = {
            'blue': {
                'primary': '#3498db',
                'secondary': '#2ecc71',
                'accent': '#e74c3c',
                'background': '#ffffff',
                'surface': '#f8f9fa',
                'text_primary': '#2c3e50',
                'text_secondary': '#7f8c8d',
                'border': '#bdc3c7'
            },
            'green': {
                'primary': '#27ae60',
                'secondary': '#3498db',
                'accent': '#f39c12',
                'background': '#ffffff',
                'surface': '#f8f9fa',
                'text_primary': '#2c3e50',
                'text_secondary': '#7f8c8d',
                'border': '#bdc3c7'
            },
            'purple': {
                'primary': '#9b59b6',
                'secondary': '#3498db',
                'accent': '#e67e22',
                'background': '#ffffff',
                'surface': '#f8f9fa',
                'text_primary': '#2c3e50',
                'text_secondary': '#7f8c8d',
                'border': '#bdc3c7'
            },
            'orange': {
                'primary': '#e67e22',
                'secondary': '#3498db',
                'accent': '#e74c3c',
                'background': '#ffffff',
                'surface': '#f8f9fa',
                'text_primary': '#2c3e50',
                'text_secondary': '#7f8c8d',
                'border': '#bdc3c7'
            },
            'red': {
                'primary': '#e74c3c',
                'secondary': '#3498db',
                'accent': '#f39c12',
                'background': '#ffffff',
                'surface': '#f8f9fa',
                'text_primary': '#2c3e50',
                'text_secondary': '#7f8c8d',
                'border': '#bdc3c7'
            }
        }
        
        # مخططات الوضع الليلي
        self.dark_color_schemes = {
            'blue': {
                'primary': '#3498db',
                'secondary': '#2ecc71',
                'accent': '#e74c3c',
                'background': '#2c3e50',
                'surface': '#34495e',
                'text_primary': '#ecf0f1',
                'text_secondary': '#bdc3c7',
                'border': '#7f8c8d'
            },
            'green': {
                'primary': '#27ae60',
                'secondary': '#3498db',
                'accent': '#f39c12',
                'background': '#2c3e50',
                'surface': '#34495e',
                'text_primary': '#ecf0f1',
                'text_secondary': '#bdc3c7',
                'border': '#7f8c8d'
            },
            'purple': {
                'primary': '#9b59b6',
                'secondary': '#3498db',
                'accent': '#e67e22',
                'background': '#2c3e50',
                'surface': '#34495e',
                'text_primary': '#ecf0f1',
                'text_secondary': '#bdc3c7',
                'border': '#7f8c8d'
            },
            'orange': {
                'primary': '#e67e22',
                'secondary': '#3498db',
                'accent': '#e74c3c',
                'background': '#2c3e50',
                'surface': '#34495e',
                'text_primary': '#ecf0f1',
                'text_secondary': '#bdc3c7',
                'border': '#7f8c8d'
            },
            'red': {
                'primary': '#e74c3c',
                'secondary': '#3498db',
                'accent': '#f39c12',
                'background': '#2c3e50',
                'surface': '#34495e',
                'text_primary': '#ecf0f1',
                'text_secondary': '#bdc3c7',
                'border': '#7f8c8d'
            }
        }
        
        # الخطوط المتاحة
        self.available_fonts = [
            'Arial',
            'Tahoma', 
            'Segoe UI',
            'Roboto',
            'Open Sans',
            'Lato',
            'Montserrat',
            'Source Sans Pro',
            'Ubuntu',
            'Nunito'
        ]
        
    def get_setting(self, key: str) -> Any:
        """الحصول على إعداد معين"""
        return self.current_settings.get(key, self.default_settings.get(key))
    
    def set_font_family(self, font_family: str):
        """تعيين نوع الخط"""
        if font_family in self.available_fonts:
            self.current_settings['font_family'] = font_family
            self.apply_font_settings()
            self.font_changed.emit(font_family, self.current_settings['font_size'])
    
    def set_font_size(self, font_size: int):
        """تعيين حجم الخط"""
        if 8 <= font_size <= 72:
            self.current_settings['font_size'] = font_size
            self.apply_font_settings()
            self.font_changed.emit(self.current_settings['font_family'], font_size)
    
    def set_theme_mode(self, theme_mode: str):
        """تعيين وضع المظهر"""
        if theme_mode in ['light', 'dark', 'auto']:
            self.current_settings['theme_mode'] = theme_mode
            self.apply_theme_settings()
            self.theme_changed.emit(theme_mode)
    
    def set_color_scheme(self, color_scheme: str):
        """تعيين مخطط الألوان"""
        if color_scheme in self.color_schemes:
            self.current_settings['color_scheme'] = color_scheme
            self.apply_color_settings()
            self.colors_changed.emit(self.get_current_colors())
    
    def set_custom_color(self, color_key: str, color_value: str):
        """تعيين لون مخصص"""
        if color_key in self.current_settings['custom_colors']:
            self.current_settings['custom_colors'][color_key] = color_value
            self.apply_color_settings()
            self.colors_changed.emit(self.get_current_colors())
    
    def get_current_colors(self) -> Dict[str, str]:
        """الحصول على الألوان الحالية"""
        theme_mode = self.current_settings['theme_mode']
        color_scheme = self.current_settings['color_scheme']
        
        if theme_mode == 'dark':
            return self.dark_color_schemes.get(color_scheme, self.dark_color_schemes['blue'])
        else:
            return self.color_schemes.get(color_scheme, self.color_schemes['blue'])
    
    def apply_font_settings(self):
        """تطبيق إعدادات الخط"""
        app = QApplication.instance()
        if not app:
            return
        
        font_family = self.current_settings['font_family']
        font_size = self.current_settings['font_size']
        
        # إنشاء الخط الجديد مع خصائص محسنة
        font = QFont(font_family, font_size)
        font.setStyleHint(QFont.SansSerif)
        font.setHintingPreference(QFont.PreferFullHinting)
        font.setWeight(QFont.Normal)
        
        # تطبيق الخط على التطبيق
        app.setFont(font)
        
        # تطبيق الخط على جميع العناصر بشكل تفصيلي
        for widget in app.allWidgets():
            if not widget or not hasattr(widget, 'setFont'):
                continue
                
            try:
                current_font = widget.font()
                new_font = QFont(font)
                
                # الحفاظ على وزن الخط الأصلي للعناصر الخاصة
                if hasattr(widget, 'objectName') and widget.objectName() in ['app_title', 'section_title']:
                    new_font.setWeight(QFont.Bold)
                    new_font.setPointSize(font_size + 4)  # جعل العناوين أكبر
                
                widget.setFont(new_font)
            except Exception as e:
                print(f"خطأ في تطبيق الخط على العنصر: {e}")
        
        # تطبيق أنماط CSS إضافية للتأكد من تطبيق الحجم
        # تطبيق أنماط CSS إضافية للتأكد من تطبيق الحجم
        style = f"""
        * {{
            font-size: {font_size}px !important;
        }}
        QLabel, QLineEdit, QTextEdit, QComboBox {{
            font-size: {font_size}px !important;
        }}
        QPushButton {{
            font-size: {font_size}px !important;
            padding: 8px 16px;
        }}
        QTableWidget, QTableView, QHeaderView::section {{
            font-size: {font_size}px !important;
        }}
        .title {{
            font-size: {font_size + 4}px !important;
            font-weight: bold;
        }}
        """
        current_style = app.styleSheet()
        app.setStyleSheet(current_style + style)
    
    def apply_theme_settings(self):
        """تطبيق إعدادات المظهر"""
        self.apply_color_settings()
    
    def apply_color_settings(self):
        """تطبيق إعدادات الألوان"""
        app = QApplication.instance()
        if not app:
            return
        
        colors = self.get_current_colors()
        font_family = self.current_settings['font_family']
        font_size = self.current_settings['font_size']
        
        # إنشاء CSS شامل
        css = self.generate_css(colors, font_family, font_size)
        
        # تطبيق CSS
        app.setStyleSheet(css)
        
        print(f"تم تطبيق مظهر {self.current_settings['theme_mode']} مع مخطط {self.current_settings['color_scheme']}")
    
    def generate_css(self, colors: Dict[str, str], font_family: str, font_size: int) -> str:
        """إنشاء CSS شامل للمظهر"""
        return f"""
        /* الأنماط العامة */
        QWidget {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
            background-color: {colors['background']};
            color: {colors['text_primary']};
        }}
        
        /* النوافذ الرئيسية */
        QMainWindow {{
            background-color: {colors['background']};
            color: {colors['text_primary']};
        }}
        
        /* الأزرار */
        QPushButton {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
            background-color: {colors['primary']};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
        }}
        
        QPushButton:hover {{
            background-color: {self._darken_color(colors['primary'], 10)};
        }}
        
        QPushButton:pressed {{
            background-color: {self._darken_color(colors['primary'], 20)};
        }}
        
        QPushButton:disabled {{
            background-color: {colors['border']};
            color: {colors['text_secondary']};
        }}
        
        /* أزرار الشريط الجانبي */
        QPushButton#sidebar_button {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 1px solid {colors['border']};
            text-align: left;
            padding: 12px 16px;
        }}
        
        QPushButton#sidebar_button:hover {{
            background-color: {colors['primary']};
            color: white;
        }}
        
        /* التسميات */
        QLabel {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
            color: {colors['text_primary']};
            background-color: transparent;
        }}
        
        /* العناوين الرئيسية */
        QLabel#app_title {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: 20px;
            font-weight: bold;
            color: {colors['primary']};
            margin: 10px;
        }}

        /* عناوين النوافذ */
        QLabel#window_title {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: 18px;
            font-weight: bold;
            color: {colors['primary']};
            margin: 8px;
        }}

        /* عناوين الأقسام */
        QLabel#section_title {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: 16px;
            font-weight: bold;
            color: {colors['text_primary']};
            margin: 6px;
        }}
        
        /* حقول الإدخال */
        QLineEdit {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 2px solid {colors['border']};
            border-radius: 6px;
            padding: 8px 12px;
        }}
        
        QLineEdit:focus {{
            border-color: {colors['primary']};
        }}
        
        QTextEdit {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 2px solid {colors['border']};
            border-radius: 6px;
            padding: 8px;
        }}
        
        QTextEdit:focus {{
            border-color: {colors['primary']};
        }}
        
        /* القوائم المنسدلة */
        QComboBox {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 2px solid {colors['border']};
            border-radius: 6px;
            padding: 8px 12px;
            min-width: 120px;
        }}
        
        QComboBox:focus {{
            border-color: {colors['primary']};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {colors['text_secondary']};
        }}
        
        QComboBox QAbstractItemView {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 1px solid {colors['border']};
            selection-background-color: {colors['primary']};
        }}

        /* الجداول */
        QTableWidget {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 1px solid {colors['border']};
            gridline-color: {colors['border']};
            selection-background-color: {colors['primary']};
            selection-color: white;
            alternate-background-color: {self._lighten_color(colors['surface'], 5)};
        }}

        QTableWidget::item {{
            padding: 8px;
            border-bottom: 1px solid {colors['border']};
        }}

        QTableWidget::item:selected {{
            background-color: {colors['primary']};
            color: white;
        }}

        QHeaderView::section {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size + 2}px;
            font-weight: bold;
            background-color: {colors['primary']};
            color: white;
            padding: 12px;
            border: none;
            border-right: 1px solid {self._darken_color(colors['primary'], 10)};
        }}

        /* التبويبات */
        QTabWidget {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
        }}

        QTabWidget::pane {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 6px;
        }}

        QTabBar::tab {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size + 1}px;
            font-weight: 600;
            background-color: {colors['background']};
            color: {colors['text_primary']};
            padding: 12px 24px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            border: 1px solid {colors['border']};
            border-bottom: none;
        }}

        QTabBar::tab:selected {{
            background-color: {colors['primary']};
            color: white;
        }}

        QTabBar::tab:hover {{
            background-color: {self._lighten_color(colors['primary'], 20)};
        }}

        /* المجموعات */
        QGroupBox {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
            font-weight: bold;
            color: {colors['text_primary']};
            border: 2px solid {colors['border']};
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }}

        QGroupBox::title {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: 16px;
            font-weight: bold;
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            color: {colors['primary']};
            background-color: {colors['background']};
        }}

        /* مربعات الاختيار */
        QCheckBox {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
            color: {colors['text_primary']};
            spacing: 8px;
        }}

        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid {colors['border']};
            border-radius: 3px;
            background-color: {colors['surface']};
        }}

        QCheckBox::indicator:checked {{
            background-color: {colors['primary']};
            border-color: {colors['primary']};
        }}

        QCheckBox::indicator:checked {{
            image: none;
        }}

        /* أزرار الراديو */
        QRadioButton {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
            color: {colors['text_primary']};
            spacing: 8px;
        }}

        QRadioButton::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid {colors['border']};
            border-radius: 9px;
            background-color: {colors['surface']};
        }}

        QRadioButton::indicator:checked {{
            background-color: {colors['primary']};
            border-color: {colors['primary']};
        }}

        /* المنزلقات */
        QSlider::groove:horizontal {{
            height: 6px;
            background-color: {colors['border']};
            border-radius: 3px;
        }}

        QSlider::handle:horizontal {{
            background-color: {colors['primary']};
            border: 2px solid white;
            width: 20px;
            height: 20px;
            border-radius: 10px;
            margin: -7px 0;
        }}

        QSlider::handle:horizontal:hover {{
            background-color: {self._darken_color(colors['primary'], 10)};
        }}

        /* شريط التمرير */
        QScrollBar:vertical {{
            background-color: {colors['surface']};
            width: 12px;
            border-radius: 6px;
        }}

        QScrollBar::handle:vertical {{
            background-color: {colors['border']};
            border-radius: 6px;
            min-height: 20px;
        }}

        QScrollBar::handle:vertical:hover {{
            background-color: {colors['text_secondary']};
        }}

        /* القوائم */
        QListWidget {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 1px solid {colors['border']};
            border-radius: 6px;
        }}

        QListWidget::item {{
            padding: 8px;
            border-bottom: 1px solid {colors['border']};
        }}

        QListWidget::item:selected {{
            background-color: {colors['primary']};
            color: white;
        }}

        /* شريط الحالة */
        QStatusBar {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
            background-color: {colors['surface']};
            color: {colors['text_secondary']};
            border-top: 1px solid {colors['border']};
        }}

        /* مربعات الحوار */
        QDialog {{
            background-color: {colors['background']};
            color: {colors['text_primary']};
        }}

        /* شريط القوائم */
        QMenuBar {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border-bottom: 1px solid {colors['border']};
        }}

        QMenuBar::item {{
            padding: 8px 12px;
            background-color: transparent;
        }}

        QMenuBar::item:selected {{
            background-color: {colors['primary']};
            color: white;
        }}

        QMenu {{
            font-family: "{font_family}", "Tahoma", sans-serif;
            font-size: {font_size}px;
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 1px solid {colors['border']};
        }}

        QMenu::item {{
            padding: 8px 20px;
        }}

        QMenu::item:selected {{
            background-color: {colors['primary']};
            color: white;
        }}
        """
    
    def _darken_color(self, color: str, percent: int) -> str:
        """تغميق لون بنسبة معينة"""
        try:
            # إزالة # إذا كانت موجودة
            color = color.lstrip('#')

            # تحويل إلى RGB
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)

            # تطبيق التغميق
            factor = (100 - percent) / 100
            r = int(r * factor)
            g = int(g * factor)
            b = int(b * factor)

            # تحويل إلى hex
            return f"#{r:02x}{g:02x}{b:02x}"
        except:
            return color

    def _lighten_color(self, color: str, percent: int) -> str:
        """تفتيح لون بنسبة معينة"""
        try:
            # إزالة # إذا كانت موجودة
            color = color.lstrip('#')

            # تحويل إلى RGB
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)

            # تطبيق التفتيح
            factor = percent / 100
            r = int(r + (255 - r) * factor)
            g = int(g + (255 - g) * factor)
            b = int(b + (255 - b) * factor)

            # التأكد من عدم تجاوز 255
            r = min(255, r)
            g = min(255, g)
            b = min(255, b)

            # تحويل إلى hex
            return f"#{r:02x}{g:02x}{b:02x}"
        except:
            return color
    
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            from ..config import get_config
            appearance_config = get_config("appearance")
            
            for key, value in appearance_config.items():
                if key in self.current_settings:
                    self.current_settings[key] = value
            
            # تطبيق الإعدادات المحملة
            self.apply_font_settings()
            self.apply_color_settings()
            
            return True
        except Exception as e:
            print(f"خطأ في تحميل إعدادات المظهر: {e}")
            return False
    
    def save_settings(self):
        """حفظ الإعدادات في الملف"""
        try:
            from ..config import update_config
            
            for key, value in self.current_settings.items():
                update_config("appearance", key, value)
            
            print("تم حفظ إعدادات المظهر")
            return True
        except Exception as e:
            print(f"خطأ في حفظ إعدادات المظهر: {e}")
            return False
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات للافتراضية"""
        self.current_settings = self.default_settings.copy()
        self.apply_font_settings()
        self.apply_color_settings()
        
        # إرسال الإشارات
        self.font_changed.emit(
            self.current_settings['font_family'],
            self.current_settings['font_size']
        )
        self.theme_changed.emit(self.current_settings['theme_mode'])
        self.colors_changed.emit(self.get_current_colors())


# إنشاء مثيل عام لمدير المظهر
appearance_manager = AppearanceManager()


def get_appearance_manager() -> AppearanceManager:
    """الحصول على مدير المظهر"""
    return appearance_manager
