"""
أدوات مساعدة للواجهة
UI Helper Functions
"""

from typing import Optional, List, Any
from PySide6.QtWidgets import (
    QWidget, QMessageBox, QApplication, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QComboBox, QLineEdit, QDateEdit, QPushButton, QLabel
)
from PySide6.QtCore import Qt, QDate, QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QFont, QIcon, QPixmap


def show_message(
    parent: QWidget,
    title: str,
    message: str,
    message_type: str = "information"
) -> None:
    """عرض رسالة للمستخدم"""
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    
    if message_type == "information":
        msg_box.setIcon(QMessageBox.Information)
    elif message_type == "warning":
        msg_box.setIcon(QMessageBox.Warning)
    elif message_type == "error":
        msg_box.setIcon(QMessageBox.Critical)
    elif message_type == "question":
        msg_box.setIcon(QMessageBox.Question)
    
    msg_box.exec()


def show_confirmation(
    parent: QWidget,
    title: str,
    message: str
) -> bool:
    """عرض رسالة تأكيد"""
    reply = QMessageBox.question(
        parent,
        title,
        message,
        QMessageBox.Yes | QMessageBox.No,
        QMessageBox.No
    )
    return reply == QMessageBox.Yes


def setup_table_widget(
    table: QTableWidget,
    headers: List[str],
    column_widths: Optional[List[int]] = None,
    sortable: bool = True,
    selection_behavior: str = "row"
) -> None:
    """إعداد جدول البيانات"""
    # تعيين العناوين
    table.setColumnCount(len(headers))
    table.setHorizontalHeaderLabels(headers)
    
    # إعداد العرض
    if column_widths:
        for i, width in enumerate(column_widths):
            if i < len(headers):
                table.setColumnWidth(i, width)
    else:
        # توزيع متساوي
        table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
    
    # إعداد الترتيب
    if sortable:
        table.setSortingEnabled(True)
    
    # إعداد التحديد
    if selection_behavior == "row":
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
    elif selection_behavior == "cell":
        table.setSelectionBehavior(QAbstractItemView.SelectItems)
    
    # إعدادات أخرى
    table.setAlternatingRowColors(True)
    table.setSelectionMode(QAbstractItemView.SingleSelection)
    table.verticalHeader().setVisible(False)


def populate_table(
    table: QTableWidget,
    data: List[List[Any]],
    editable: bool = False
) -> None:
    """ملء الجدول بالبيانات"""
    table.setRowCount(len(data))
    
    for row_idx, row_data in enumerate(data):
        for col_idx, cell_data in enumerate(row_data):
            if col_idx < table.columnCount():
                item = QTableWidgetItem(str(cell_data) if cell_data is not None else "")
                
                if not editable:
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                
                table.setItem(row_idx, col_idx, item)


def setup_combobox(
    combo: QComboBox,
    items: List[tuple],  # (display_text, value)
    default_value: Any = None
) -> None:
    """إعداد القائمة المنسدلة"""
    combo.clear()
    
    for display_text, value in items:
        combo.addItem(display_text, value)
    
    if default_value is not None:
        index = combo.findData(default_value)
        if index >= 0:
            combo.setCurrentIndex(index)


def get_combobox_value(combo: QComboBox) -> Any:
    """الحصول على قيمة القائمة المنسدلة"""
    return combo.currentData()


def set_combobox_value(combo: QComboBox, value: Any) -> bool:
    """تعيين قيمة القائمة المنسدلة"""
    index = combo.findData(value)
    if index >= 0:
        combo.setCurrentIndex(index)
        return True
    return False


def setup_date_edit(
    date_edit: QDateEdit,
    default_date: Optional[QDate] = None,
    min_date: Optional[QDate] = None,
    max_date: Optional[QDate] = None
) -> None:
    """إعداد حقل التاريخ"""
    if default_date:
        date_edit.setDate(default_date)
    else:
        date_edit.setDate(QDate.currentDate())
    
    if min_date:
        date_edit.setMinimumDate(min_date)
    
    if max_date:
        date_edit.setMaximumDate(max_date)
    
    date_edit.setCalendarPopup(True)


def clear_form(widget: QWidget) -> None:
    """مسح جميع حقول النموذج"""
    for child in widget.findChildren(QLineEdit):
        child.clear()
    
    for child in widget.findChildren(QComboBox):
        child.setCurrentIndex(0)
    
    for child in widget.findChildren(QDateEdit):
        child.setDate(QDate.currentDate())


def validate_form(widget: QWidget, required_fields: List[str]) -> tuple[bool, str]:
    """التحقق من صحة النموذج"""
    for field_name in required_fields:
        field = widget.findChild(QWidget, field_name)
        
        if isinstance(field, QLineEdit):
            if not field.text().strip():
                return False, f"الحقل '{field.objectName()}' مطلوب"
        
        elif isinstance(field, QComboBox):
            if field.currentIndex() == -1:
                return False, f"يجب اختيار قيمة من '{field.objectName()}'"
    
    return True, ""


def set_widget_style(widget: QWidget, style_class: str) -> None:
    """تطبيق ستايل على العنصر"""
    current_class = widget.property("class") or ""
    if style_class not in current_class:
        new_class = f"{current_class} {style_class}".strip()
        widget.setProperty("class", new_class)
        widget.style().unpolish(widget)
        widget.style().polish(widget)


def create_fade_animation(widget: QWidget, duration: int = 300) -> QPropertyAnimation:
    """إنشاء تأثير الظهور التدريجي"""
    animation = QPropertyAnimation(widget, b"windowOpacity")
    animation.setDuration(duration)
    animation.setStartValue(0.0)
    animation.setEndValue(1.0)
    animation.setEasingCurve(QEasingCurve.InOutQuad)
    return animation


def show_notification(
    parent: QWidget,
    message: str,
    duration: int = 3000,
    notification_type: str = "info"
) -> None:
    """عرض إشعار مؤقت"""
    # سيتم تطوير هذه الدالة لاحقاً مع نظام الإشعارات
    pass


def center_widget(widget: QWidget, parent: Optional[QWidget] = None) -> None:
    """توسيط العنصر"""
    if parent is None:
        # توسيط في الشاشة
        screen = QApplication.primaryScreen().geometry()
        widget.move(
            (screen.width() - widget.width()) // 2,
            (screen.height() - widget.height()) // 2
        )
    else:
        # توسيط في العنصر الأب
        parent_rect = parent.geometry()
        widget.move(
            parent_rect.x() + (parent_rect.width() - widget.width()) // 2,
            parent_rect.y() + (parent_rect.height() - widget.height()) // 2
        )


def apply_rtl_layout(widget: QWidget) -> None:
    """تطبيق التخطيط المحسن من اليمين إلى اليسار مع دعم العربية"""
    widget.setLayoutDirection(Qt.RightToLeft)

    # تطبيق على جميع العناصر الفرعية
    for child in widget.findChildren(QWidget):
        child.setLayoutDirection(Qt.RightToLeft)

        # تحسين محاذاة النصوص العربية
        if hasattr(child, 'setAlignment'):
            try:
                # للتسميات والنصوص - محاذاة يمين
                if child.__class__.__name__ in ['QLabel', 'QLineEdit', 'QTextEdit', 'QPlainTextEdit']:
                    child.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            except:
                pass

        # تحسين الخطوط للنصوص العربية
        if hasattr(child, 'setFont'):
            try:
                font = child.font()
                # تحسين عرض الخط العربي
                font.setStyleHint(QFont.System)
                font.setHintingPreference(QFont.PreferFullHinting)
                child.setFont(font)
            except:
                pass


def set_font_size(widget: QWidget, size: int) -> None:
    """تعيين حجم الخط"""
    font = widget.font()
    font.setPointSize(size)
    widget.setFont(font)


def make_bold(widget: QWidget) -> None:
    """جعل النص عريض"""
    font = widget.font()
    font.setBold(True)
    widget.setFont(font)


def setup_arabic_font(widget: QWidget, size: int = 10, bold: bool = False) -> None:
    """إعداد خط عربي محسن"""
    from PySide6.QtGui import QFont

    # قائمة الخطوط العربية المفضلة
    arabic_fonts = [
        "Segoe UI",
        "Tahoma",
        "Arial Unicode MS",
        "Microsoft Sans Serif",
        "Arial"
    ]

    font = QFont()

    # محاولة استخدام أفضل خط متوفر
    for font_name in arabic_fonts:
        font.setFamily(font_name)
        if QFont(font_name).exactMatch():
            break

    font.setPointSize(size)
    font.setBold(bold)
    font.setStyleHint(QFont.System)
    font.setHintingPreference(QFont.PreferFullHinting)

    widget.setFont(font)


def center_text_arabic(widget: QWidget) -> None:
    """توسيط النص العربي"""
    if hasattr(widget, 'setAlignment'):
        widget.setAlignment(Qt.AlignCenter)


def align_text_right_arabic(widget: QWidget) -> None:
    """محاذاة النص العربي لليمين"""
    if hasattr(widget, 'setAlignment'):
        widget.setAlignment(Qt.AlignRight | Qt.AlignVCenter)


def setup_table_rtl(table: QTableWidget) -> None:
    """إعداد الجدول للعربية من اليمين لليسار"""
    table.setLayoutDirection(Qt.RightToLeft)

    # إعداد رأس الجدول
    header = table.horizontalHeader()
    header.setLayoutDirection(Qt.RightToLeft)
    header.setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter)

    # إعداد الخطوط
    setup_arabic_font(table, 9)
    setup_arabic_font(header, 9, bold=True)


def setup_form_rtl(form_widget: QWidget) -> None:
    """إعداد النموذج للعربية مع تحسينات خاصة"""
    apply_rtl_layout(form_widget)

    # البحث عن جميع التسميات والحقول
    labels = form_widget.findChildren(QLabel)
    line_edits = form_widget.findChildren(QLineEdit)
    combo_boxes = form_widget.findChildren(QComboBox)

    # تحسين التسميات
    for label in labels:
        align_text_right_arabic(label)
        setup_arabic_font(label, 9)

    # تحسين حقول الإدخال
    for line_edit in line_edits:
        align_text_right_arabic(line_edit)
        setup_arabic_font(line_edit, 9)

    # تحسين القوائم المنسدلة
    for combo in combo_boxes:
        setup_arabic_font(combo, 9)


def setup_button_arabic(button: QPushButton, size: int = 10) -> None:
    """إعداد الزر للعربية"""
    setup_arabic_font(button, size, bold=True)
    button.setLayoutDirection(Qt.RightToLeft)

    # تحسين الحشو للنصوص العربية
    button.setStyleSheet(f"""
        QPushButton {{
            padding: 8px 16px;
            text-align: center;
        }}
    """)


def improve_arabic_display(widget: QWidget) -> None:
    """تحسين عرض العربية بشكل شامل"""
    apply_rtl_layout(widget)

    # تطبيق تحسينات على أنواع مختلفة من العناصر
    for child in widget.findChildren(QWidget):
        class_name = child.__class__.__name__

        if class_name == 'QLabel':
            align_text_right_arabic(child)
            setup_arabic_font(child, 9)

        elif class_name in ['QLineEdit', 'QTextEdit', 'QPlainTextEdit']:
            align_text_right_arabic(child)
            setup_arabic_font(child, 9)

        elif class_name == 'QPushButton':
            setup_button_arabic(child, 9)

        elif class_name == 'QTableWidget':
            setup_table_rtl(child)

        elif class_name == 'QComboBox':
            setup_arabic_font(child, 9)
            child.setLayoutDirection(Qt.RightToLeft)
