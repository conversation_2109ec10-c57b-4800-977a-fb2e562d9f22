#!/usr/bin/env python3
"""
اختبار الشريط العلوي النظيف مع الأيقونة الجديدة
Test Clean Header with New Icon
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QFrame, QHBoxLayout, QPushButton
from PySide6.QtCore import Qt, QSize


def create_clean_header_test():
    """إنشاء نافذة اختبار الشريط العلوي النظيف"""
    
    class CleanHeaderTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار الشريط العلوي النظيف مع الأيقونة الجديدة")
            self.setGeometry(100, 100, 1200, 800)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            # إنشاء الشريط العلوي النظيف
            self.create_clean_header()
            main_layout.addWidget(self.header)
            
            # محتوى الاختبار
            content_widget = QWidget()
            content_layout = QVBoxLayout(content_widget)
            content_layout.setContentsMargins(30, 30, 30, 30)
            content_layout.setSpacing(25)
            
            # عنوان الاختبار
            test_title = QLabel("🧹 اختبار الشريط العلوي النظيف والواضح")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 26px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 20px;
                    padding: 20px;
                    background-color: #3498db;
                    color: white;
                    border-radius: 12px;
                }
            """)
            content_layout.addWidget(test_title)
            
            # معلومات التحسينات النظيفة
            improvements_info = QLabel("""
            ✨ التصميم النظيف الجديد:
            
            🎯 المبادئ المطبقة:
            • البساطة والوضوح في التصميم
            • نصوص واضحة بدون تعقيد
            • ألوان هادئة ومريحة للعين
            • تباعد مناسب بين العناصر
            • إزالة التأثيرات المشتتة
            
            📝 العناوين المحسنة:
            • العنوان الرئيسي: "نظام إدارة الموارد البشرية" (28px)
            • العنوان الفرعي: "إدارة شاملة للموظفين والمعاملات المالية" (16px)
            • تباعد أحرف مناسب (1px)
            • بدون ظلال أو تأثيرات مشتتة
            
            👤 بطاقة المستخدم النظيفة:
            • تصميم بسيط وأنيق (220×55px)
            • خلفية فاتحة (#f8f9fa)
            • حدود رمادية ناعمة
            • أيقونة مستخدم بسيطة مع خلفية زرقاء
            • نصوص واضحة: "مرحباً بك" (14px) + "المدير العام" (16px)
            
            ⚙️ زر الإعدادات الجديد:
            • أيقونة SVG مخصصة بدلاً من الرمز النصي
            • تصميم دائري أنيق (55×55px)
            • خلفية بيضاء مع حدود رمادية
            • تأثيرات hover بسيطة وناعمة
            • أيقونة ترس بنفسجية جميلة
            
            🎨 نظام الألوان النظيف:
            • الشريط العلوي: أبيض نظيف
            • الحدود السفلية: أزرق بسيط (#3498db)
            • بطاقة المستخدم: رمادي فاتح (#f8f9fa)
            • أيقونة المستخدم: أزرق (#3498db)
            • زر الإعدادات: أبيض مع حدود رمادية
            
            📏 الأبعاد المحسنة:
            • ارتفاع الشريط: 85px (مناسب ومريح)
            • عرض بطاقة المستخدم: 220px
            • ارتفاع بطاقة المستخدم: 55px
            • حجم زر الإعدادات: 55×55px
            • حجم أيقونة المستخدم: 35×35px
            """)
            improvements_info.setAlignment(Qt.AlignLeft)
            improvements_info.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #2c3e50;
                    background-color: #f8f9fa;
                    padding: 25px;
                    border-radius: 12px;
                    border-left: 4px solid #3498db;
                    line-height: 1.6;
                    border: 1px solid #dee2e6;
                }
            """)
            content_layout.addWidget(improvements_info)
            
            # مقارنة قبل وبعد
            comparison_frame = QFrame()
            comparison_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 2px solid #dee2e6;
                    border-radius: 12px;
                    padding: 20px;
                }
            """)
            
            comparison_layout = QVBoxLayout(comparison_frame)
            
            comparison_title = QLabel("📊 مقارنة التصميم: قبل وبعد")
            comparison_title.setAlignment(Qt.AlignCenter)
            comparison_title.setStyleSheet("""
                QLabel {
                    font-size: 20px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 15px;
                    padding: 12px;
                    background-color: #ecf0f1;
                    border-radius: 8px;
                }
            """)
            comparison_layout.addWidget(comparison_title)
            
            comparison_text = QLabel("""
            ❌ التصميم السابق (مشابك ومعقد):
            • نصوص كبيرة جداً (32px للعنوان الرئيسي)
            • تدرجات لونية معقدة ومشتتة
            • ظلال وتأثيرات كثيرة
            • أيقونات نصية غير واضحة (⚙️)
            • ألوان متعددة ومربكة
            • تباعد أحرف مفرط (3px)
            • خلفيات متدرجة معقدة
            
            ✅ التصميم الجديد (نظيف وواضح):
            • نصوص مناسبة وواضحة (28px للعنوان الرئيسي)
            • ألوان بسيطة وهادئة
            • بدون ظلال أو تأثيرات مشتتة
            • أيقونة SVG احترافية للإعدادات
            • نظام ألوان متناسق ونظيف
            • تباعد أحرف مناسب (1px)
            • خلفيات بسيطة وأنيقة
            
            🎯 النتيجة: تحسن كبير في الوضوح والقراءة مع تصميم أنظف وأكثر احترافية!
            """)
            comparison_text.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #34495e;
                    line-height: 1.6;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                }
            """)
            comparison_layout.addWidget(comparison_text)
            
            content_layout.addWidget(comparison_frame)
            content_layout.addStretch()
            
            main_layout.addWidget(content_widget)
            
        def create_clean_header(self):
            """إنشاء الشريط العلوي النظيف"""
            self.header = QFrame()
            self.header.setObjectName("clean_header")
            self.header.setFixedHeight(85)

            header_layout = QHBoxLayout(self.header)
            header_layout.setContentsMargins(30, 15, 30, 15)
            header_layout.setSpacing(25)

            # قسم العنوان النظيف
            title_section = QVBoxLayout()
            title_section.setSpacing(5)

            # العنوان الرئيسي النظيف
            main_title = QLabel("نظام إدارة الموارد البشرية")
            main_title.setObjectName("clean_main_title")
            main_title.setAlignment(Qt.AlignLeft)

            # العنوان الفرعي النظيف
            subtitle = QLabel("إدارة شاملة للموظفين والمعاملات المالية")
            subtitle.setObjectName("clean_subtitle")
            subtitle.setAlignment(Qt.AlignLeft)

            title_section.addWidget(main_title)
            title_section.addWidget(subtitle)

            # قسم معلومات المستخدم النظيف
            user_section = QHBoxLayout()
            user_section.setSpacing(15)

            # بطاقة المستخدم نظيفة
            user_card = QFrame()
            user_card.setObjectName("clean_user_card")
            user_card.setFixedSize(220, 55)
            
            user_card_layout = QHBoxLayout(user_card)
            user_card_layout.setContentsMargins(15, 8, 15, 8)
            user_card_layout.setSpacing(12)

            # أيقونة المستخدم نظيفة
            user_icon = QLabel("👤")
            user_icon.setObjectName("clean_user_icon")
            user_icon.setFixedSize(35, 35)
            user_icon.setAlignment(Qt.AlignCenter)

            # معلومات المستخدم نظيفة
            user_info_layout = QVBoxLayout()
            user_info_layout.setSpacing(2)
            
            welcome_label = QLabel("مرحباً بك")
            welcome_label.setObjectName("clean_welcome_label")
            
            user_name = QLabel("المدير العام")
            user_name.setObjectName("clean_user_name")

            user_info_layout.addWidget(welcome_label)
            user_info_layout.addWidget(user_name)

            user_card_layout.addWidget(user_icon)
            user_card_layout.addLayout(user_info_layout)
            user_card_layout.addStretch()

            # زر الإعدادات مع الأيقونة الجديدة
            from src.utils.icons import create_settings_icon
            
            settings_btn = QPushButton()
            settings_btn.setObjectName("clean_settings_btn")
            settings_btn.setFixedSize(55, 55)
            settings_btn.setIcon(create_settings_icon(QSize(40, 40)))
            settings_btn.setIconSize(QSize(40, 40))
            settings_btn.setToolTip("إعدادات النظام")
            settings_btn.clicked.connect(lambda: print("🔄 تم النقر على زر الإعدادات النظيف"))

            user_section.addWidget(user_card)
            user_section.addWidget(settings_btn)

            # تجميع الأقسام
            header_layout.addLayout(title_section)
            header_layout.addStretch()
            header_layout.addLayout(user_section)
    
    return CleanHeaderTest()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الشريط العلوي النظيف مع الأيقونة الجديدة")
    print("=" * 70)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            enhanced_style_file = styles_dir / "enhanced_dashboard.qss"
            
            combined_styles = ""
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read() + "\n"
                    
            if enhanced_style_file.exists():
                with open(enhanced_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read()
                    
            app.setStyleSheet(combined_styles)
            print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_clean_header_test()
        window.show()
        
        print("✅ تم فتح نافذة اختبار التصميم النظيف!")
        print("\n🧹 التحسينات النظيفة:")
        print("• نصوص واضحة بدون تعقيد")
        print("• ألوان بسيطة وهادئة")
        print("• أيقونة SVG احترافية للإعدادات")
        print("• تصميم نظيف ومريح للعين")
        
        print("\n📏 الأبعاد الجديدة:")
        print("• ارتفاع الشريط: 85px")
        print("• العنوان الرئيسي: 28px")
        print("• العنوان الفرعي: 16px")
        print("• بطاقة المستخدم: 220×55px")
        print("• زر الإعدادات: 55×55px")
        
        print("\n🎨 نظام الألوان:")
        print("• الشريط العلوي: أبيض نظيف")
        print("• الحدود: أزرق بسيط")
        print("• بطاقة المستخدم: رمادي فاتح")
        print("• زر الإعدادات: أبيض مع حدود")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
