#!/usr/bin/env python3
"""
إنشاء معاملات مالية تجريبية
Create Sample Financial Transactions
"""

import sys
from pathlib import Path
from datetime import date, timedelta
import random

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.database import db_manager, get_db_session_context
from src.models import Employee, FinancialTransaction, TransactionType


def create_sample_transactions():
    """إنشاء معاملات مالية تجريبية"""
    print("💰 إنشاء معاملات مالية تجريبية...")
    print("=" * 50)
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        with get_db_session_context() as session:
            # الحصول على جميع الموظفين
            employees = session.query(Employee).filter_by(is_active=True).all()
            
            if not employees:
                print("❌ لا يوجد موظفين في النظام")
                return False
            
            print(f"👥 عدد الموظفين: {len(employees)}")
            
            # إنشاء معاملات مالية متنوعة
            transactions_created = 0
            
            for employee in employees:
                print(f"\n👤 إنشاء معاملات للموظف: {employee.full_name}")
                
                # تواريخ مختلفة في الشهر الحالي
                current_date = date.today()
                month_start = date(current_date.year, current_date.month, 1)
                
                # إنشاء سلفة
                advance_amount = random.randint(50000, 200000)
                advance_date = month_start + timedelta(days=random.randint(1, 10))
                
                advance = FinancialTransaction(
                    employee_id=employee.id,
                    transaction_type=TransactionType.ADVANCE,
                    amount=advance_amount,
                    description=f"سلفة شهر {current_date.month}",
                    transaction_date=advance_date
                )
                session.add(advance)
                transactions_created += 1
                print(f"   💸 سلفة: {advance_amount:,} د.ع")
                
                # إنشاء مكافأة (أحياناً)
                if random.choice([True, False]):
                    bonus_amount = random.randint(25000, 100000)
                    bonus_date = month_start + timedelta(days=random.randint(15, 25))
                    
                    bonus = FinancialTransaction(
                        employee_id=employee.id,
                        transaction_type=TransactionType.BONUS,
                        amount=bonus_amount,
                        description="مكافأة أداء",
                        transaction_date=bonus_date
                    )
                    session.add(bonus)
                    transactions_created += 1
                    print(f"   🎁 مكافأة: {bonus_amount:,} د.ع")
                
                # إنشاء مكافأة إضافية (أحياناً)
                if random.choice([True, False]):
                    extra_bonus_amount = random.randint(15000, 75000)
                    extra_bonus_date = month_start + timedelta(days=random.randint(20, 28))

                    extra_bonus = FinancialTransaction(
                        employee_id=employee.id,
                        transaction_type=TransactionType.BONUS,
                        amount=extra_bonus_amount,
                        description="مكافأة إضافية",
                        transaction_date=extra_bonus_date
                    )
                    session.add(extra_bonus)
                    transactions_created += 1
                    print(f"   ⚡ مكافأة إضافية: {extra_bonus_amount:,} د.ع")
                
                # إنشاء خصم (أحياناً)
                if random.choice([True, False, False]):  # احتمال أقل للخصم
                    deduction_amount = random.randint(10000, 50000)
                    deduction_date = month_start + timedelta(days=random.randint(5, 15))
                    
                    deduction = FinancialTransaction(
                        employee_id=employee.id,
                        transaction_type=TransactionType.DEDUCTION,
                        amount=deduction_amount,
                        description="خصم تأخير",
                        transaction_date=deduction_date
                    )
                    session.add(deduction)
                    transactions_created += 1
                    print(f"   📉 خصم: {deduction_amount:,} د.ع")
                
                # إنشاء دين ماركت (أحياناً)
                if random.choice([True, False, False]):  # احتمال أقل للدين
                    debt_amount = random.randint(20000, 80000)
                    debt_date = month_start + timedelta(days=random.randint(10, 20))

                    debt = FinancialTransaction(
                        employee_id=employee.id,
                        transaction_type=TransactionType.MARKET_DEBT,
                        amount=debt_amount,
                        description="دين ماركت",
                        transaction_date=debt_date
                    )
                    session.add(debt)
                    transactions_created += 1
                    print(f"   🏪 دين ماركت: {debt_amount:,} د.ع")
            
            # حفظ التغييرات
            session.commit()
            
            print(f"\n✅ تم إنشاء {transactions_created} معاملة مالية بنجاح!")
            
            # عرض ملخص المعاملات
            print("\n📊 ملخص المعاملات:")
            print("-" * 30)
            
            for employee in employees:
                print(f"\n👤 {employee.full_name}:")
                
                # حساب المعاملات للشهر الحالي
                advances = employee.get_total_advances(current_date.month, current_date.year)
                bonuses = employee.get_total_bonuses(current_date.month, current_date.year)
                deductions = employee.get_total_deductions(current_date.month, current_date.year)
                debts = employee.get_total_debts(current_date.month, current_date.year)
                
                print(f"   💸 إجمالي السلف: {advances:,.0f} د.ع")
                print(f"   🎁 إجمالي المكافآت والحوافز: {bonuses:,.0f} د.ع")
                print(f"   📉 إجمالي الخصومات: {deductions:,.0f} د.ع")
                print(f"   🏪 إجمالي الديون: {debts:,.0f} د.ع")
                
                # حساب الراتب المتوقع
                basic_salary = float(employee.basic_salary)
                expected_net = basic_salary + bonuses - advances - deductions - debts
                print(f"   💰 الراتب الأساسي: {basic_salary:,.0f} د.ع")
                print(f"   💵 صافي الراتب المتوقع: {expected_net:,.0f} د.ع")
            
            return True
            
    except Exception as e:
        print(f"\n❌ خطأ في إنشاء المعاملات المالية: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """الدالة الرئيسية"""
    success = create_sample_transactions()
    
    if success:
        print("\n🎉 تم إنشاء المعاملات المالية التجريبية بنجاح!")
        print("\nيمكنك الآن:")
        print("1. تشغيل النظام: python run_hr_system.py")
        print("2. الذهاب إلى قسم الرواتب")
        print("3. حساب الرواتب للشهر الحالي")
        print("4. تعديل تفاصيل الراتب (عدد أيام الدوام، المعاملات المالية)")
        print("5. صرف الرواتب")
        return 0
    else:
        print("💥 فشل في إنشاء المعاملات المالية!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
