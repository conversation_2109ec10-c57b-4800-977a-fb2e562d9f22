# مرجع API - نظام إدارة شؤون الموظفين

## مقدمة

هذا المرجع يوثق جميع الكلاسات والدوال والواجهات البرمجية في نظام إدارة شؤون الموظفين.

## النماذج (Models)

### BaseModel

```python
class BaseModel:
    """النموذج الأساسي لجميع الجداول"""
    
    id: int                    # المعرف الفريد
    created_at: datetime       # تاريخ الإنشاء
    updated_at: datetime       # تاريخ آخر تحديث
    is_active: bool           # حالة النشاط
```

### Employee

```python
class Employee(BaseModel):
    """نموذج الموظف"""
    
    # الحقول الأساسية
    employee_number: str       # الرقم الوظيفي
    full_name: str            # الاسم الكامل
    national_id: str          # رقم الهوية
    phone: str                # رقم الهاتف
    email: str                # البريد الإلكتروني
    hire_date: date           # تاريخ المباشرة
    basic_salary: Decimal     # الراتب الأساسي
    employment_status: EmploymentStatus  # حالة التوظيف
    
    # العلاقات
    department_id: int        # معرف القسم
    job_title_id: int         # معرف العنوان الوظيفي
    department: Department    # القسم
    job_title: JobTitle       # العنوان الوظيفي
    
    # الطرق
    def get_current_balance(self, session) -> Decimal:
        """حساب الرصيد الحالي للموظف"""
        
    def get_salary_history(self, session) -> List[SalaryRecord]:
        """الحصول على تاريخ الرواتب"""
        
    def get_financial_transactions(self, session) -> List[FinancialTransaction]:
        """الحصول على المعاملات المالية"""
```

### Department

```python
class Department(BaseModel):
    """نموذج القسم"""
    
    name: str                 # اسم القسم
    description: str          # وصف القسم
    manager_name: str         # اسم المدير
    
    # العلاقات
    employees: List[Employee] # قائمة الموظفين
    
    # الطرق
    def get_employees_count(self) -> int:
        """عدد الموظفين في القسم"""
        
    def get_total_salaries(self) -> Decimal:
        """إجمالي رواتب القسم"""
```

### JobTitle

```python
class JobTitle(BaseModel):
    """نموذج العنوان الوظيفي"""
    
    title: str                # العنوان الوظيفي
    description: str          # وصف المهام
    base_salary: Decimal      # الراتب الأساسي المقترح
    
    # العلاقات
    employees: List[Employee] # الموظفون بهذا العنوان
```

### FinancialTransaction

```python
class FinancialTransaction(BaseModel):
    """نموذج المعاملة المالية"""
    
    employee_id: int          # معرف الموظف
    transaction_type: TransactionType  # نوع المعاملة
    amount: Decimal           # المبلغ
    remaining_amount: Decimal # المبلغ المتبقي
    transaction_date: date    # تاريخ المعاملة
    description: str          # الوصف
    payment_status: PaymentStatus  # حالة الدفع
    
    # العلاقات
    employee: Employee        # الموظف
    
    # الطرق الثابتة
    @staticmethod
    def get_pending_debts(session) -> List['FinancialTransaction']:
        """الحصول على الديون المعلقة"""
        
    @staticmethod
    def get_transactions_by_period(session, start_date, end_date) -> List['FinancialTransaction']:
        """الحصول على المعاملات لفترة محددة"""
```

### SalaryRecord

```python
class SalaryRecord(BaseModel):
    """نموذج سجل الراتب"""
    
    employee_id: int          # معرف الموظف
    month: int                # الشهر
    year: int                 # السنة
    basic_salary: Decimal     # الراتب الأساسي
    allowances: Decimal       # البدلات
    deductions: Decimal       # الخصومات
    gross_salary: Decimal     # إجمالي الراتب
    net_salary: Decimal       # صافي الراتب
    is_paid: bool            # حالة الصرف
    
    # العلاقات
    employee: Employee        # الموظف
    
    # الطرق الثابتة
    @staticmethod
    def calculate_salary(employee, month, year, session) -> 'SalaryRecord':
        """حساب راتب الموظف لشهر محدد"""
```

### AuditLog

```python
class AuditLog(BaseModel):
    """نموذج سجل التدقيق"""
    
    action_type: ActionType   # نوع العملية
    table_name: str           # اسم الجدول
    record_id: str            # معرف السجل
    description: str          # وصف العملية
    old_values: dict          # القيم القديمة
    new_values: dict          # القيم الجديدة
    user_name: str            # اسم المستخدم
    ip_address: str           # عنوان IP
    timestamp: datetime       # وقت العملية
    
    # الطرق الثابتة
    @staticmethod
    def log_action(session, action_type, **kwargs):
        """تسجيل عملية في سجل التدقيق"""
        
    @staticmethod
    def get_recent_logs(session, limit=100) -> List['AuditLog']:
        """الحصول على السجلات الحديثة"""
```

## التعدادات (Enums)

### EmploymentStatus

```python
class EmploymentStatus(Enum):
    """حالة التوظيف"""
    ACTIVE = "نشط"           # موظف نشط
    TERMINATED = "مفصول"     # موظف مفصول
    SUSPENDED = "موقوف"      # موظف موقوف
```

### TransactionType

```python
class TransactionType(Enum):
    """نوع المعاملة المالية"""
    ADVANCE = "سلفة"         # سلفة
    DEDUCTION = "خصم"        # خصم
    BONUS = "مكافأة"         # مكافأة
    MARKET_DEBT = "دين الماركت"  # دين الماركت
```

### PaymentStatus

```python
class PaymentStatus(Enum):
    """حالة الدفع"""
    PENDING = "معلق"         # معلق
    PARTIAL = "جزئي"        # مدفوع جزئياً
    COMPLETED = "مكتمل"      # مدفوع بالكامل
```

### ActionType

```python
class ActionType(Enum):
    """نوع العملية في سجل التدقيق"""
    CREATE = "إنشاء"         # إنشاء سجل جديد
    UPDATE = "تحديث"         # تحديث سجل
    DELETE = "حذف"           # حذف سجل
    VIEW = "عرض"             # عرض سجل
    LOGIN = "تسجيل دخول"     # تسجيل دخول
    LOGOUT = "تسجيل خروج"    # تسجيل خروج
    BACKUP = "نسخ احتياطي"   # عملية نسخ احتياطي
```

## الخدمات (Services)

### ReportService

```python
class ReportService:
    """خدمة توليد التقارير"""
    
    def __init__(self):
        """إنشاء خدمة التقارير"""
        
    def generate_employees_report(self, parameters: dict) -> bytes:
        """توليد تقرير الموظفين"""
        
    def generate_salaries_report(self, parameters: dict) -> bytes:
        """توليد تقرير الرواتب"""
        
    def generate_financial_report(self, parameters: dict) -> bytes:
        """توليد تقرير المعاملات المالية"""
        
    def generate_employee_account_report(self, parameters: dict) -> bytes:
        """توليد تقرير كشف حساب الموظف"""
        
    def export_to_excel(self, data: List[List], headers: List[str], filename: str) -> bytes:
        """تصدير البيانات إلى Excel"""
```

### BackupService

```python
class BackupService:
    """خدمة النسخ الاحتياطي"""
    
    def __init__(self):
        """إنشاء خدمة النسخ الاحتياطي"""
        
    def create_backup(self, backup_name: str = None) -> dict:
        """إنشاء نسخة احتياطية"""
        
    def restore_backup(self, backup_path: str) -> dict:
        """استعادة نسخة احتياطية"""
        
    def list_backups(self) -> List[dict]:
        """قائمة النسخ الاحتياطية"""
        
    def delete_backup(self, backup_path: str) -> bool:
        """حذف نسخة احتياطية"""
        
    def start_scheduler(self):
        """بدء جدولة النسخ التلقائي"""
        
    def stop_scheduler(self):
        """إيقاف جدولة النسخ التلقائي"""
```

## الواجهات (Views)

### MainWindow

```python
class MainWindow(QMainWindow):
    """النافذة الرئيسية"""
    
    def __init__(self):
        """إنشاء النافذة الرئيسية"""
        
    def show_page(self, page_name: str):
        """عرض صفحة محددة"""
        
    def add_page(self, page_id: str, widget: QWidget, title: str):
        """إضافة صفحة جديدة"""
```

### EmployeesView

```python
class EmployeesView(QWidget):
    """واجهة إدارة الموظفين"""
    
    # الإشارات
    add_employee_requested = Signal()
    edit_employee_requested = Signal(int)
    
    def __init__(self):
        """إنشاء واجهة الموظفين"""
        
    def load_employees(self):
        """تحميل قائمة الموظفين"""
        
    def filter_employees(self):
        """تصفية الموظفين"""
        
    def search_employees(self, search_text: str):
        """البحث في الموظفين"""
```

### EmployeeForm

```python
class EmployeeForm(QDialog):
    """نموذج إضافة/تعديل الموظف"""
    
    # الإشارات
    employee_saved = Signal(int)
    
    def __init__(self, employee_id: int = None, parent=None):
        """إنشاء نموذج الموظف"""
        
    def load_employee_data(self, employee_id: int):
        """تحميل بيانات الموظف للتعديل"""
        
    def validate_form(self) -> bool:
        """التحقق من صحة البيانات"""
        
    def save_employee(self):
        """حفظ بيانات الموظف"""
        
    def clear_form(self):
        """مسح النموذج"""
```

## الأدوات المساعدة (Utils)

### ThemeManager

```python
class ThemeManager(QObject):
    """مدير الأنماط والثيمات"""
    
    # الإشارات
    theme_changed = Signal(str)
    
    def __init__(self):
        """إنشاء مدير الأنماط"""
        
    def get_available_themes(self) -> dict:
        """الحصول على الثيمات المتاحة"""
        
    def get_current_theme(self) -> str:
        """الحصول على الثيم الحالي"""
        
    def set_theme(self, theme_name: str) -> bool:
        """تعيين الثيم"""
        
    def toggle_theme(self) -> str:
        """تبديل الثيم"""
```

### UI Helpers

```python
def apply_rtl_layout(widget: QWidget):
    """تطبيق تخطيط من اليمين لليسار"""
    
def setup_table_widget(table: QTableWidget, headers: List[str], 
                      column_widths: List[int], **kwargs):
    """إعداد جدول البيانات"""
    
def populate_table(table: QTableWidget, data: List[List], editable: bool = False):
    """ملء الجدول بالبيانات"""
    
def show_message(parent: QWidget, title: str, message: str, 
                message_type: str = "information"):
    """عرض رسالة للمستخدم"""
    
def show_confirmation(parent: QWidget, title: str, message: str) -> bool:
    """عرض رسالة تأكيد"""
    
def setup_combobox(combo: QComboBox, items: List[tuple], default_value=None):
    """إعداد القائمة المنسدلة"""
    
def get_combobox_value(combo: QComboBox):
    """الحصول على قيمة القائمة المنسدلة"""
    
def format_currency(amount: Decimal) -> str:
    """تنسيق المبلغ المالي"""
    
def format_date(date_obj: date) -> str:
    """تنسيق التاريخ"""
    
def format_datetime(datetime_obj: datetime) -> str:
    """تنسيق التاريخ والوقت"""
```

## إدارة قاعدة البيانات

### Database Manager

```python
class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self):
        """إنشاء مدير قاعدة البيانات"""
        
    def create_engine(self) -> Engine:
        """إنشاء محرك قاعدة البيانات"""
        
    def create_tables(self):
        """إنشاء الجداول"""
        
    def get_session(self) -> Session:
        """الحصول على جلسة قاعدة البيانات"""
        
    def close_session(self, session: Session):
        """إغلاق جلسة قاعدة البيانات"""
```

### Context Manager

```python
@contextmanager
def get_db_session_context():
    """مدير السياق لجلسة قاعدة البيانات"""
    session = SessionLocal()
    try:
        yield session
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()
```

## التكوين (Configuration)

### Config Manager

```python
def get_config(section: str = None) -> dict:
    """الحصول على إعدادات التطبيق"""
    
def update_config(section: str, key: str, value):
    """تحديث إعداد محدد"""
    
def get_database_url() -> str:
    """الحصول على رابط قاعدة البيانات"""
```

## الثوابت

```python
# مسارات الملفات
CONFIG_DIR = Path("config")
STYLES_DIR = Path("src/styles")
BACKUP_DIR = Path("backups")
LOGS_DIR = Path("logs")

# إعدادات قاعدة البيانات
DEFAULT_DB_HOST = "localhost"
DEFAULT_DB_PORT = 5432
DEFAULT_DB_NAME = "hr_system"

# إعدادات التطبيق
APP_NAME = "نظام إدارة شؤون الموظفين"
APP_VERSION = "1.0.0"
```

---

**هذا المرجع يوفر نظرة شاملة على جميع الواجهات البرمجية في النظام. للمزيد من التفاصيل، راجع الكود المصدري والتعليقات المضمنة.**
