"""
نموذج إضافة/تعديل العنوان الوظيفي
Job Title Add/Edit Form
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit, QTextEdit,
    QPushButton, QLabel, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QDoubleValidator

from ..utils import apply_rtl_layout, show_message, clean_numeric_input
from ..database import get_db_session_context
from ..models import JobTitle


class JobTitleForm(QDialog):
    """نموذج إضافة/تعديل العنوان الوظيفي"""
    
    # إشارات
    job_title_saved = Signal(int)  # إشارة حفظ العنوان الوظيفي
    
    def __init__(self, job_title_id: int = None, parent=None):
        super().__init__(parent)
        self.job_title_id = job_title_id
        self.is_edit_mode = job_title_id is not None
        self.job_title = None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_job_title_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setObjectName("job_title_form")
        apply_rtl_layout(self)
        
        # إعداد النافذة
        title = "✏️ تعديل العنوان الوظيفي" if self.is_edit_mode else "➕ إضافة عنوان وظيفي جديد"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(650, 550)  # تكبير النافذة
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # إنشاء رأس النموذج
        self.create_header(main_layout)
        
        # إنشاء حقول النموذج
        self.create_form_fields(main_layout)
        
        # إنشاء أزرار الإجراءات
        self.create_action_buttons(main_layout)
        
    def create_header(self, layout: QVBoxLayout):
        """إنشاء رأس النموذج المحسن"""
        # إطار العنوان
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #f0f8ff;
                border: 2px solid #6f42c1;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(8)

        # العنوان الرئيسي
        title = "✏️ تعديل بيانات العنوان الوظيفي" if self.is_edit_mode else "➕ إضافة عنوان وظيفي جديد"
        main_title = QLabel(title)
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin: 5px 0px;
            }
        """)

        # العنوان الفرعي
        subtitle = "🎯 يرجى ملء جميع الحقول المطلوبة وتحديد نطاق الراتب" if not self.is_edit_mode else "📝 تحديث معلومات العنوان الوظيفي"
        sub_title = QLabel(subtitle)
        sub_title.setAlignment(Qt.AlignCenter)
        sub_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #6c757d;
                margin: 0px;
            }
        """)

        header_layout.addWidget(main_title)
        header_layout.addWidget(sub_title)
        layout.addWidget(header_frame)
        
    def create_form_fields(self, layout: QVBoxLayout):
        """إنشاء حقول النموذج المحسنة"""
        # إطار النموذج
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 20px;
                margin: 10px 5px;
            }
        """)

        form_layout = QFormLayout(form_frame)
        form_layout.setSpacing(15)
        form_layout.setLabelAlignment(Qt.AlignRight)

        # تنسيق التسميات
        label_style = """
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #495057;
                padding: 5px;
            }
        """

        # تنسيق حقول الإدخال
        input_style = """
            QLineEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                padding: 10px 12px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #6f42c1;
                background-color: #f8f9fa;
            }
            QLineEdit:hover {
                border-color: #adb5bd;
            }
        """

        # رمز الوظيفة
        code_label = QLabel("🏷️ رمز الوظيفة *:")
        code_label.setStyleSheet(label_style)

        code_layout = QHBoxLayout()
        self.code_input = QLineEdit()
        self.code_input.setMaxLength(10)
        self.code_input.setPlaceholderText("سيتم توليده تلقائياً")
        self.code_input.setReadOnly(True)
        self.code_input.setStyleSheet("""
            QLineEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                padding: 10px 12px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #f8f9fa;
                color: #6c757d;
            }
            QLineEdit:focus {
                border-color: #6f42c1;
                background-color: #ffffff;
                color: #495057;
            }
        """)

        self.generate_code_btn = QPushButton("🔄 توليد تلقائي")
        self.generate_code_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #17a2b8;
                border: none;
                border-radius: 5px;
                padding: 10px 15px;
                min-width: 100px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:pressed {
                background-color: #117a8b;
            }
        """)

        code_layout.addWidget(self.code_input)
        code_layout.addWidget(self.generate_code_btn)
        form_layout.addRow(code_label, code_layout)

        # العنوان الوظيفي
        title_label = QLabel("💼 العنوان الوظيفي *:")
        title_label.setStyleSheet(label_style)

        self.title_input = QLineEdit()
        self.title_input.setMaxLength(100)
        self.title_input.setPlaceholderText("مثال: مدير قسم تقنية المعلومات")
        self.title_input.setStyleSheet(input_style)
        form_layout.addRow(title_label, self.title_input)

        # وصف الوظيفة
        desc_label = QLabel("📝 الوصف:")
        desc_label.setStyleSheet(label_style)

        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(120)
        self.description_input.setPlaceholderText("وصف مختصر للوظيفة ومسؤولياتها الأساسية...")
        self.description_input.setStyleSheet("""
            QTextEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                padding: 10px 12px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #ffffff;
            }
            QTextEdit:focus {
                border-color: #6f42c1;
                background-color: #f8f9fa;
            }
            QTextEdit:hover {
                border-color: #adb5bd;
            }
        """)
        form_layout.addRow(desc_label, self.description_input)

        # الحد الأدنى للراتب
        min_salary_label = QLabel("💰 الحد الأدنى للراتب:")
        min_salary_label.setStyleSheet(label_style)

        self.min_salary_input = QLineEdit()
        self.min_salary_input.setPlaceholderText("1,000,000")
        validator_min = QDoubleValidator(0.0, 999999999.99, 2)
        self.min_salary_input.setValidator(validator_min)
        self.min_salary_input.setStyleSheet(input_style)
        form_layout.addRow(min_salary_label, self.min_salary_input)

        # الحد الأقصى للراتب
        max_salary_label = QLabel("💎 الحد الأقصى للراتب:")
        max_salary_label.setStyleSheet(label_style)

        self.max_salary_input = QLineEdit()
        self.max_salary_input.setPlaceholderText("5,000,000")
        validator_max = QDoubleValidator(0.0, 999999999.99, 2)
        self.max_salary_input.setValidator(validator_max)
        self.max_salary_input.setStyleSheet(input_style)
        form_layout.addRow(max_salary_label, self.max_salary_input)

        layout.addWidget(form_frame)
        
    def create_action_buttons(self, layout: QVBoxLayout):
        """إنشاء أزرار الإجراءات المحسنة"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px 5px 5px 5px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)

        # زر الحفظ
        save_text = "💾 تحديث البيانات" if self.is_edit_mode else "💾 حفظ العنوان الوظيفي"
        self.save_btn = QPushButton(save_text)
        self.save_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: white;
                background-color: #6f42c1;
                border: none;
                border-radius: 8px;
                padding: 12px 25px;
                min-width: 160px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #4e2a87;
                transform: translateY(0px);
            }
        """)

        # زر الإلغاء
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #6c757d;
                background-color: #ffffff;
                border: 2px solid #6c757d;
                border-radius: 8px;
                padding: 12px 25px;
                min-width: 100px;
                min-height: 45px;
            }
            QPushButton:hover {
                color: white;
                background-color: #6c757d;
                border-color: #6c757d;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #5a6268;
                border-color: #5a6268;
                transform: translateY(0px);
            }
        """)

        # ترتيب الأزرار
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_job_title)
        self.cancel_btn.clicked.connect(self.reject)

        # ربط زر توليد الرمز
        self.generate_code_btn.clicked.connect(self.generate_job_title_code)

        # توليد رمز تلقائي عند تغيير العنوان الوظيفي
        self.title_input.textChanged.connect(self.auto_generate_code)

        # التحقق من نطاق الراتب
        self.min_salary_input.textChanged.connect(self.validate_salary_range)
        self.max_salary_input.textChanged.connect(self.validate_salary_range)
        
    def validate_salary_range(self):
        """التحقق من صحة نطاق الراتب"""
        min_salary = clean_numeric_input(self.min_salary_input.text())
        max_salary = clean_numeric_input(self.max_salary_input.text())

        # إعادة تعيين الأنماط
        input_style = """
            QLineEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                padding: 10px 12px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #6f42c1;
                background-color: #f8f9fa;
            }
            QLineEdit:hover {
                border-color: #adb5bd;
            }
        """

        error_style = """
            QLineEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                padding: 10px 12px;
                border: 2px solid #dc3545;
                border-radius: 6px;
                background-color: #fff5f5;
                color: #721c24;
            }
            QLineEdit:focus {
                border-color: #dc3545;
                background-color: #f8d7da;
            }
        """

        if min_salary and max_salary and min_salary > max_salary:
            self.max_salary_input.setStyleSheet(error_style)
            self.max_salary_input.setToolTip("⚠️ الحد الأقصى يجب أن يكون أكبر من الحد الأدنى")
        else:
            self.max_salary_input.setStyleSheet(input_style)
            self.max_salary_input.setToolTip("")
            
    def load_job_title_data(self):
        """تحميل بيانات العنوان الوظيفي للتعديل"""
        try:
            with get_db_session_context() as session:
                self.job_title = session.query(JobTitle).filter_by(id=self.job_title_id).first()
                
                if not self.job_title:
                    show_message(self, "خطأ", "العنوان الوظيفي غير موجود", "error")
                    self.reject()
                    return
                
                # ملء الحقول
                self.code_input.setText(self.job_title.code)
                self.title_input.setText(self.job_title.title)
                
                if self.job_title.description:
                    self.description_input.setPlainText(self.job_title.description)
                
                if self.job_title.min_salary:
                    self.min_salary_input.setText(str(self.job_title.min_salary))
                
                if self.job_title.max_salary:
                    self.max_salary_input.setText(str(self.job_title.max_salary))
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل بيانات العنوان الوظيفي: {e}", "error")
            self.reject()

    def generate_job_title_code(self):
        """توليد رمز العنوان الوظيفي تلقائياً"""
        title = self.title_input.text().strip()
        if not title:
            show_message(self, "تنبيه", "يرجى إدخال العنوان الوظيفي أولاً", "warning")
            return

        # توليد رمز من العنوان
        code = self.create_code_from_title(title)

        # التأكد من عدم تكرار الرمز
        unique_code = self.ensure_unique_code(code)

        self.code_input.setText(unique_code)
        self.code_input.setReadOnly(False)

    def auto_generate_code(self):
        """توليد رمز تلقائي عند تغيير العنوان"""
        if self.job_title_id is None:  # فقط للعناوين الجديدة
            title = self.title_input.text().strip()
            if title and len(title) >= 3:
                code = self.create_code_from_title(title)
                unique_code = self.ensure_unique_code(code)
                self.code_input.setText(unique_code)

    def create_code_from_title(self, title: str) -> str:
        """إنشاء رمز من العنوان الوظيفي"""
        # إزالة الكلمات الشائعة
        common_words = ["مدير", "رئيس", "أخصائي", "مساعد", "نائب", "كبير", "أول"]
        words = title.split()
        filtered_words = [word for word in words if word not in common_words]

        if not filtered_words:
            filtered_words = words

        # أخذ الأحرف الأولى من الكلمات
        if len(filtered_words) == 1:
            # كلمة واحدة: أخذ أول 3-4 أحرف
            code = filtered_words[0][:4].upper()
        else:
            # عدة كلمات: أخذ الحرف الأول من كل كلمة
            code = "".join([word[0].upper() for word in filtered_words[:3]])

        # التأكد من أن الرمز يحتوي على أحرف إنجليزية
        if not code.isascii():
            # تحويل الأحرف العربية إلى إنجليزية
            arabic_to_english = {
                'م': 'M', 'د': 'D', 'ر': 'R', 'ا': 'A', 'ل': 'L',
                'ت': 'T', 'ن': 'N', 'س': 'S', 'ع': 'E', 'ب': 'B',
                'ف': 'F', 'ح': 'H', 'ج': 'J', 'ك': 'K', 'و': 'W',
                'ز': 'Z', 'ط': 'T', 'ي': 'Y', 'ص': 'S', 'خ': 'KH',
                'ذ': 'TH', 'ض': 'DH', 'غ': 'GH', 'ظ': 'ZH', 'ق': 'Q'
            }

            english_code = ""
            for char in code:
                english_code += arabic_to_english.get(char, char)
            code = english_code[:4]

        return code

    def ensure_unique_code(self, base_code: str) -> str:
        """التأكد من عدم تكرار الرمز"""
        try:
            with get_db_session_context() as session:
                code = base_code
                counter = 1

                while True:
                    existing = session.query(JobTitle).filter(
                        JobTitle.code == code,
                        JobTitle.id != (self.job_title_id or 0),
                        JobTitle.is_active == True
                    ).first()

                    if not existing:
                        return code

                    # إضافة رقم للرمز
                    code = f"{base_code}{counter}"
                    counter += 1

                    if counter > 99:  # تجنب الحلقة اللانهائية
                        break

                return f"{base_code}{counter}"

        except Exception:
            return base_code
            
    def validate_form(self) -> tuple[bool, str]:
        """التحقق من صحة النموذج"""
        # التحقق من الحقول المطلوبة
        if not self.code_input.text().strip():
            return False, "رمز الوظيفة مطلوب"
        
        if not self.title_input.text().strip():
            return False, "العنوان الوظيفي مطلوب"
        
        # التحقق من صحة رمز الوظيفة
        code = self.code_input.text().strip()
        if len(code) < 2 or len(code) > 10:
            return False, "رمز الوظيفة يجب أن يكون بين 2-10 أحرف"
        
        if not code.isalnum():
            return False, "رمز الوظيفة يجب أن يحتوي على أحرف وأرقام فقط"
        
        # التحقق من نطاق الراتب
        min_salary = clean_numeric_input(self.min_salary_input.text())
        max_salary = clean_numeric_input(self.max_salary_input.text())
        
        if min_salary and min_salary < 0:
            return False, "الحد الأدنى للراتب يجب أن يكون رقماً موجباً"
        
        if max_salary and max_salary < 0:
            return False, "الحد الأقصى للراتب يجب أن يكون رقماً موجباً"
        
        if min_salary and max_salary and min_salary > max_salary:
            return False, "الحد الأدنى للراتب يجب أن يكون أقل من الحد الأقصى"
        
        return True, ""
        
    def save_job_title(self):
        """حفظ بيانات العنوان الوظيفي"""
        # التحقق من صحة النموذج
        is_valid, error_message = self.validate_form()
        if not is_valid:
            show_message(self, "خطأ في البيانات", error_message, "error")
            return
        
        try:
            with get_db_session_context() as session:
                # التحقق من عدم تكرار رمز الوظيفة
                existing_job_title = session.query(JobTitle).filter(
                    JobTitle.code == self.code_input.text().strip(),
                    JobTitle.id != (self.job_title_id or 0),
                    JobTitle.is_active == True
                ).first()
                
                if existing_job_title:
                    show_message(self, "خطأ", "رمز الوظيفة موجود مسبقاً", "error")
                    return
                
                # التحقق من عدم تكرار العنوان الوظيفي
                existing_title = session.query(JobTitle).filter(
                    JobTitle.title == self.title_input.text().strip(),
                    JobTitle.id != (self.job_title_id or 0),
                    JobTitle.is_active == True
                ).first()
                
                if existing_title:
                    show_message(self, "خطأ", "العنوان الوظيفي موجود مسبقاً", "error")
                    return
                
                # إنشاء أو تحديث العنوان الوظيفي
                if self.is_edit_mode:
                    job_title = session.query(JobTitle).filter_by(id=self.job_title_id).first()
                else:
                    job_title = JobTitle()
                    session.add(job_title)
                
                # تعيين البيانات
                job_title.code = self.code_input.text().strip()
                job_title.title = self.title_input.text().strip()
                job_title.description = self.description_input.toPlainText().strip() or None
                job_title.min_salary = clean_numeric_input(self.min_salary_input.text())
                job_title.max_salary = clean_numeric_input(self.max_salary_input.text())
                
                session.commit()
                
                # إرسال إشارة النجاح
                self.job_title_saved.emit(job_title.id)
                
                success_message = "✅ تم تحديث بيانات العنوان الوظيفي بنجاح!" if self.is_edit_mode else "✅ تم إضافة العنوان الوظيفي بنجاح!"
                show_message(self, "🎉 عملية ناجحة", success_message, "information")
                
                self.accept()
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في حفظ بيانات العنوان الوظيفي: {e}", "error")
