"""
الأدوات المساعدة
Utility Functions
"""

from .helpers import *
from .ui_helpers import *
from .validation import *
from .ui_improvements import *
from .search_filter import *
from .font_manager import get_font_manager, set_global_font_size, get_global_font_size
from .appearance_manager import get_appearance_manager

__all__ = [
    # من helpers.py
    "format_currency",
    "format_date",
    "format_datetime",
    "validate_phone",
    "validate_employee_number",
    "validate_email",
    "clean_numeric_input",
    "generate_employee_number",
    "calculate_age",
    "calculate_years_of_service",
    "get_month_name",
    "get_employment_status_display",
    "get_transaction_type_display",
    "get_payment_status_display",
    "safe_divide",
    "truncate_text",

    # من ui_helpers.py
    "show_message",
    "show_confirmation",
    "setup_table_widget",
    "populate_table",
    "setup_combobox",
    "get_combobox_value",
    "set_combobox_value",
    "setup_date_edit",
    "clear_form",
    "validate_form",
    "set_widget_style",
    "create_fade_animation",
    "show_notification",
    "center_widget",
    "apply_rtl_layout",
    "set_font_size",
    "make_bold",
    "setup_arabic_font",
    "center_text_arabic",
    "align_text_right_arabic",
    "setup_table_rtl",
    "setup_form_rtl",
    "setup_button_arabic",
    "improve_arabic_display",

    # من validation.py
    "ValidationError",
    "Validator",
    "FormValidator",
    "validate_employee_data",
    "validate_department_data",
    "validate_job_title_data",
    "validate_financial_transaction_data",

    # من search_filter.py
    "FilterOperator",
    "SearchFilter",
    "SearchEngine",
    "AdvancedSearchBuilder",
    "create_search_builder",
    "quick_search",
    "filter_by_field",
    "filter_by_date_range",
    "filter_by_numeric_range",

    # من ui_improvements.py
    "UIStyleManager",
    "apply_modern_style",
    "setup_button_styles",
    "improve_layout_spacing",
    "setup_responsive_sizing",
    "fix_common_ui_issues",
    "apply_comprehensive_ui_improvements",
]