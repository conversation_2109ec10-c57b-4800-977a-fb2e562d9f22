#!/usr/bin/env python3
"""
إنشاء معاملات مالية بسيطة
"""

import sys
from pathlib import Path
from datetime import date

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.database import db_manager, get_db_session_context
from src.models import Employee, FinancialTransaction, TransactionType


def create_simple_transactions():
    """إنشاء معاملات مالية بسيطة"""
    print("💰 إنشاء معاملات مالية بسيطة...")
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        with get_db_session_context() as session:
            # الحصول على الموظف الأول
            employee = session.query(Employee).filter_by(is_active=True).first()
            
            if not employee:
                print("❌ لا يوجد موظفين في النظام")
                return False
            
            print(f"👤 الموظف: {employee.full_name}")
            
            current_date = date.today()
            
            # إنشاء سلفة
            advance = FinancialTransaction(
                employee_id=employee.id,
                transaction_type=TransactionType.ADVANCE,
                amount=50000,
                description="سلفة شهرية",
                transaction_date=current_date
            )
            session.add(advance)
            print("💸 تم إضافة سلفة: 50,000 د.ع")
            
            # إنشاء مكافأة
            bonus = FinancialTransaction(
                employee_id=employee.id,
                transaction_type=TransactionType.BONUS,
                amount=75000,
                description="مكافأة أداء",
                transaction_date=current_date
            )
            session.add(bonus)
            print("🎁 تم إضافة مكافأة: 75,000 د.ع")
            
            # إنشاء خصم
            deduction = FinancialTransaction(
                employee_id=employee.id,
                transaction_type=TransactionType.DEDUCTION,
                amount=25000,
                description="خصم تأخير",
                transaction_date=current_date
            )
            session.add(deduction)
            print("📉 تم إضافة خصم: 25,000 د.ع")
            
            # إنشاء دين ماركت
            debt = FinancialTransaction(
                employee_id=employee.id,
                transaction_type=TransactionType.MARKET_DEBT,
                amount=30000,
                description="دين ماركت",
                transaction_date=current_date
            )
            session.add(debt)
            print("🏪 تم إضافة دين ماركت: 30,000 د.ع")
            
            # حفظ التغييرات
            session.commit()
            
            print("\n✅ تم إنشاء المعاملات المالية بنجاح!")
            
            # عرض الملخص
            print(f"\n📊 ملخص المعاملات للموظف {employee.full_name}:")
            print(f"💸 السلف: 50,000 د.ع")
            print(f"🎁 المكافآت: 75,000 د.ع")
            print(f"📉 الخصومات: 25,000 د.ع")
            print(f"🏪 ديون الماركت: 30,000 د.ع")
            
            # حساب الراتب المتوقع
            basic_salary = float(employee.basic_salary)
            expected_net = basic_salary + 75000 - 50000 - 25000 - 30000
            print(f"\n💰 الراتب الأساسي: {basic_salary:,.0f} د.ع")
            print(f"💵 صافي الراتب المتوقع: {expected_net:,.0f} د.ع")
            
            return True
            
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """الدالة الرئيسية"""
    success = create_simple_transactions()
    
    if success:
        print("\n🎉 تم إنشاء المعاملات المالية بنجاح!")
        print("\nالآن يمكنك:")
        print("1. تشغيل النظام: python run_hr_system.py")
        print("2. الذهاب إلى قسم الرواتب")
        print("3. حساب الرواتب للشهر الحالي")
        print("4. تعديل تفاصيل الراتب")
        print("5. صرف الرواتب")
        return 0
    else:
        print("💥 فشل في إنشاء المعاملات المالية!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
