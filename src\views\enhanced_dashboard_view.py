"""
عرض لوحة التحكم المحسنة
Enhanced Dashboard View
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout
from PySide6.QtCore import Signal

from ..widgets import EnhancedDashboard


class EnhancedDashboardView(QWidget):
    """عرض لوحة التحكم المحسنة"""
    
    # إشارات للإجراءات السريعة
    add_employee_requested = Signal()
    add_transaction_requested = Signal()
    view_reports_requested = Signal()
    backup_requested = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # استخدام لوحة التحكم المحسنة
        self.enhanced_dashboard = EnhancedDashboard()
        
        # ربط الإشارات
        self.enhanced_dashboard.add_employee_requested.connect(self.add_employee_requested.emit)
        self.enhanced_dashboard.add_transaction_requested.connect(self.add_transaction_requested.emit)
        self.enhanced_dashboard.view_reports_requested.connect(self.view_reports_requested.emit)
        self.enhanced_dashboard.backup_requested.connect(self.backup_requested.emit)
        
        layout.addWidget(self.enhanced_dashboard)
        
    def refresh_data(self):
        """تحديث البيانات"""
        if hasattr(self, 'enhanced_dashboard'):
            self.enhanced_dashboard.refresh_statistics()
