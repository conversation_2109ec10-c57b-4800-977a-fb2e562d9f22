"""
مولد التقارير المحسن
Enhanced Report Generator
"""

import io
import csv
from datetime import datetime, date
from typing import Dict, List, Any, Optional, Union
from decimal import Decimal

try:
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

from ..database import get_db_session_context
from ..models import Employee, Department, JobTitle, FinancialTransaction, SalaryRecord, TransactionType, PaymentStatus
from .helpers import format_currency, format_date, get_employment_status_display, get_transaction_type_display


class ReportGenerator:
    """مولد التقارير"""
    
    def __init__(self):
        self.setup_fonts()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        if REPORTLAB_AVAILABLE:
            try:
                # محاولة تحميل خط عربي (يجب توفير ملف الخط)
                # pdfmetrics.registerFont(TTFont('Arabic', 'fonts/NotoSansArabic-Regular.ttf'))
                pass
            except:
                pass
    
    def generate_employees_report(self, parameters: Dict[str, Any]) -> bytes:
        """توليد تقرير الموظفين"""
        try:
            with get_db_session_context() as session:
                # بناء الاستعلام
                query = session.query(Employee).filter_by(is_active=True)
                
                # تطبيق التصفيات
                if parameters.get('department_id'):
                    query = query.filter(Employee.department_id == parameters['department_id'])
                
                if parameters.get('job_title_id'):
                    query = query.filter(Employee.job_title_id == parameters['job_title_id'])
                
                if parameters.get('employment_status'):
                    query = query.filter(Employee.employment_status == parameters['employment_status'])
                
                employees = query.all()
                
                # توليد التقرير حسب النوع
                if parameters.get('subtype') == 'detailed':
                    return self._generate_detailed_employees_report(employees)
                else:
                    return self._generate_employees_list_report(employees)
                    
        except Exception as e:
            raise Exception(f"فشل في توليد تقرير الموظفين: {e}")
    
    def generate_salaries_report(self, parameters: Dict[str, Any]) -> bytes:
        """توليد تقرير الرواتب"""
        try:
            with get_db_session_context() as session:
                # بناء الاستعلام
                query = session.query(SalaryRecord).filter_by(is_active=True)
                
                # تطبيق التصفيات
                if parameters.get('month'):
                    query = query.filter(SalaryRecord.month == parameters['month'])
                
                if parameters.get('year'):
                    query = query.filter(SalaryRecord.year == parameters['year'])
                
                if parameters.get('department_id'):
                    query = query.join(Employee).filter(Employee.department_id == parameters['department_id'])
                
                salary_records = query.all()
                
                # توليد التقرير حسب النوع
                if parameters.get('subtype') == 'detailed':
                    return self._generate_detailed_salaries_report(salary_records, parameters)
                else:
                    return self._generate_salaries_summary_report(salary_records, parameters)
                    
        except Exception as e:
            raise Exception(f"فشل في توليد تقرير الرواتب: {e}")
    
    def generate_financial_report(self, parameters: Dict[str, Any]) -> bytes:
        """توليد تقرير المعاملات المالية"""
        try:
            with get_db_session_context() as session:
                # بناء الاستعلام
                query = session.query(FinancialTransaction).filter_by(is_active=True)
                
                # تطبيق التصفيات
                if parameters.get('from_date'):
                    query = query.filter(FinancialTransaction.transaction_date >= parameters['from_date'])
                
                if parameters.get('to_date'):
                    query = query.filter(FinancialTransaction.transaction_date <= parameters['to_date'])
                
                if parameters.get('transaction_type'):
                    query = query.filter(FinancialTransaction.transaction_type == parameters['transaction_type'])
                
                transactions = query.all()
                
                # توليد التقرير حسب النوع
                if parameters.get('subtype') == 'pending_debts':
                    return self._generate_pending_debts_report(transactions)
                elif parameters.get('subtype') == 'detailed':
                    return self._generate_detailed_financial_report(transactions, parameters)
                else:
                    return self._generate_financial_summary_report(transactions, parameters)
                    
        except Exception as e:
            raise Exception(f"فشل في توليد تقرير المعاملات المالية: {e}")
    
    def generate_employee_account_report(self, parameters: Dict[str, Any]) -> bytes:
        """توليد تقرير كشف حساب الموظف"""
        try:
            employee_id = parameters.get('employee_id')
            if not employee_id:
                raise Exception("يجب تحديد الموظف")
            
            with get_db_session_context() as session:
                employee = session.query(Employee).filter_by(id=employee_id, is_active=True).first()
                if not employee:
                    raise Exception("الموظف غير موجود")
                
                # جلب المعاملات المالية
                transactions_query = session.query(FinancialTransaction).filter_by(
                    employee_id=employee_id, is_active=True
                )
                
                if parameters.get('from_date'):
                    transactions_query = transactions_query.filter(
                        FinancialTransaction.transaction_date >= parameters['from_date']
                    )
                
                if parameters.get('to_date'):
                    transactions_query = transactions_query.filter(
                        FinancialTransaction.transaction_date <= parameters['to_date']
                    )
                
                transactions = transactions_query.order_by(FinancialTransaction.transaction_date).all()
                
                # جلب سجلات الرواتب
                salaries_query = session.query(SalaryRecord).filter_by(
                    employee_id=employee_id, is_active=True
                )
                
                if parameters.get('from_date'):
                    # تحويل التاريخ إلى شهر وسنة
                    from_date = parameters['from_date']
                    salaries_query = salaries_query.filter(
                        (SalaryRecord.year > from_date.year) |
                        ((SalaryRecord.year == from_date.year) & (SalaryRecord.month >= from_date.month))
                    )
                
                if parameters.get('to_date'):
                    to_date = parameters['to_date']
                    salaries_query = salaries_query.filter(
                        (SalaryRecord.year < to_date.year) |
                        ((SalaryRecord.year == to_date.year) & (SalaryRecord.month <= to_date.month))
                    )
                
                salary_records = salaries_query.order_by(SalaryRecord.year, SalaryRecord.month).all()
                
                # توليد التقرير حسب النوع
                if parameters.get('subtype') == 'summary':
                    return self._generate_employee_account_summary(employee, transactions, salary_records, parameters)
                else:
                    return self._generate_employee_account_statement(employee, transactions, salary_records, parameters)
                    
        except Exception as e:
            raise Exception(f"فشل في توليد كشف حساب الموظف: {e}")
    
    def _generate_employees_list_report(self, employees: List[Employee]) -> bytes:
        """توليد قائمة الموظفين"""
        output = io.StringIO()
        writer = csv.writer(output)
        
        # كتابة العناوين
        headers = [
            'الرقم الوظيفي', 'الاسم الكامل', 'القسم', 'العنوان الوظيفي',
            'الراتب الأساسي', 'تاريخ المباشرة', 'حالة التوظيف', 'رقم الهاتف'
        ]
        writer.writerow(headers)
        
        # كتابة البيانات
        for emp in employees:
            row = [
                emp.employee_number,
                emp.full_name,
                emp.department.name if emp.department else '',
                emp.job_title.title if emp.job_title else '',
                str(emp.basic_salary),
                format_date(emp.hire_date),
                get_employment_status_display(emp.employment_status.name),
                emp.phone or ''
            ]
            writer.writerow(row)
        
        return output.getvalue().encode('utf-8-sig')
    
    def _generate_detailed_employees_report(self, employees: List[Employee]) -> bytes:
        """توليد تقرير مفصل للموظفين"""
        output = io.StringIO()
        writer = csv.writer(output)
        
        # كتابة العناوين
        headers = [
            'الرقم الوظيفي', 'الاسم الكامل', 'رقم الهوية', 'رقم الهاتف', 'البريد الإلكتروني',
            'العنوان', 'القسم', 'العنوان الوظيفي', 'الراتب الأساسي', 'تاريخ المباشرة',
            'حالة التوظيف', 'سنوات الخدمة'
        ]
        writer.writerow(headers)
        
        # كتابة البيانات
        for emp in employees:
            years_of_service = (date.today() - emp.hire_date).days // 365 if emp.hire_date else 0
            
            row = [
                emp.employee_number,
                emp.full_name,
                emp.national_id or '',
                emp.phone or '',
                emp.email or '',
                emp.address or '',
                emp.department.name if emp.department else '',
                emp.job_title.title if emp.job_title else '',
                str(emp.basic_salary),
                format_date(emp.hire_date),
                get_employment_status_display(emp.employment_status.name),
                str(years_of_service)
            ]
            writer.writerow(row)
        
        return output.getvalue().encode('utf-8-sig')
    
    def _generate_salaries_summary_report(self, salary_records: List[SalaryRecord], parameters: Dict[str, Any]) -> bytes:
        """توليد ملخص الرواتب"""
        output = io.StringIO()
        writer = csv.writer(output)
        
        # كتابة معلومات التقرير
        month = parameters.get('month', '')
        year = parameters.get('year', '')
        writer.writerow([f'ملخص الرواتب - {month}/{year}'])
        writer.writerow([])
        
        # كتابة العناوين
        headers = [
            'الموظف', 'القسم', 'الراتب الأساسي', 'أيام العمل',
            'إجمالي السلف', 'إجمالي الخصومات', 'صافي الراتب', 'حالة الصرف'
        ]
        writer.writerow(headers)
        
        total_basic = Decimal('0')
        total_net = Decimal('0')
        
        # كتابة البيانات
        for record in salary_records:
            total_basic += record.basic_salary or Decimal('0')
            total_net += record.net_salary or Decimal('0')
            
            row = [
                record.employee.full_name if record.employee else '',
                record.employee.department.name if record.employee and record.employee.department else '',
                str(record.basic_salary or 0),
                str(record.working_days or 0),
                str(record.total_advances or 0),
                str(record.total_deductions or 0),
                str(record.net_salary or 0),
                record.payment_status.value if record.payment_status else ''
            ]
            writer.writerow(row)
        
        # كتابة الإجماليات
        writer.writerow([])
        writer.writerow(['الإجماليات', '', str(total_basic), '', '', '', str(total_net), ''])
        
        return output.getvalue().encode('utf-8-sig')
    
    def _generate_detailed_salaries_report(self, salary_records: List[SalaryRecord], parameters: Dict[str, Any]) -> bytes:
        """توليد مسير الرواتب المفصل"""
        # نفس المنطق مع تفاصيل أكثر
        return self._generate_salaries_summary_report(salary_records, parameters)
    
    def _generate_financial_summary_report(self, transactions: List[FinancialTransaction], parameters: Dict[str, Any]) -> bytes:
        """توليد ملخص المعاملات المالية"""
        output = io.StringIO()
        writer = csv.writer(output)
        
        # كتابة معلومات التقرير
        from_date = parameters.get('from_date', '')
        to_date = parameters.get('to_date', '')
        writer.writerow([f'ملخص المعاملات المالية من {from_date} إلى {to_date}'])
        writer.writerow([])
        
        # تجميع البيانات حسب النوع
        summary = {}
        for trans in transactions:
            trans_type = trans.transaction_type.value
            if trans_type not in summary:
                summary[trans_type] = {'count': 0, 'total_amount': Decimal('0'), 'total_paid': Decimal('0')}
            
            summary[trans_type]['count'] += 1
            summary[trans_type]['total_amount'] += trans.amount or Decimal('0')
            summary[trans_type]['total_paid'] += trans.paid_amount or Decimal('0')
        
        # كتابة الملخص
        writer.writerow(['نوع المعاملة', 'العدد', 'إجمالي المبلغ', 'إجمالي المدفوع', 'المتبقي'])
        
        for trans_type, data in summary.items():
            remaining = data['total_amount'] - data['total_paid']
            row = [
                get_transaction_type_display(trans_type),
                str(data['count']),
                str(data['total_amount']),
                str(data['total_paid']),
                str(remaining)
            ]
            writer.writerow(row)
        
        return output.getvalue().encode('utf-8-sig')
    
    def _generate_detailed_financial_report(self, transactions: List[FinancialTransaction], parameters: Dict[str, Any]) -> bytes:
        """توليد تقرير مفصل للمعاملات المالية"""
        output = io.StringIO()
        writer = csv.writer(output)
        
        # كتابة العناوين
        headers = [
            'التاريخ', 'الموظف', 'نوع المعاملة', 'المبلغ', 'المبلغ المدفوع',
            'المتبقي', 'حالة الدفع', 'الوصف'
        ]
        writer.writerow(headers)
        
        # كتابة البيانات
        for trans in transactions:
            remaining = (trans.amount or Decimal('0')) - (trans.paid_amount or Decimal('0'))
            
            row = [
                format_date(trans.transaction_date),
                trans.employee.full_name if trans.employee else '',
                get_transaction_type_display(trans.transaction_type.value),
                str(trans.amount or 0),
                str(trans.paid_amount or 0),
                str(remaining),
                trans.payment_status.value if trans.payment_status else '',
                trans.description or ''
            ]
            writer.writerow(row)
        
        return output.getvalue().encode('utf-8-sig')
    
    def _generate_pending_debts_report(self, transactions: List[FinancialTransaction]) -> bytes:
        """توليد تقرير الديون المعلقة"""
        # تصفية المعاملات المعلقة فقط
        pending_transactions = [
            trans for trans in transactions
            if trans.payment_status in [PaymentStatus.PENDING, PaymentStatus.PARTIALLY_PAID]
            and (trans.amount or Decimal('0')) > (trans.paid_amount or Decimal('0'))
        ]
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        writer.writerow(['تقرير الديون المعلقة'])
        writer.writerow([])
        
        # كتابة العناوين
        headers = [
            'الموظف', 'نوع المعاملة', 'تاريخ المعاملة', 'المبلغ الأصلي',
            'المبلغ المدفوع', 'المبلغ المتبقي', 'عدد الأيام'
        ]
        writer.writerow(headers)
        
        total_pending = Decimal('0')
        
        # كتابة البيانات
        for trans in pending_transactions:
            remaining = (trans.amount or Decimal('0')) - (trans.paid_amount or Decimal('0'))
            total_pending += remaining
            
            days_pending = (date.today() - trans.transaction_date).days if trans.transaction_date else 0
            
            row = [
                trans.employee.full_name if trans.employee else '',
                get_transaction_type_display(trans.transaction_type.value),
                format_date(trans.transaction_date),
                str(trans.amount or 0),
                str(trans.paid_amount or 0),
                str(remaining),
                str(days_pending)
            ]
            writer.writerow(row)
        
        # كتابة الإجمالي
        writer.writerow([])
        writer.writerow(['إجمالي الديون المعلقة', '', '', '', '', str(total_pending), ''])
        
        return output.getvalue().encode('utf-8-sig')
    
    def _generate_employee_account_statement(self, employee: Employee, transactions: List[FinancialTransaction], 
                                           salary_records: List[SalaryRecord], parameters: Dict[str, Any]) -> bytes:
        """توليد كشف حساب الموظف"""
        output = io.StringIO()
        writer = csv.writer(output)
        
        # معلومات الموظف
        writer.writerow([f'كشف حساب الموظف: {employee.full_name}'])
        writer.writerow([f'الرقم الوظيفي: {employee.employee_number}'])
        writer.writerow([f'القسم: {employee.department.name if employee.department else ""}'])
        writer.writerow([])
        
        # المعاملات المالية
        writer.writerow(['المعاملات المالية'])
        writer.writerow(['التاريخ', 'النوع', 'المبلغ', 'المدفوع', 'المتبقي', 'الوصف'])
        
        total_transactions = Decimal('0')
        total_paid = Decimal('0')
        
        for trans in transactions:
            remaining = (trans.amount or Decimal('0')) - (trans.paid_amount or Decimal('0'))
            total_transactions += trans.amount or Decimal('0')
            total_paid += trans.paid_amount or Decimal('0')
            
            row = [
                format_date(trans.transaction_date),
                get_transaction_type_display(trans.transaction_type.value),
                str(trans.amount or 0),
                str(trans.paid_amount or 0),
                str(remaining),
                trans.description or ''
            ]
            writer.writerow(row)
        
        writer.writerow([])
        writer.writerow(['إجمالي المعاملات', '', str(total_transactions), str(total_paid), str(total_transactions - total_paid), ''])
        
        # سجلات الرواتب
        writer.writerow([])
        writer.writerow(['سجلات الرواتب'])
        writer.writerow(['الشهر/السنة', 'الراتب الأساسي', 'أيام العمل', 'صافي الراتب', 'حالة الصرف'])
        
        total_salaries = Decimal('0')
        
        for record in salary_records:
            total_salaries += record.net_salary or Decimal('0')
            
            row = [
                f'{record.month}/{record.year}',
                str(record.basic_salary or 0),
                str(record.working_days or 0),
                str(record.net_salary or 0),
                record.payment_status.value if record.payment_status else ''
            ]
            writer.writerow(row)
        
        writer.writerow([])
        writer.writerow(['إجمالي الرواتب', '', '', str(total_salaries), ''])
        
        return output.getvalue().encode('utf-8-sig')
    
    def _generate_employee_account_summary(self, employee: Employee, transactions: List[FinancialTransaction], 
                                         salary_records: List[SalaryRecord], parameters: Dict[str, Any]) -> bytes:
        """توليد ملخص حساب الموظف"""
        output = io.StringIO()
        writer = csv.writer(output)
        
        # معلومات الموظف
        writer.writerow([f'ملخص حساب الموظف: {employee.full_name}'])
        writer.writerow([f'الرقم الوظيفي: {employee.employee_number}'])
        writer.writerow([])
        
        # ملخص المعاملات
        advances = sum((t.amount or Decimal('0')) for t in transactions if t.transaction_type == TransactionType.ADVANCE)
        deductions = sum((t.amount or Decimal('0')) for t in transactions if t.transaction_type == TransactionType.DEDUCTION)
        bonuses = sum((t.amount or Decimal('0')) for t in transactions if t.transaction_type == TransactionType.BONUS)
        market_debts = sum((t.amount or Decimal('0')) for t in transactions if t.transaction_type == TransactionType.MARKET_DEBT)
        
        total_salaries = sum((r.net_salary or Decimal('0')) for r in salary_records)
        
        writer.writerow(['البيان', 'المبلغ'])
        writer.writerow(['إجمالي الرواتب', str(total_salaries)])
        writer.writerow(['إجمالي السلف', str(advances)])
        writer.writerow(['إجمالي الخصومات', str(deductions)])
        writer.writerow(['إجمالي المكافآت', str(bonuses)])
        writer.writerow(['إجمالي ديون الماركت', str(market_debts)])
        writer.writerow([])
        
        # الرصيد النهائي
        final_balance = total_salaries + bonuses - advances - deductions - market_debts
        writer.writerow(['الرصيد النهائي', str(final_balance)])
        
        return output.getvalue().encode('utf-8-sig')
