"""
واجهة إدارة الأقسام
Departments Management View
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QLabel, QFrame, QHeaderView, QAbstractItemView
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from ..utils import (
    apply_rtl_layout, setup_table_widget, populate_table, show_message,
    show_confirmation
)
from ..database import get_db_session_context
from ..models import Department


class DepartmentsView(QWidget):
    """واجهة إدارة الأقسام"""
    
    # إشارات
    department_selected = Signal(int)  # إشارة اختيار قسم
    add_department_requested = Signal()  # إشارة طلب إضافة قسم
    edit_department_requested = Signal(int)  # إشارة طلب تعديل قسم
    
    def __init__(self):
        super().__init__()
        self.departments_data = []
        self.filtered_data = []
        
        self.setup_ui()
        self.setup_connections()
        self.load_departments()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setObjectName("departments_view")
        apply_rtl_layout(self)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # إنشاء رأس الصفحة المحسن
        self.create_enhanced_header(main_layout)

        # إنشاء شريط البحث والإحصائيات المدمج
        self.create_integrated_search_stats(main_layout)

        # إنشاء جدول الأقسام المحسن
        self.create_enhanced_departments_table(main_layout)

        # إنشاء شريط الأزرار المدمج
        self.create_compact_action_buttons(main_layout)
        
    def create_enhanced_header(self, layout: QVBoxLayout):
        """إنشاء رأس الصفحة المحسن"""
        # إنشاء إطار العنوان الرئيسي المحسن
        header_frame = QFrame()
        header_frame.setObjectName("enhanced_header_frame")
        header_frame.setFixedHeight(140)  # ارتفاع أكبر للمظهر الأفضل
        header_frame.setStyleSheet("""
            QFrame#enhanced_header_frame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                            stop: 0 #4fd1c7, stop: 1 #7fdbda);
                border: 3px solid rgba(79, 209, 199, 0.6);
                border-radius: 15px;
                margin: 8px;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 15, 20, 15)  # حشو أكبر للمظهر الأفضل
        header_layout.setSpacing(8)  # تباعد محسن

        # العنوان الرئيسي المحسن
        self.main_title = QLabel("🏢 إدارة الأقسام")
        self.main_title.setAlignment(Qt.AlignCenter)
        self.main_title.setFixedHeight(65)  # ارتفاع أكبر للمظهر الأفضل
        self.main_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 26px;
                font-weight: bold;
                color: white;
                background-color: transparent;
                padding: 10px;
                margin: 0px;
            }
        """)

        # العنوان الفرعي المحسن
        self.subtitle = QLabel("🏗️ إدارة أقسام المؤسسة وتنظيم الهيكل الإداري")
        self.subtitle.setAlignment(Qt.AlignCenter)
        self.subtitle.setFixedHeight(45)  # ارتفاع أكبر للمظهر الأفضل
        self.subtitle.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: rgba(255, 255, 255, 0.9);
                background-color: rgba(255, 255, 255, 0.1);
                padding: 8px 20px;
                border-radius: 10px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                margin: 5px;
            }
        """)

        # تجميع العناصر
        header_layout.addWidget(self.main_title)
        header_layout.addWidget(self.subtitle)

        layout.addWidget(header_frame)

    def update_page_title(self, title: str, subtitle: str):
        """تحديث عنوان الصفحة"""
        self.main_title.setText(title)
        self.subtitle.setText(subtitle)
        
    def create_integrated_search_stats(self, layout: QVBoxLayout):
        """إنشاء البحث والإحصائيات المدمجة"""
        search_frame = QFrame()
        search_frame.setObjectName("search_frame")
        search_frame.setStyleSheet("""
            QFrame#search_frame {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 3px solid #e9ecef;
                border-radius: 15px;
                padding: 20px;
                margin: 15px 0px;
            }
        """)

        search_layout = QVBoxLayout(search_frame)
        search_layout.setSpacing(10)

        # الصف الأول: عنوان البحث مع الإحصائيات
        first_row = QHBoxLayout()

        # عنوان البحث المحسن
        search_title = QLabel("🔍 البحث والتصفية")
        search_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        # الإحصائيات المدمجة
        stats_layout = QHBoxLayout()

        # عداد الأقسام المحسن
        self.departments_count_label = QLabel("📊 إجمالي: 0")
        self.departments_count_label.setFixedHeight(35)
        self.departments_count_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #ecf0f1);
                padding: 8px 15px;
                border-radius: 8px;
                border: 2px solid #3498db;
                text-align: center;
            }
        """)

        # عداد الأقسام النشطة المحسن
        self.active_departments_label = QLabel("✅ نشط: 0")
        self.active_departments_label.setFixedHeight(35)
        self.active_departments_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: #27ae60;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #d5f4e6);
                padding: 8px 15px;
                border-radius: 8px;
                border: 2px solid #27ae60;
                text-align: center;
            }
        """)

        stats_layout.addWidget(self.departments_count_label)
        stats_layout.addWidget(self.active_departments_label)
        stats_layout.addStretch()

        first_row.addWidget(search_title)
        first_row.addStretch()
        first_row.addLayout(stats_layout)

        # الصف الثاني: حقل البحث
        second_row = QHBoxLayout()

        search_label = QLabel("🔎 البحث السريع:")
        search_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: 600;
                color: #495057;
                min-width: 100px;
            }
        """)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم أو الرمز...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 15px;
                padding: 10px 15px;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #f8f9fa);
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #667eea;
                background-color: #ffffff;
            }
        """)

        self.clear_search_btn = QPushButton("🗑️ مسح")
        self.clear_search_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #6c757d;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 60px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)

        second_row.addWidget(search_label)
        second_row.addWidget(self.search_input)
        second_row.addWidget(self.clear_search_btn)

        search_layout.addLayout(first_row)
        search_layout.addLayout(second_row)

        layout.addWidget(search_frame)
        
    def create_enhanced_departments_table(self, layout: QVBoxLayout):
        """إنشاء جدول الأقسام المحسن"""
        self.departments_table = QTableWidget()

        # إعداد الجدول مع أعمدة محسنة
        headers = ["رمز القسم", "اسم القسم", "الوصف", "عدد الموظفين"]
        column_widths = [160, 280, 380, 180]

        setup_table_widget(
            self.departments_table,
            headers,
            column_widths,
            sortable=True,
            selection_behavior="row"
        )

        # تحسين مظهر الجدول المحسن مع تأثيرات التحديد الواضحة
        self.departments_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 3px solid #e9ecef;
                border-radius: 12px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 15px;
                gridline-color: #f1f3f4;
                selection-background-color: #667eea;
                selection-color: white;
                outline: none;
            }

            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #f1f3f4;
                text-align: center;
                color: #2c3e50;
                min-height: 25px;
                border: 1px solid transparent;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                            stop: 0 #667eea, stop: 1 #764ba2);
                color: white;
                font-weight: bold;
                border: 2px solid #5a67d8;
                border-radius: 6px;
            }

            QTableWidget::item:hover:!selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #f8f9fa, stop: 1 #e9ecef);
                border: 1px solid #667eea;
                border-radius: 4px;
            }

            QTableWidget::item:focus {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                            stop: 0 #667eea, stop: 1 #764ba2);
                color: white;
                font-weight: bold;
                border: 2px solid #5a67d8;
                border-radius: 6px;
                outline: none;
            }

            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #667eea, stop: 1 #764ba2);
                color: white;
                font-weight: bold;
                font-size: 15px;
                padding: 15px;
                border: none;
                border-right: 1px solid rgba(255, 255, 255, 0.3);
                text-align: center;
            }

            QHeaderView::section:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #5a67d8, stop: 1 #6c5ce7);
            }
        """)

        # تمكين قائمة السياق
        self.departments_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.departments_table.customContextMenuRequested.connect(self.show_context_menu)

        # تمكين التحديد والتفاعل المحسن
        self.departments_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.departments_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.departments_table.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

        # تحسين خصائص الجدول للتفاعل
        self.departments_table.setAlternatingRowColors(False)
        self.departments_table.setShowGrid(True)
        self.departments_table.setSortingEnabled(False)  # إيقاف الترتيب مؤقتاً لتجنب التداخل
        self.departments_table.setWordWrap(False)

        # تمكين التحديد بالنقر
        self.departments_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.departments_table.setTabKeyNavigation(True)

        layout.addWidget(self.departments_table)
        
    def create_compact_action_buttons(self, layout: QVBoxLayout):
        """إنشاء شريط الأزرار المدمج والعملي"""
        # إطار الأزرار المدمج
        buttons_frame = QFrame()
        buttons_frame.setObjectName("compact_buttons_frame")
        buttons_frame.setStyleSheet("""
            QFrame#compact_buttons_frame {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 12px;
                margin: 10px 0px;
                max-height: 70px;
            }
        """)

        # تخطيط أفقي مدمج
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(12)
        buttons_layout.setContentsMargins(15, 8, 15, 8)

        # أزرار الإجراءات المحسنة
        self.add_btn = QPushButton("➕ إضافة قسم")
        self.add_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #28a745, stop: 1 #20c997);
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                min-width: 120px;
                min-height: 40px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #218838, stop: 1 #1e7e34);
            }
        """)

        self.edit_btn = QPushButton("✏️ تعديل")
        self.edit_btn.setEnabled(False)
        self.edit_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #007bff, stop: 1 #0056b3);
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                min-width: 90px;
                min-height: 40px;
            }
            QPushButton:hover:enabled {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #0056b3, stop: 1 #004085);
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.setEnabled(False)
        self.delete_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #dc3545, stop: 1 #c82333);
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                min-width: 80px;
                min-height: 40px;
            }
            QPushButton:hover:enabled {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #c82333, stop: 1 #bd2130);
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #6f42c1, stop: 1 #5a32a3);
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                min-width: 90px;
                min-height: 40px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #5a32a3, stop: 1 #4e2a8e);
            }
        """)

        # ترتيب الأزرار المدمجة
        buttons_layout.addWidget(self.add_btn)
        buttons_layout.addWidget(self.edit_btn)
        buttons_layout.addWidget(self.delete_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.refresh_btn)

        layout.addWidget(buttons_frame)

    def show_context_menu(self, position):
        """عرض قائمة السياق للجدول"""
        # التحقق من وجود عنصر في الموضع المحدد
        item = self.departments_table.itemAt(position)
        if not item:
            return

        # إنشاء قائمة السياق محسنة
        from PySide6.QtWidgets import QMenu
        context_menu = QMenu(self)
        context_menu.setStyleSheet("""
            QMenu {
                background-color: #ffffff;
                border: 2px solid #4fd1c7;
                border-radius: 10px;
                padding: 8px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 6px;
                margin: 2px;
                font-size: 13px;
            }
            QMenu::item:selected {
                background-color: #4fd1c7;
                color: white;
            }
            QMenu::separator {
                height: 2px;
                background-color: #e0f2f1;
                margin: 4px 8px;
            }
        """)

        # الحصول على الصف المحدد
        row = self.departments_table.currentRow()
        has_selection = 0 <= row < len(self.filtered_data)

        if has_selection:
            department_data = self.filtered_data[row]
            department_name = department_data[1]
            department_code = department_data[0]
            employee_count = int(department_data[3])

            # إضافة عنوان القائمة المحسن
            title_action = context_menu.addAction(f"🏢 {department_name}")
            title_action.setEnabled(False)

            subtitle_action = context_menu.addAction(f"🆔 {department_code} • 👥 {employee_count} موظف")
            subtitle_action.setEnabled(False)

            context_menu.addSeparator()

            # إضافة الإجراءات المحسنة
            edit_action = context_menu.addAction("✏️ تعديل بيانات القسم")
            edit_action.triggered.connect(self.on_edit_department)

            context_menu.addSeparator()

            if employee_count == 0:
                delete_action = context_menu.addAction("🗑️ حذف القسم")
                delete_action.triggered.connect(self.on_delete_department)
            else:
                cannot_delete_action = context_menu.addAction("⚠️ لا يمكن الحذف (يحتوي على موظفين)")
                cannot_delete_action.setEnabled(False)

            context_menu.addSeparator()

            copy_action = context_menu.addAction("📋 نسخ معلومات القسم")
            copy_action.triggered.connect(lambda: self.copy_department_info(row))

            # عرض القائمة
            context_menu.exec(self.departments_table.mapToGlobal(position))

    def copy_department_info(self, row: int):
        """نسخ معلومات القسم إلى الحافظة"""
        if 0 <= row < len(self.filtered_data):
            try:
                from PySide6.QtWidgets import QApplication

                department_data = self.filtered_data[row]
                department_code = department_data[0]
                department_name = department_data[1]
                department_desc = department_data[2]
                employee_count = department_data[3]

                # تنسيق المعلومات
                info_text = f"""معلومات القسم:
🆔 رمز القسم: {department_code}
🏢 اسم القسم: {department_name}
📝 الوصف: {department_desc}
👥 عدد الموظفين: {employee_count}"""

                # نسخ إلى الحافظة
                clipboard = QApplication.clipboard()
                clipboard.setText(info_text)

                # عرض رسالة تأكيد
                from ..utils import show_message
                show_message(self, "تم النسخ", "تم نسخ معلومات القسم إلى الحافظة", "info")

            except Exception as e:
                from ..utils import show_message
                show_message(self, "خطأ", f"فشل في نسخ المعلومات: {e}", "error")
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        # ربط إشارات البحث
        self.search_input.textChanged.connect(self.filter_departments)
        self.clear_search_btn.clicked.connect(self.clear_search)
        
        # ربط إشارات الجدول
        self.departments_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.departments_table.cellDoubleClicked.connect(self.on_cell_double_clicked)
        self.departments_table.cellClicked.connect(self.on_cell_clicked)
        
        # ربط إشارات الأزرار
        self.add_btn.clicked.connect(self.on_add_department)
        self.edit_btn.clicked.connect(self.on_edit_department)
        self.delete_btn.clicked.connect(self.on_delete_department)
        self.refresh_btn.clicked.connect(self.load_departments)
        
    def load_departments(self):
        """تحميل بيانات الأقسام"""
        try:
            with get_db_session_context() as session:
                departments = session.query(Department).filter_by(is_active=True).all()
                
                self.departments_data = []
                for dept in departments:
                    row_data = [
                        dept.code,
                        dept.name,
                        dept.description or "",
                        str(dept.employee_count)
                    ]
                    # إضافة ID للاستخدام الداخلي
                    row_data.append(dept.id)
                    self.departments_data.append(row_data)
                
                # تطبيق التصفية
                self.filter_departments()
                
                # تحديث عداد الأقسام
                total_departments = len(self.departments_data)
                active_departments = len([d for d in self.departments_data if int(d[3]) > 0])

                self.departments_count_label.setText(f"📊 إجمالي: {total_departments}")
                self.active_departments_label.setText(f"✅ نشط: {active_departments}")
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل بيانات الأقسام: {e}", "error")
            
    def filter_departments(self):
        """تصفية الأقسام"""
        search_text = self.search_input.text().lower()
        
        self.filtered_data = []
        
        for row in self.departments_data:
            # فلترة النص
            if search_text and search_text not in row[0].lower() and search_text not in row[1].lower():
                continue
            
            self.filtered_data.append(row)
        
        # تحديث الجدول
        self.update_table()
        
    def update_table(self):
        """تحديث الجدول"""
        # إزالة العمود الأخير (ID) من العرض
        display_data = [row[:-1] for row in self.filtered_data]
        populate_table(self.departments_table, display_data, editable=False)

        # تحسين خصائص العناصر للتحديد
        for row in range(self.departments_table.rowCount()):
            for col in range(self.departments_table.columnCount()):
                item = self.departments_table.item(row, col)
                if item:
                    if row < len(self.filtered_data):
                        # جعل العناصر قابلة للتحديد والتفاعل
                        item.setFlags(Qt.ItemFlag.ItemIsSelectable | Qt.ItemFlag.ItemIsEnabled)
                        # تحسين محاذاة النص
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    else:
                        # الصفوف الفارغة غير قابلة للتحديد
                        item.setFlags(Qt.ItemFlag.NoItemFlags)
        
    def clear_search(self):
        """مسح البحث"""
        self.search_input.clear()
        
    def on_selection_changed(self):
        """معالج تغيير التحديد"""
        has_selection = len(self.departments_table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        
        if has_selection:
            row = self.departments_table.currentRow()
            if 0 <= row < len(self.filtered_data):
                department_id = self.filtered_data[row][-1]  # آخر عنصر هو ID
                self.department_selected.emit(department_id)
                
    def on_cell_clicked(self, row, _):
        """معالج النقر على خلية"""
        # التأكد من أن الصف ضمن البيانات الفعلية
        if 0 <= row < len(self.filtered_data):
            # تحديد الصف كاملاً
            self.departments_table.selectRow(row)
            self.departments_table.setCurrentCell(row, 0)

            # تحديث حالة الأزرار
            self.on_selection_changed()

    def on_cell_double_clicked(self, row, _):
        """معالج النقر المزدوج على خلية"""
        # التأكد من أن الصف ضمن البيانات الفعلية
        if 0 <= row < len(self.filtered_data):
            # تحديد الصف أولاً
            self.departments_table.selectRow(row)
            self.departments_table.setCurrentCell(row, 0)

            # تحديث التحديد
            self.on_selection_changed()

            # تعديل القسم مباشرة
            self.on_edit_department()
        
    def on_add_department(self):
        """معالج إضافة قسم"""
        self.add_department_requested.emit()
        
    def on_edit_department(self):
        """معالج تعديل قسم"""
        row = self.departments_table.currentRow()
        if 0 <= row < len(self.filtered_data):
            department_id = self.filtered_data[row][-1]
            self.edit_department_requested.emit(department_id)
            
    def on_delete_department(self):
        """معالج حذف قسم"""
        row = self.departments_table.currentRow()
        if 0 <= row < len(self.filtered_data):
            department_name = self.filtered_data[row][1]
            employee_count = int(self.filtered_data[row][3])
            
            if employee_count > 0:
                show_message(
                    self,
                    "لا يمكن الحذف",
                    f"لا يمكن حذف القسم '{department_name}' لأنه يحتوي على {employee_count} موظف.",
                    "warning"
                )
                return
            
            if show_confirmation(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف القسم '{department_name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه."
            ):
                department_id = self.filtered_data[row][-1]
                self.delete_department(department_id)
                
    def delete_department(self, department_id: int):
        """حذف قسم"""
        try:
            with get_db_session_context() as session:
                department = session.query(Department).filter_by(id=department_id).first()
                if department:
                    department.soft_delete()
                    session.commit()
                    
                    show_message(self, "نجح", "تم حذف القسم بنجاح", "information")
                    self.load_departments()
                else:
                    show_message(self, "خطأ", "القسم غير موجود", "error")
                    
        except Exception as e:
            show_message(self, "خطأ", f"فشل في حذف القسم: {e}", "error")
            
    def get_selected_department_id(self) -> int:
        """الحصول على معرف القسم المحدد"""
        row = self.departments_table.currentRow()
        if 0 <= row < len(self.filtered_data):
            return self.filtered_data[row][-1]
        return -1
