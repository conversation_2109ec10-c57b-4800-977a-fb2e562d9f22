"""
إعدادات الاختبارات المشتركة
Shared Test Configuration
"""

import pytest
import sys
import os
from pathlib import Path
from unittest.mock import Mock, patch

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# إعداد متغيرات البيئة للاختبار
os.environ["TESTING"] = "1"
os.environ["DATABASE_URL"] = "sqlite:///:memory:"


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """إعداد بيئة الاختبار"""
    # إعداد المسارات
    test_data_dir = Path(__file__).parent / "test_data"
    test_data_dir.mkdir(exist_ok=True)
    
    # إعداد ملفات الاختبار
    yield
    
    # تنظيف بعد الاختبارات
    import shutil
    if test_data_dir.exists():
        shutil.rmtree(test_data_dir, ignore_errors=True)


@pytest.fixture
def mock_database_session():
    """جلسة قاعدة بيانات وهمية"""
    session = Mock()
    
    # إعداد السلوكيات الافتراضية
    session.query.return_value = Mock()
    session.add.return_value = None
    session.commit.return_value = None
    session.rollback.return_value = None
    session.close.return_value = None
    
    return session


@pytest.fixture
def mock_config():
    """تكوين وهمي للاختبار"""
    config = {
        "database": {
            "host": "localhost",
            "port": 5432,
            "username": "test_user",
            "password": "test_password",
            "database": "test_hr_system"
        },
        "app": {
            "name": "نظام إدارة شؤون الموظفين",
            "version": "1.0.0",
            "debug": True
        },
        "ui": {
            "theme": "light",
            "language": "ar",
            "font_size": 10
        },
        "backup": {
            "auto_backup": True,
            "backup_interval": "daily",
            "backup_time": "02:00",
            "max_backups": 30
        },
        "reports": {
            "default_format": "pdf",
            "page_size": "A4",
            "font_name": "Arial"
        }
    }
    
    return config


@pytest.fixture
def sample_employee_data():
    """بيانات موظف نموذجية للاختبار"""
    return {
        "employee_number": "EMP001",
        "full_name": "محمد أحمد علي",
        "national_id": "1234567890",
        "phone": "0501234567",
        "email": "<EMAIL>",
        "hire_date": "2023-01-01",
        "basic_salary": 5000.00,
        "employment_status": "ACTIVE",
        "department_id": 1,
        "job_title_id": 1
    }


@pytest.fixture
def sample_department_data():
    """بيانات قسم نموذجية للاختبار"""
    return {
        "name": "قسم تقنية المعلومات",
        "description": "قسم مسؤول عن تقنية المعلومات والحاسوب",
        "manager_name": "أحمد محمد السعيد"
    }


@pytest.fixture
def sample_job_title_data():
    """بيانات عنوان وظيفي نموذجية للاختبار"""
    return {
        "title": "مطور برمجيات",
        "description": "مطور تطبيقات ومواقع ويب",
        "base_salary": 5000.00
    }


@pytest.fixture
def sample_financial_transaction_data():
    """بيانات معاملة مالية نموذجية للاختبار"""
    return {
        "employee_id": 1,
        "transaction_type": "ADVANCE",
        "amount": 1000.00,
        "remaining_amount": 1000.00,
        "transaction_date": "2024-01-01",
        "description": "سلفة شهرية",
        "payment_status": "PENDING"
    }


@pytest.fixture
def sample_salary_record_data():
    """بيانات سجل راتب نموذجية للاختبار"""
    return {
        "employee_id": 1,
        "month": 1,
        "year": 2024,
        "basic_salary": 5000.00,
        "allowances": 500.00,
        "deductions": 200.00,
        "gross_salary": 5300.00,
        "net_salary": 5100.00,
        "is_paid": False
    }


@pytest.fixture
def mock_qt_application():
    """تطبيق Qt وهمي للاختبار"""
    from PySide6.QtWidgets import QApplication
    
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    yield app
    
    # تنظيف
    if app:
        app.quit()


@pytest.fixture
def mock_file_system(tmp_path):
    """نظام ملفات وهمي للاختبار"""
    # إنشاء هيكل مجلدات وهمي
    test_dirs = [
        "src/styles",
        "src/assets",
        "backups",
        "logs",
        "reports"
    ]
    
    for dir_path in test_dirs:
        (tmp_path / dir_path).mkdir(parents=True, exist_ok=True)
    
    # إنشاء ملفات وهمية
    test_files = {
        "src/styles/main.qss": "/* Test stylesheet */",
        "src/styles/dark.qss": "/* Dark theme stylesheet */",
        "config.json": '{"test": true}',
        "README.md": "# Test Project"
    }
    
    for file_path, content in test_files.items():
        (tmp_path / file_path).write_text(content, encoding="utf-8")
    
    return tmp_path


@pytest.fixture
def mock_database_operations():
    """عمليات قاعدة البيانات الوهمية"""
    operations = Mock()
    
    # إعداد العمليات الأساسية
    operations.create_employee.return_value = 1
    operations.update_employee.return_value = True
    operations.delete_employee.return_value = True
    operations.get_employee.return_value = None
    operations.list_employees.return_value = []
    
    operations.create_department.return_value = 1
    operations.update_department.return_value = True
    operations.delete_department.return_value = True
    operations.get_department.return_value = None
    operations.list_departments.return_value = []
    
    operations.create_job_title.return_value = 1
    operations.update_job_title.return_value = True
    operations.delete_job_title.return_value = True
    operations.get_job_title.return_value = None
    operations.list_job_titles.return_value = []
    
    return operations


@pytest.fixture(autouse=True)
def mock_logging():
    """تعطيل التسجيل أثناء الاختبارات"""
    with patch('src.main.logging'):
        yield


@pytest.fixture
def mock_message_box():
    """صندوق رسائل وهمي"""
    with patch('src.utils.ui_helpers.QMessageBox') as mock:
        mock.information.return_value = None
        mock.warning.return_value = None
        mock.critical.return_value = None
        mock.question.return_value = mock.Yes
        yield mock


# إعدادات pytest
def pytest_configure(config):
    """إعداد pytest"""
    # إضافة علامات مخصصة
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "ui: marks tests as UI tests"
    )


def pytest_collection_modifyitems(config, items):
    """تعديل عناصر الاختبار"""
    # إضافة علامة slow للاختبارات التي تحتوي على "slow" في الاسم
    for item in items:
        if "slow" in item.nodeid:
            item.add_marker(pytest.mark.slow)
        
        # إضافة علامة integration للاختبارات التي تحتوي على "integration"
        if "integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        
        # إضافة علامة ui لاختبارات الواجهات
        if "test_views" in item.nodeid or "ui" in item.nodeid:
            item.add_marker(pytest.mark.ui)
