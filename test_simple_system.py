#!/usr/bin/env python3
"""
اختبار بسيط للنظام
Simple System Test
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
    from PySide6.QtCore import Qt
    
    print("✅ تم استيراد PySide6 بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد PySide6: {e}")
    sys.exit(1)

def test_simple_window():
    """اختبار نافذة بسيطة"""
    
    class SimpleWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار النظام البسيط")
            self.setGeometry(100, 100, 800, 600)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            layout = QVBoxLayout(central_widget)
            layout.setContentsMargins(50, 50, 50, 50)
            layout.setSpacing(20)
            
            # عنوان الاختبار
            title = QLabel("🧪 اختبار النظام البسيط")
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    padding: 20px;
                    background-color: #3498db;
                    color: white;
                    border-radius: 10px;
                }
            """)
            layout.addWidget(title)
            
            # رسالة النجاح
            success_msg = QLabel("✅ النظام يعمل بشكل صحيح!")
            success_msg.setAlignment(Qt.AlignCenter)
            success_msg.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    color: #27ae60;
                    padding: 15px;
                    background-color: #d5f4e6;
                    border-radius: 8px;
                    border: 2px solid #27ae60;
                }
            """)
            layout.addWidget(success_msg)
            
            # معلومات النظام
            info = QLabel("""
            📋 معلومات الاختبار:
            
            ✅ PySide6 يعمل بشكل صحيح
            ✅ النوافذ تظهر بشكل طبيعي
            ✅ الأنماط CSS تعمل
            ✅ النصوص العربية تظهر بشكل صحيح
            ✅ التخطيط يعمل بشكل مناسب
            
            🎯 الخطوة التالية: اختبار النظام الكامل
            """)
            info.setAlignment(Qt.AlignLeft)
            info.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #2c3e50;
                    padding: 20px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    border: 1px solid #dee2e6;
                    line-height: 1.6;
                }
            """)
            layout.addWidget(info)
            
            layout.addStretch()
    
    return SimpleWindow()


def test_imports():
    """اختبار الاستيرادات"""
    print("🧪 اختبار الاستيرادات...")
    
    try:
        from src.config import get_config
        print("✅ تم استيراد config بنجاح")
    except ImportError as e:
        print(f"❌ خطأ في استيراد config: {e}")
        return False
    
    try:
        from src.utils import set_font_size
        print("✅ تم استيراد utils بنجاح")
    except ImportError as e:
        print(f"❌ خطأ في استيراد utils: {e}")
        return False
    
    try:
        from src.widgets.enhanced_dashboard import EnhancedDashboard
        print("✅ تم استيراد enhanced_dashboard بنجاح")
    except ImportError as e:
        print(f"❌ خطأ في استيراد enhanced_dashboard: {e}")
        return False
    
    try:
        from src.views.sidebar import Sidebar
        print("✅ تم استيراد sidebar بنجاح")
    except ImportError as e:
        print(f"❌ خطأ في استيراد sidebar: {e}")
        return False
    
    return True


def test_main_window():
    """اختبار النافذة الرئيسية"""
    print("🧪 اختبار النافذة الرئيسية...")
    
    try:
        from src.views.main_window import MainWindow
        print("✅ تم استيراد MainWindow بنجاح")
        return True
    except ImportError as e:
        print(f"❌ خطأ في استيراد MainWindow: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في إنشاء MainWindow: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار النظام البسيط")
    print("=" * 50)
    
    # اختبار الاستيرادات
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيرادات")
        return 1
    
    # اختبار النافذة الرئيسية
    if not test_main_window():
        print("\n❌ فشل في اختبار النافذة الرئيسية")
        return 1
    
    print("\n✅ جميع الاختبارات نجحت!")
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            enhanced_style_file = styles_dir / "enhanced_dashboard.qss"
            
            combined_styles = ""
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read() + "\n"
                    
            if enhanced_style_file.exists():
                with open(enhanced_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read()
                    
            app.setStyleSheet(combined_styles)
            print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # إنشاء نافذة الاختبار
        window = test_simple_window()
        window.show()
        
        print("✅ تم فتح نافذة الاختبار البسيط!")
        print("\n🎯 إذا ظهرت النافذة بشكل صحيح، فالنظام يعمل!")
        print("🔄 الآن يمكنك تجربة النظام الكامل بتشغيل: python run_hr_system.py")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
