#!/usr/bin/env python3
"""
اختبار نظام الخطوط المحسن
Test Enhanced Font System
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                               QWidget, QPushButton, QLabel, QLineEdit, QComboBox, 
                               QTableWidget, QGroupBox, QCheckBox, QSlider, QSpinBox)
from PySide6.QtCore import Qt


def create_font_test_window():
    """إنشاء نافذة اختبار الخطوط المحسنة"""
    
    class EnhancedFontTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار نظام الخطوط المحسن")
            self.setGeometry(100, 100, 900, 700)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            
            # العنوان
            title = QLabel("🔤 اختبار نظام الخطوط المحسن")
            title.setObjectName("app_title")
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet("color: #2c3e50; margin: 15px;")
            main_layout.addWidget(title)
            
            # شريط التحكم في الخط
            font_control_group = QGroupBox("التحكم في حجم الخط")
            font_control_layout = QHBoxLayout(font_control_group)
            
            # منزلق حجم الخط
            font_control_layout.addWidget(QLabel("حجم الخط:"))
            
            self.font_slider = QSlider(Qt.Horizontal)
            self.font_slider.setMinimum(8)
            self.font_slider.setMaximum(72)  # زيادة الحد الأقصى إلى 72
            self.font_slider.setValue(12)
            self.font_slider.valueChanged.connect(self.on_font_size_changed)
            font_control_layout.addWidget(self.font_slider)
            
            self.font_size_label = QLabel("12")
            self.font_size_label.setMinimumWidth(30)
            font_control_layout.addWidget(self.font_size_label)
            
            # أزرار سريعة
            small_btn = QPushButton("صغير (10)")
            medium_btn = QPushButton("متوسط (16)")
            large_btn = QPushButton("كبير (24)")
            xlarge_btn = QPushButton("كبير جداً (36)")
            xxlarge_btn = QPushButton("عملاق (48)")

            small_btn.clicked.connect(lambda: self.set_font_size(10))
            medium_btn.clicked.connect(lambda: self.set_font_size(16))
            large_btn.clicked.connect(lambda: self.set_font_size(24))
            xlarge_btn.clicked.connect(lambda: self.set_font_size(36))
            xxlarge_btn.clicked.connect(lambda: self.set_font_size(48))
            
            font_control_layout.addWidget(small_btn)
            font_control_layout.addWidget(medium_btn)
            font_control_layout.addWidget(large_btn)
            font_control_layout.addWidget(xlarge_btn)
            font_control_layout.addWidget(xxlarge_btn)
            
            main_layout.addWidget(font_control_group)
            
            # مجموعة الأزرار
            buttons_group = QGroupBox("الأزرار")
            buttons_layout = QHBoxLayout(buttons_group)
            
            btn1 = QPushButton("زر عادي")
            btn2 = QPushButton("زر الشريط الجانبي")
            btn2.setObjectName("sidebar_button")
            btn3 = QPushButton("زر مهم")
            btn3.setStyleSheet("background-color: #3498db; color: white;")
            
            buttons_layout.addWidget(btn1)
            buttons_layout.addWidget(btn2)
            buttons_layout.addWidget(btn3)
            main_layout.addWidget(buttons_group)
            
            # مجموعة التسميات والحقول
            inputs_group = QGroupBox("التسميات وحقول الإدخال")
            inputs_layout = QVBoxLayout(inputs_group)
            
            # تسميات
            label1 = QLabel("تسمية عادية")
            label2 = QLabel("تسمية مهمة")
            label2.setStyleSheet("font-weight: bold; color: #e74c3c;")
            
            inputs_layout.addWidget(label1)
            inputs_layout.addWidget(label2)
            
            # حقول إدخال
            line_edit = QLineEdit("حقل نص")
            combo_box = QComboBox()
            combo_box.addItems(["خيار 1", "خيار 2", "خيار 3", "خيار طويل جداً"])
            
            inputs_layout.addWidget(line_edit)
            inputs_layout.addWidget(combo_box)
            main_layout.addWidget(inputs_group)
            
            # مجموعة الجدول
            table_group = QGroupBox("الجداول")
            table_layout = QVBoxLayout(table_group)
            
            table = QTableWidget(4, 3)
            table.setHorizontalHeaderLabels(["العمود الأول", "العمود الثاني", "العمود الثالث"])
            
            # ملء الجدول ببيانات تجريبية
            for row in range(4):
                for col in range(3):
                    table.setItem(row, col, QTableWidget().item())
                    table.item(row, col).setText(f"خلية {row+1}-{col+1}")
            
            table.setMaximumHeight(150)
            table_layout.addWidget(table)
            main_layout.addWidget(table_group)
            
            # مجموعة العناصر الأخرى
            others_group = QGroupBox("عناصر أخرى")
            others_layout = QVBoxLayout(others_group)
            
            checkbox1 = QCheckBox("مربع اختيار أول")
            checkbox2 = QCheckBox("مربع اختيار ثاني")
            checkbox1.setChecked(True)
            
            spin_box = QSpinBox()
            spin_box.setMinimum(0)
            spin_box.setMaximum(100)
            spin_box.setValue(50)
            
            others_layout.addWidget(checkbox1)
            others_layout.addWidget(checkbox2)
            others_layout.addWidget(QLabel("مربع رقمي:"))
            others_layout.addWidget(spin_box)
            main_layout.addWidget(others_group)
            
            # معلومات الحالة
            self.status_label = QLabel("جاهز - حجم الخط الحالي: 12px")
            self.status_label.setStyleSheet("background-color: #ecf0f1; padding: 8px; border-radius: 4px;")
            main_layout.addWidget(self.status_label)
            
        def set_font_size(self, size):
            """تعيين حجم خط محدد"""
            self.font_slider.setValue(size)
            
        def on_font_size_changed(self, value):
            """معالج تغيير حجم الخط"""
            self.font_size_label.setText(str(value))
            
            # استخدام مدير الخطوط المحسن
            try:
                from src.utils.font_manager import get_font_manager
                font_manager = get_font_manager()
                font_manager.set_font_size(value)
                
                self.status_label.setText(f"تم تطبيق حجم الخط: {value}px على جميع العناصر")
                
            except Exception as e:
                self.status_label.setText(f"خطأ في تطبيق الخط: {e}")
                print(f"خطأ في تطبيق الخط: {e}")
    
    return EnhancedFontTestWindow()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نظام الخطوط المحسن")
    print("=" * 60)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط الأساسية
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    app.setStyleSheet(f.read())
                print("✅ تم تحميل الأنماط الأساسية")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # تهيئة مدير الخطوط
        try:
            from src.utils.font_manager import get_font_manager
            font_manager = get_font_manager()
            font_manager.load_font_size_from_config()
            print("✅ تم تهيئة مدير الخطوط")
        except Exception as e:
            print(f"⚠️ فشل في تهيئة مدير الخطوط: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_font_test_window()
        window.show()
        
        print("✅ تم فتح نافذة اختبار الخطوط المحسنة!")
        print("\n🎯 يمكنك الآن:")
        print("1. استخدام منزلق حجم الخط لتغيير الحجم")
        print("2. استخدام الأزرار السريعة (صغير/متوسط/كبير/كبير جداً/عملاق)")
        print("3. مراقبة تطبيق التغييرات على جميع العناصر فوراً")
        print("4. اختبار أحجام خطوط مختلفة (8-72)")
        print("5. مشاهدة تحديث شريط الحالة")
        print("6. اختبار الأحجام الكبيرة للعروض التقديمية")
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
