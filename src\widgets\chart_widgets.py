"""
مكونات الرسومات البيانية التفاعلية
Interactive Chart Widgets
"""

import math
from typing import List, Tuple, Optional
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame
)
from PySide6.QtCore import Qt, QRect, QPoint, QTimer, Signal, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QPainter, QPen, QBrush, QColor, QFont, QFontMetrics, QPainterPath


class AnimatedProgressRing(QWidget):
    """حلقة تقدم متحركة مع ربط بيانات محسن"""

    value_changed = Signal(float)

    def __init__(self, title: str, value: float, max_value: float, color: str, parent=None):
        super().__init__(parent)
        self.title = title
        self._current_value = 0.0
        self._target_value = value
        self._max_value = max_value
        self.color = QColor(color)
        self.hover_color = self.color.lighter(120)
        self.is_hovered = False

        # إعداد الحجم
        self.setFixedSize(180, 180)
        self.setMouseTracking(True)

        # إعداد الرسوم المتحركة
        self.animation = QPropertyAnimation(self, b"current_value")
        self.animation.setDuration(1500)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        self.animation.valueChanged.connect(self.update)

    def update_value(self, new_value: float, new_max: float = None):
        """تحديث القيمة مع رسوم متحركة"""
        if new_max is not None:
            self._max_value = new_max
        self._target_value = new_value

        # بدء الرسوم المتحركة
        self.animation.stop()
        self.animation.setStartValue(self._current_value)
        self.animation.setEndValue(self._target_value)
        self.animation.start()

    @property
    def current_value(self) -> float:
        """الحصول على القيمة الحالية"""
        return self._current_value

    @current_value.setter
    def current_value(self, value: float):
        """تعيين القيمة الحالية"""
        self._current_value = value
        self.value_changed.emit(value)
        self.update()

    @property
    def target_value(self) -> float:
        return self._target_value

    @property
    def max_value(self) -> float:
        return self._max_value
        
    def enterEvent(self, event):
        """عند دخول الماوس"""
        self.is_hovered = True
        self.update()
        
    def leaveEvent(self, event):
        """عند خروج الماوس"""
        self.is_hovered = False
        self.update()
        
    def paintEvent(self, event):
        """رسم الحلقة"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # إعداد المتغيرات
        rect = self.rect()
        center = rect.center()
        radius = min(rect.width(), rect.height()) // 2 - 20
        inner_radius = radius - 15
        
        # رسم الخلفية
        painter.setPen(QPen(QColor(240, 240, 240), 15, Qt.SolidLine, Qt.RoundCap))
        painter.drawEllipse(center, radius, radius)
        
        # حساب النسبة المئوية
        percentage = (self.current_value / self.max_value) if self.max_value > 0 else 0
        angle = int(360 * percentage)
        
        # رسم التقدم
        current_color = self.hover_color if self.is_hovered else self.color
        painter.setPen(QPen(current_color, 15, Qt.SolidLine, Qt.RoundCap))
        painter.drawArc(center.x() - radius, center.y() - radius, 
                       radius * 2, radius * 2, 90 * 16, -angle * 16)
        
        # رسم النص في الوسط
        painter.setPen(QPen(QColor(60, 60, 60)))
        
        # القيمة الرئيسية
        font = QFont("Arial", 18, QFont.Bold)
        painter.setFont(font)
        value_text = f"{int(self.current_value)}"
        painter.drawText(rect, Qt.AlignCenter, value_text)
        
        # العنوان
        font = QFont("Arial", 10, QFont.Normal)
        painter.setFont(font)
        title_rect = QRect(rect.x(), rect.y() + rect.height() // 2 + 20, 
                          rect.width(), 30)
        painter.drawText(title_rect, Qt.AlignCenter, self.title)
        
        # النسبة المئوية
        percentage_text = f"{percentage * 100:.1f}%"
        percentage_rect = QRect(rect.x(), rect.y() + rect.height() // 2 - 40, 
                               rect.width(), 20)
        painter.drawText(percentage_rect, Qt.AlignCenter, percentage_text)


class AnimatedBarChart(QWidget):
    """رسم بياني بالأعمدة متحرك مع ربط بيانات محسن"""

    def __init__(self, title: str, data: List[Tuple[str, float]], colors: List[str], parent=None):
        super().__init__(parent)
        self.title = title
        self.data = data
        self.colors = [QColor(color) for color in colors]
        self.current_values = [0.0] * len(data)
        self.target_values = [value for _, value in data]
        self.max_value = max(self.target_values) if self.target_values else 1
        self.hovered_bar = -1

        # إعداد الحجم
        self.setFixedSize(400, 250)
        self.setMouseTracking(True)

        # إعداد الرسوم المتحركة
        self.animations = []
        self._setup_animations()

    def _setup_animations(self):
        """إعداد الرسوم المتحركة"""
        self.animations.clear()
        for i in range(len(self.data)):
            animation = QPropertyAnimation(self, b"dummy")  # خاصية وهمية
            animation.setDuration(1200 + i * 150)
            animation.setEasingCurve(QEasingCurve.OutBounce)
            animation.valueChanged.connect(lambda value, idx=i: self._update_bar_value(idx, value))
            self.animations.append(animation)

    def _update_bar_value(self, index: int, value: float):
        """تحديث قيمة عمود محدد"""
        if 0 <= index < len(self.current_values):
            self.current_values[index] = value
            self.update()

    def update_data(self, new_data: List[Tuple[str, float]]):
        """تحديث البيانات مع رسوم متحركة"""
        self.data = new_data
        self.target_values = [value for _, value in new_data]
        self.max_value = max(self.target_values) if self.target_values else 1
        self.current_values = [0.0] * len(new_data)

        # إعادة إعداد الرسوم المتحركة
        self._setup_animations()
        self.start_animations()
        
    def start_animations(self):
        """بدء الرسوم المتحركة"""
        for i, animation in enumerate(self.animations):
            if i < len(self.target_values):
                animation.setStartValue(0.0)
                animation.setEndValue(self.target_values[i])
                animation.finished.connect(lambda: self.update())
                animation.start()
            
    def get_animated_value(self, index: int) -> float:
        """الحصول على القيمة المتحركة"""
        return self.current_values[index] if index < len(self.current_values) else 0.0
        
    def set_animated_value(self, index: int, value: float):
        """تعيين القيمة المتحركة"""
        if index < len(self.current_values):
            self.current_values[index] = value
            
    def mouseMoveEvent(self, event):
        """تتبع حركة الماوس"""
        if not self.data:
            return
            
        # حساب العمود المحدد
        bar_width = (self.width() - 60) // len(self.data)
        bar_index = (event.pos().x() - 30) // bar_width
        
        if 0 <= bar_index < len(self.data):
            self.hovered_bar = bar_index
        else:
            self.hovered_bar = -1
            
        self.update()
        
    def leaveEvent(self, event):
        """عند خروج الماوس"""
        self.hovered_bar = -1
        self.update()
        
    def paintEvent(self, event):
        """رسم الأعمدة"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        rect = self.rect()
        
        # رسم العنوان
        painter.setPen(QPen(QColor(60, 60, 60)))
        font = QFont("Arial", 12, QFont.Bold)
        painter.setFont(font)
        title_rect = QRect(0, 0, rect.width(), 30)
        painter.drawText(title_rect, Qt.AlignCenter, self.title)
        
        if not self.data:
            return
            
        # إعداد منطقة الرسم
        chart_rect = QRect(30, 40, rect.width() - 60, rect.height() - 70)
        bar_width = chart_rect.width() // len(self.data)
        
        # رسم الأعمدة
        for i, ((label, _), current_value) in enumerate(zip(self.data, self.current_values)):
            # حساب ارتفاع العمود
            bar_height = int((current_value / self.max_value) * chart_rect.height()) if self.max_value > 0 else 0
            
            # موقع العمود
            bar_x = chart_rect.x() + i * bar_width + bar_width // 4
            bar_y = chart_rect.bottom() - bar_height
            bar_rect_width = bar_width // 2
            
            # لون العمود
            color = self.colors[i % len(self.colors)]
            if i == self.hovered_bar:
                color = color.lighter(130)
                
            # رسم العمود
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(color.darker(120), 2))
            painter.drawRoundedRect(bar_x, bar_y, bar_rect_width, bar_height, 5, 5)
            
            # رسم القيمة فوق العمود
            if bar_height > 0:
                painter.setPen(QPen(QColor(60, 60, 60)))
                font = QFont("Arial", 9, QFont.Bold)
                painter.setFont(font)
                value_rect = QRect(bar_x - 10, bar_y - 20, bar_rect_width + 20, 15)
                painter.drawText(value_rect, Qt.AlignCenter, f"{int(current_value)}")
            
            # رسم التسمية
            painter.setPen(QPen(QColor(80, 80, 80)))
            font = QFont("Arial", 8, QFont.Normal)
            painter.setFont(font)
            label_rect = QRect(bar_x - 15, chart_rect.bottom() + 5, bar_rect_width + 30, 20)
            painter.drawText(label_rect, Qt.AlignCenter, label)


class StatisticsChartWidget(QFrame):
    """مكون الرسومات البيانية الإحصائية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("statistics_chart_widget")
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setMinimumHeight(400)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # عنوان القسم
        title_label = QLabel("📊 الإحصائيات التفاعلية")
        title_label.setObjectName("chart_section_title")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # منطقة الرسومات البيانية - ترتيب محسن
        charts_container = QVBoxLayout()
        charts_container.setSpacing(25)

        # الصف الأول: الحلقات الدائرية
        rings_section = QVBoxLayout()

        # عنوان فرعي للحلقات
        rings_title = QLabel("📊 المؤشرات الرئيسية")
        rings_title.setObjectName("subsection_title")
        rings_title.setAlignment(Qt.AlignCenter)
        rings_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                margin: 10px 0;
            }
        """)
        rings_section.addWidget(rings_title)

        # الحلقات في صف واحد
        rings_layout = QHBoxLayout()
        rings_layout.setSpacing(40)
        rings_layout.setContentsMargins(20, 10, 20, 10)

        # إنشاء الحلقات مع بيانات افتراضية
        self.employee_ring = AnimatedProgressRing("إجمالي الموظفين", 0, 100, "#3498db")
        self.department_ring = AnimatedProgressRing("الأقسام", 0, 15, "#27ae60")
        self.salary_ring = AnimatedProgressRing("الرواتب (بالآلاف)", 0, 1000, "#f39c12")

        rings_layout.addStretch()
        rings_layout.addWidget(self.employee_ring)
        rings_layout.addWidget(self.department_ring)
        rings_layout.addWidget(self.salary_ring)
        rings_layout.addStretch()

        rings_section.addLayout(rings_layout)

        # الصف الثاني: الرسم البياني بالأعمدة
        bar_section = QVBoxLayout()

        # عنوان فرعي للرسم البياني
        bar_title = QLabel("📈 التوزيع التفصيلي")
        bar_title.setObjectName("subsection_title")
        bar_title.setAlignment(Qt.AlignCenter)
        bar_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                margin: 10px 0;
            }
        """)
        bar_section.addWidget(bar_title)

        # الرسم البياني بالأعمدة مع بيانات افتراضية
        bar_data = [
            ("الموظفين", 0),
            ("الأقسام", 0),
            ("المناصب", 0),
            ("السلف", 0)
        ]
        bar_colors = ["#3498db", "#27ae60", "#9b59b6", "#e74c3c"]
        self.bar_chart = AnimatedBarChart("", bar_data, bar_colors)

        # وضع الرسم البياني في الوسط
        bar_container = QHBoxLayout()
        bar_container.addStretch()
        bar_container.addWidget(self.bar_chart)
        bar_container.addStretch()

        bar_section.addLayout(bar_container)

        # إضافة الأقسام إلى الحاوية الرئيسية
        charts_container.addLayout(rings_section)
        charts_container.addSpacing(20)
        charts_container.addLayout(bar_section)

        main_layout.addLayout(charts_container)
        
    def update_data(self, employee_count: int, department_count: int,
                   total_salary: float, advance_count: int, job_title_count: int):
        """تحديث البيانات"""
        print(f"🔄 تحديث البيانات: موظفين={employee_count}, أقسام={department_count}, رواتب={total_salary}, سلف={advance_count}, مناصب={job_title_count}")

        # تحديث الحلقات
        self.employee_ring.target_value = employee_count
        self.employee_ring.max_value = max(100, employee_count + 20)  # تحديث الحد الأقصى
        self.employee_ring.start_animation()

        self.department_ring.target_value = department_count
        self.department_ring.max_value = max(15, department_count + 5)  # تحديث الحد الأقصى
        self.department_ring.start_animation()

        self.salary_ring.target_value = total_salary / 1000  # بالآلاف
        self.salary_ring.max_value = max(1000, (total_salary / 1000) + 200)  # تحديث الحد الأقصى
        self.salary_ring.start_animation()

        # تحديث الرسم البياني
        new_data = [
            ("الموظفين", employee_count),
            ("الأقسام", department_count),
            ("المناصب", job_title_count),
            ("السلف", advance_count)
        ]

        # تحديث بيانات الرسم البياني
        self.bar_chart.data = new_data
        self.bar_chart.target_values = [value for _, value in new_data]
        self.bar_chart.current_values = [0.0] * len(new_data)  # إعادة تعيين القيم الحالية
        self.bar_chart.max_value = max(self.bar_chart.target_values) if self.bar_chart.target_values else 1

        # إعادة إنشاء الرسوم المتحركة
        self.bar_chart.animations.clear()
        for i in range(len(new_data)):
            animation = QPropertyAnimation(self.bar_chart, f"animatedValue{i}".encode())
            animation.setDuration(1500 + i * 200)
            animation.setEasingCurve(QEasingCurve.OutBounce)
            animation.valueChanged.connect(lambda value, idx=i: self.bar_chart.set_animated_value(idx, value))
            animation.valueChanged.connect(self.bar_chart.update)
            self.bar_chart.animations.append(animation)

        self.bar_chart.start_animations()
