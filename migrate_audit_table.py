"""
سكريبت لترقية جدول audit_logs بإضافة الأعمدة الجديدة
Migration script for audit_logs table
"""

import sqlite3
import os
from src.database.connection import db_manager

def migrate_audit_table():
    """ترقية جدول audit_logs بإضافة الأعمدة الجديدة"""
    print("🔄 بدء ترقية جدول audit_logs...")
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        # الاتصال بقاعدة البيانات مباشرة
        db_path = "hr_system.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود الأعمدة الجديدة
        cursor.execute("PRAGMA table_info(audit_logs)")
        columns = [column[1] for column in cursor.fetchall()]
        
        print(f"📋 الأعمدة الموجودة حالياً: {columns}")
        
        # قائمة الأعمدة الجديدة المطلوب إضافتها
        new_columns = [
            ("session_id", "TEXT"),
            ("request_id", "TEXT"), 
            ("execution_time", "REAL"),
            ("error_message", "TEXT"),
            ("additional_data", "TEXT")
        ]
        
        # إضافة الأعمدة الجديدة إذا لم تكن موجودة
        for column_name, column_type in new_columns:
            if column_name not in columns:
                try:
                    alter_sql = f"ALTER TABLE audit_logs ADD COLUMN {column_name} {column_type}"
                    cursor.execute(alter_sql)
                    print(f"✅ تم إضافة العمود: {column_name}")
                except sqlite3.OperationalError as e:
                    print(f"⚠️ خطأ في إضافة العمود {column_name}: {e}")
            else:
                print(f"ℹ️ العمود {column_name} موجود بالفعل")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("✅ تم الانتهاء من ترقية جدول audit_logs بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في ترقية الجدول: {e}")
        return False

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        import shutil
        from datetime import datetime
        
        db_path = "hr_system.db"
        if os.path.exists(db_path):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"hr_system_backup_{timestamp}.db"
            shutil.copy2(db_path, backup_path)
            print(f"💾 تم إنشاء نسخة احتياطية: {backup_path}")
            return backup_path
        else:
            print("ℹ️ لا توجد قاعدة بيانات للنسخ الاحتياطي")
            return None
    except Exception as e:
        print(f"⚠️ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def recreate_database():
    """إعادة إنشاء قاعدة البيانات من الصفر"""
    try:
        print("🔄 إعادة إنشاء قاعدة البيانات...")
        
        # حذف قاعدة البيانات القديمة
        db_path = "hr_system.db"
        if os.path.exists(db_path):
            os.remove(db_path)
            print("🗑️ تم حذف قاعدة البيانات القديمة")
        
        # إنشاء قاعدة بيانات جديدة
        db_manager.initialize()
        print("✅ تم إنشاء قاعدة البيانات الجديدة بنجاح!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعادة إنشاء قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء عملية ترقية نظام التدقيق")
    print("=" * 50)
    
    # إنشاء نسخة احتياطية أولاً
    backup_path = backup_database()
    
    # محاولة الترقية أولاً
    print("\n1️⃣ محاولة ترقية الجدول الموجود...")
    if migrate_audit_table():
        print("🎉 تم الانتهاء من الترقية بنجاح!")
        return True
    
    # إذا فشلت الترقية، اسأل المستخدم عن إعادة الإنشاء
    print("\n2️⃣ فشلت الترقية. هل تريد إعادة إنشاء قاعدة البيانات؟")
    print("⚠️ تحذير: سيتم فقدان جميع البيانات الموجودة!")
    
    response = input("اكتب 'نعم' للمتابعة أو أي شيء آخر للإلغاء: ").strip().lower()
    
    if response in ['نعم', 'yes', 'y']:
        if recreate_database():
            print("🎉 تم إعادة إنشاء قاعدة البيانات بنجاح!")
            return True
        else:
            print("❌ فشل في إعادة إنشاء قاعدة البيانات")
            return False
    else:
        print("❌ تم إلغاء العملية")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 يمكنك:")
        print("1. تشغيل السكريبت مرة أخرى")
        print("2. حذف ملف hr_system.db يدوياً وإعادة تشغيل النظام")
        print("3. استعادة النسخة الاحتياطية إذا كانت متوفرة")
