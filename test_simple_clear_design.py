#!/usr/bin/env python3
"""
اختبار التصميم البسيط والواضح
Test Simple and Clear Design
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QFrame, QHBoxLayout, QPushButton
from PySide6.QtCore import Qt


def create_simple_clear_design_test():
    """إنشاء نافذة اختبار التصميم البسيط والواضح"""
    
    class SimpleClearDesignTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار التصميم البسيط والواضح")
            self.setGeometry(100, 100, 1300, 800)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            # إنشاء الشريط العلوي البسيط والواضح
            self.create_simple_clear_header()
            main_layout.addWidget(self.header)
            
            # محتوى الاختبار
            content_widget = QWidget()
            content_layout = QVBoxLayout(content_widget)
            content_layout.setContentsMargins(30, 30, 30, 30)
            content_layout.setSpacing(25)
            
            # عنوان الاختبار
            test_title = QLabel("🎯 اختبار التصميم البسيط والواضح")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 26px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 20px;
                    padding: 20px;
                    background-color: #3498db;
                    color: white;
                    border-radius: 12px;
                }
            """)
            content_layout.addWidget(test_title)
            
            # معلومات التصميم البسيط
            simple_info = QLabel("""
            🎯 التصميم البسيط والواضح الجديد:
            
            🏢 اسم البرنامج المحسن:
            • حجم خط كبير وواضح: 32px (زيادة من 28px)
            • خلفية رمادية فاتحة (#ecf0f1) للوضوح
            • حدود زرقاء سميكة (3px) للتمييز
            • حشو كبير (15px 20px) للراحة البصرية
            • زوايا مدورة (12px) للمظهر الناعم
            • لون نص داكن (#2c3e50) للوضوح التام
            
            💼 العنوان الفرعي المتناسق:
            • حجم خط متوسط: 16px
            • وزن خط عريض: 600
            • خلفية فاتحة (#f8f9fa) للتناسق
            • حدود جانبية زرقاء (4px) للربط البصري
            • حشو مناسب (8px 20px) للتوازن
            • لون نص متوسط (#34495e) للتدرج
            
            👤 بطاقة المستخدم البسيطة:
            • حجم مناسب: 250×70px (أصغر وأبسط)
            • خلفية رمادية داكنة (#34495e) للتباين
            • حدود داكنة (2px #2c3e50) للوضوح
            • زوايا مدورة (10px) للتناسق
            • أيقونة بسيطة (36×36px) مع خلفية داكنة
            • نصوص واضحة بألوان متباينة
            
            🎨 فلسفة التصميم البسيط:
            • الوضوح أهم من الجمال المعقد
            • التباين العالي للقراءة السهلة
            • الألوان الصلبة بدلاً من التدرجات
            • الحدود الواضحة للتمييز
            • الأحجام المناسبة للاستخدام العملي
            • التناسق في جميع العناصر
            
            📏 الأبعاد المحسنة:
            • اسم البرنامج: حشو 15px×20px للوضوح
            • العنوان الفرعي: حشو 8px×20px للتناسق
            • بطاقة المستخدم: 250×70px للبساطة
            • أيقونة المستخدم: 36×36px للوضوح
            • زر الإعدادات: 140×50px (بدون تغيير)
            
            🎯 الألوان الواضحة:
            • اسم البرنامج: #2c3e50 على #ecf0f1
            • العنوان الفرعي: #34495e على #f8f9fa
            • بطاقة المستخدم: #ecf0f1 على #34495e
            • الحدود: #3498db للتمييز والربط
            • الحالة: #2ecc71 للوضوح
            
            ✨ المزايا:
            • سهولة في القراءة والفهم
            • وضوح تام في جميع الظروف
            • تباين عالي للإتاحة
            • بساطة في الصيانة والتطوير
            • سرعة في التحميل والعرض
            • تناسق مثالي بين العناصر
            """)
            simple_info.setAlignment(Qt.AlignLeft)
            simple_info.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #2c3e50;
                    background-color: #f8f9fa;
                    padding: 25px;
                    border-radius: 12px;
                    border-left: 4px solid #3498db;
                    line-height: 1.6;
                    border: 1px solid #dee2e6;
                }
            """)
            content_layout.addWidget(simple_info)
            
            # مقارنة التصاميم
            comparison_frame = QFrame()
            comparison_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 2px solid #dee2e6;
                    border-radius: 12px;
                    padding: 20px;
                }
            """)
            
            comparison_layout = QVBoxLayout(comparison_frame)
            
            comparison_title = QLabel("📊 مقارنة التصاميم")
            comparison_title.setAlignment(Qt.AlignCenter)
            comparison_title.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 15px;
                    padding: 10px;
                    background-color: #ecf0f1;
                    border-radius: 8px;
                }
            """)
            comparison_layout.addWidget(comparison_title)
            
            comparison_text = QLabel("""
            📏 جدول مقارنة التصاميم:
            
            ┌─────────────────────┬─────────────────┬─────────────────┐
            │ الخاصية             │ التصميم المعقد   │ التصميم البسيط   │
            ├─────────────────────┼─────────────────┼─────────────────┤
            │ حجم اسم البرنامج    │ 28px            │ 32px            │
            │ خلفية اسم البرنامج  │ تدرج معقد       │ رمادي فاتح      │
            │ حدود اسم البرنامج   │ 4px جانبي       │ 3px كامل        │
            │ حجم بطاقة المستخدم  │ 400×85px        │ 250×70px        │
            │ خلفية بطاقة المستخدم│ تدرج ملون       │ رمادي داكن      │
            │ عدد الأقسام         │ 3 أقسام        │ 2 قسم          │
            │ التعقيد             │ عالي            │ منخفض           │
            │ الوضوح             │ متوسط           │ عالي            │
            │ سهولة القراءة       │ جيد             │ ممتاز           │
            │ التباين اللوني      │ منخفض           │ عالي            │
            │ سرعة التحميل        │ بطيء            │ سريع            │
            │ سهولة الصيانة       │ صعب             │ سهل             │
            └─────────────────────┴─────────────────┴─────────────────┘
            
            ❌ مشاكل التصميم المعقد:
            • تدرجات لونية تشتت الانتباه
            • أحجام كبيرة تستهلك مساحة
            • تباين ضعيف يصعب القراءة
            • تعقيد في الكود والصيانة
            
            ✅ مزايا التصميم البسيط:
            • وضوح تام في جميع الظروف
            • تباين عالي للقراءة السهلة
            • بساطة في التطوير والصيانة
            • سرعة في التحميل والاستجابة
            • تناسق مثالي بين العناصر
            • احترافية في المظهر
            
            🎯 النتيجة: البساطة = الوضوح = الاحترافية!
            """)
            comparison_text.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    color: #34495e;
                    font-family: 'Courier New', monospace;
                    line-height: 1.4;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    border: 1px solid #dee2e6;
                }
            """)
            comparison_layout.addWidget(comparison_text)
            
            content_layout.addWidget(comparison_frame)
            content_layout.addStretch()
            
            main_layout.addWidget(content_widget)
            
        def create_simple_clear_header(self):
            """إنشاء الشريط العلوي البسيط والواضح"""
            self.header = QFrame()
            self.header.setObjectName("main_header_frame")
            self.header.setFixedHeight(100)

            header_layout = QHBoxLayout(self.header)
            header_layout.setContentsMargins(30, 20, 30, 20)
            header_layout.setSpacing(30)

            # قسم العنوان البسيط والواضح
            title_section = QVBoxLayout()
            title_section.setSpacing(8)

            # العنوان الرئيسي واضح ومميز
            main_title = QLabel("🏢 نظام إدارة الموارد البشرية")
            main_title.setAlignment(Qt.AlignLeft)
            main_title.setStyleSheet("""
                QLabel {
                    font-size: 32px;
                    font-weight: bold;
                    color: #2c3e50;
                    background-color: #ecf0f1;
                    padding: 15px 20px;
                    border-radius: 12px;
                    border: 3px solid #3498db;
                    margin: 5px 0px;
                }
            """)

            # العنوان الفرعي واضح ومتناسق
            subtitle = QLabel("💼 نظام شامل ومتطور لإدارة الموظفين والمعاملات المالية")
            subtitle.setAlignment(Qt.AlignLeft)
            subtitle.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    font-weight: 600;
                    color: #34495e;
                    padding: 8px 20px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    border-left: 4px solid #3498db;
                    margin: 5px 0px;
                }
            """)

            title_section.addWidget(main_title)
            title_section.addWidget(subtitle)

            # قسم معلومات المستخدم البسيط
            user_section = QHBoxLayout()
            user_section.setSpacing(20)

            # بطاقة المستخدم بطريقة بسيطة وواضحة
            user_card = QFrame()
            user_card.setFixedSize(250, 70)
            user_card.setStyleSheet("""
                QFrame {
                    background-color: #34495e;
                    border: 2px solid #2c3e50;
                    border-radius: 10px;
                }
            """)
            
            user_card_layout = QHBoxLayout(user_card)
            user_card_layout.setContentsMargins(15, 10, 15, 10)
            user_card_layout.setSpacing(12)

            # أيقونة المستخدم بسيطة
            user_icon = QLabel("👤")
            user_icon.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    color: #ecf0f1;
                    background-color: #2c3e50;
                    border-radius: 18px;
                    padding: 6px;
                    min-width: 36px;
                    max-width: 36px;
                    min-height: 36px;
                    max-height: 36px;
                }
            """)
            user_icon.setAlignment(Qt.AlignCenter)
            user_card_layout.addWidget(user_icon)
            
            # معلومات المستخدم بسيطة
            user_info_layout = QVBoxLayout()
            user_info_layout.setSpacing(2)
            
            # اسم المستخدم
            user_name = QLabel("المدير العام")
            user_name.setStyleSheet("""
                QLabel {
                    color: #ecf0f1;
                    font-size: 16px;
                    font-weight: bold;
                }
            """)
            user_info_layout.addWidget(user_name)
            
            # حالة بسيطة
            status_label = QLabel("🟢 متصل")
            status_label.setStyleSheet("""
                QLabel {
                    color: #2ecc71;
                    font-size: 12px;
                    font-weight: 500;
                }
            """)
            user_info_layout.addWidget(status_label)
            
            user_card_layout.addLayout(user_info_layout)
            user_card_layout.addStretch()

            # زر الإعدادات (نفس الشكل السابق)
            settings_btn = QPushButton("⚙️ الإعدادات")
            settings_btn.setFixedSize(140, 50)
            settings_btn.setToolTip("إعدادات النظام")
            settings_btn.clicked.connect(lambda: print("🔄 تم النقر على زر الإعدادات"))
            settings_btn.setStyleSheet("""
                QPushButton {
                    font-size: 14px;
                    font-weight: bold;
                    color: white;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #95a5a6, stop: 1 #7f8c8d);
                    border: none;
                    border-radius: 8px;
                    padding: 12px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #bdc3c7, stop: 1 #95a5a6);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #7f8c8d, stop: 1 #6c7b7d);
                }
            """)

            user_section.addWidget(user_card)
            user_section.addWidget(settings_btn)

            # تجميع الأقسام
            header_layout.addLayout(title_section)
            header_layout.addStretch()
            header_layout.addLayout(user_section)
    
    return SimpleClearDesignTest()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار التصميم البسيط والواضح")
    print("=" * 50)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            enhanced_style_file = styles_dir / "enhanced_dashboard.qss"
            
            combined_styles = ""
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read() + "\n"
                    
            if enhanced_style_file.exists():
                with open(enhanced_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read()
                    
            app.setStyleSheet(combined_styles)
            print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_simple_clear_design_test()
        window.show()
        
        print("✅ تم فتح نافذة اختبار التصميم البسيط!")
        print("\n🏢 اسم البرنامج المحسن:")
        print("• حجم خط كبير: 32px")
        print("• خلفية رمادية فاتحة واضحة")
        print("• حدود زرقاء سميكة (3px)")
        print("• حشو كبير للراحة البصرية")
        
        print("\n👤 بطاقة المستخدم البسيطة:")
        print("• حجم مناسب: 250×70px")
        print("• خلفية رمادية داكنة للتباين")
        print("• أيقونة بسيطة (36×36px)")
        print("• نصوص واضحة ومتباينة")
        
        print("\n🎯 فلسفة التصميم:")
        print("• الوضوح أهم من التعقيد")
        print("• التباين العالي للقراءة")
        print("• الألوان الصلبة الواضحة")
        print("• البساطة في الصيانة")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
