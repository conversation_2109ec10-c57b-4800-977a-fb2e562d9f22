#!/usr/bin/env python3
"""
اختبار حساب الرواتب
Test Salary Calculation
"""

import sys
from pathlib import Path
from datetime import date

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.database import db_manager, get_db_session_context
from src.models import Employee, SalaryRecord


def test_salary_calculation():
    """اختبار حساب الرواتب"""
    print("🧮 اختبار حساب الرواتب...")
    print("=" * 50)
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        current_month = date.today().month
        current_year = date.today().year
        
        print(f"📅 الشهر الحالي: {current_month}/{current_year}")
        
        with get_db_session_context() as session:
            # الحصول على جميع الموظفين النشطين
            employees = session.query(Employee).filter_by(is_active=True).all()
            print(f"👥 عدد الموظفين النشطين: {len(employees)}")
            
            if not employees:
                print("❌ لا يوجد موظفين نشطين")
                return False
            
            salary_records = []
            
            for employee in employees:
                print(f"\n👤 معالجة الموظف: {employee.full_name}")
                print(f"   الرقم الوظيفي: {employee.employee_number}")
                print(f"   الراتب الأساسي: {employee.basic_salary:,.0f} د.ع")
                print(f"   الراتب اليومي: {employee.daily_salary:,.0f} د.ع")
                
                # التحقق من وجود سجل راتب للفترة
                existing_record = SalaryRecord.exists_for_period(
                    session, employee.id, current_month, current_year
                )
                
                if existing_record:
                    print("   ⚠️ سجل الراتب موجود مسبقاً")
                    continue
                
                # حساب المعاملات المالية للشهر
                total_advances = employee.get_total_advances(current_month, current_year)
                total_deductions = employee.get_total_deductions(current_month, current_year)
                total_bonuses = employee.get_total_bonuses(current_month, current_year)
                total_market_debts = employee.get_total_debts(current_month, current_year)
                
                print(f"   💰 إجمالي السلف: {total_advances:,.0f} د.ع")
                print(f"   📉 إجمالي الخصومات: {total_deductions:,.0f} د.ع")
                print(f"   🎁 إجمالي المكافآت: {total_bonuses:,.0f} د.ع")
                print(f"   🏪 ديون الماركت: {total_market_debts:,.0f} د.ع")
                
                # إنشاء سجل راتب جديد
                salary_record = SalaryRecord(
                    employee_id=employee.id,
                    month=current_month,
                    year=current_year,
                    basic_salary=employee.basic_salary,
                    daily_salary=employee.daily_salary,
                    working_days=30,  # افتراضي
                    total_advances=total_advances,
                    total_deductions=total_deductions,
                    total_bonuses=total_bonuses,
                    total_market_debts=total_market_debts
                )
                
                # حساب الراتب
                salary_record.calculate_salary()
                
                print(f"   📊 إجمالي الراتب: {salary_record.gross_salary:,.0f} د.ع")
                print(f"   💵 صافي الراتب: {salary_record.net_salary:,.0f} د.ع")
                
                session.add(salary_record)
                salary_records.append(salary_record)
            
            # حفظ التغييرات
            session.commit()
            
            print(f"\n✅ تم حساب {len(salary_records)} راتب بنجاح!")
            
            # عرض النتائج
            print("\n📋 ملخص الرواتب:")
            print("-" * 50)
            for record in salary_records:
                print(f"👤 {record.employee.full_name}")
                print(f"   💰 الراتب الأساسي: {record.basic_salary:,.0f} د.ع")
                print(f"   📊 إجمالي الراتب: {record.gross_salary:,.0f} د.ع")
                print(f"   💵 صافي الراتب: {record.net_salary:,.0f} د.ع")
                print(f"   📅 الفترة: {record.month}/{record.year}")
                print()
            
            return True
            
    except Exception as e:
        print(f"\n❌ خطأ في حساب الرواتب: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """الدالة الرئيسية"""
    success = test_salary_calculation()
    
    if success:
        print("🎉 اختبار حساب الرواتب نجح!")
        print("\nيمكنك الآن:")
        print("1. تشغيل النظام: python run_hr_system.py")
        print("2. الذهاب إلى قسم الرواتب")
        print("3. عرض الرواتب المحسوبة")
        return 0
    else:
        print("💥 فشل اختبار حساب الرواتب!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
