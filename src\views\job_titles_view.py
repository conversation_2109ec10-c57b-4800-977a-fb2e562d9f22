"""
واجهة إدارة العناوين الوظيفية
Job Titles Management View
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QLabel, QFrame, QHeaderView, QAbstractItemView
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from ..utils import (
    apply_rtl_layout, setup_table_widget, populate_table, show_message,
    show_confirmation, format_currency
)
from ..database import get_db_session_context
from ..models import JobTitle


class JobTitlesView(QWidget):
    """واجهة إدارة العناوين الوظيفية"""
    
    # إشارات
    job_title_selected = Signal(int)  # إشارة اختيار عنوان وظيفي
    add_job_title_requested = Signal()  # إشارة طلب إضافة عنوان وظيفي
    edit_job_title_requested = Signal(int)  # إشارة طلب تعديل عنوان وظيفي
    
    def __init__(self):
        super().__init__()
        self.job_titles_data = []
        self.filtered_data = []
        
        self.setup_ui()
        self.setup_connections()
        self.load_job_titles()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setObjectName("job_titles_view")
        apply_rtl_layout(self)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # إنشاء رأس الصفحة المحسن
        self.create_enhanced_header(main_layout)

        # إنشاء شريط البحث والإحصائيات المدمج
        self.create_integrated_search_stats(main_layout)

        # إنشاء جدول العناوين الوظيفية المحسن
        self.create_enhanced_job_titles_table(main_layout)

        # إنشاء شريط الأزرار المدمج
        self.create_compact_action_buttons(main_layout)
        
    def create_enhanced_header(self, layout: QVBoxLayout):
        """إنشاء رأس الصفحة المحسن"""
        # إنشاء إطار العنوان الرئيسي المدمج
        header_frame = QFrame()
        header_frame.setObjectName("compact_header_frame")
        header_frame.setFixedHeight(120)  # ارتفاع محسن لتوفير مساحة أفضل للعناوين
        header_frame.setStyleSheet("""
            QFrame#compact_header_frame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                            stop: 0 #ed8936, stop: 1 #ffb366);
                border: 3px solid rgba(237, 137, 54, 0.6);
                border-radius: 15px;
                margin: 8px;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 8, 15, 8)
        header_layout.setSpacing(5)

        # العنوان الرئيسي المتغير
        self.main_title = QLabel("💼 إدارة العناوين الوظيفية")
        self.main_title.setAlignment(Qt.AlignCenter)
        self.main_title.setFixedHeight(60)  # ارتفاع محسن للعنوان الرئيسي
        self.main_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 26px;
                font-weight: bold;
                color: white;
                background-color: transparent;
                padding: 10px;
                margin: 0px;
            }
        """)

        # العنوان الفرعي المتغير
        self.subtitle = QLabel("🎯 إدارة المناصب والوظائف وتحديد نطاقات الرواتب")
        self.subtitle.setAlignment(Qt.AlignCenter)
        self.subtitle.setFixedHeight(40)  # ارتفاع محسن للعنوان الفرعي
        self.subtitle.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: rgba(255, 255, 255, 0.9);
                background-color: rgba(255, 255, 255, 0.1);
                padding: 8px 20px;
                border-radius: 10px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                margin: 5px;
            }
        """)

        # تجميع العناصر
        header_layout.addWidget(self.main_title)
        header_layout.addWidget(self.subtitle)

        layout.addWidget(header_frame)

    def update_page_title(self, title: str, subtitle: str):
        """تحديث عنوان الصفحة"""
        self.main_title.setText(title)
        self.subtitle.setText(subtitle)
        
    def create_integrated_search_stats(self, layout: QVBoxLayout):
        """إنشاء البحث والإحصائيات المدمجة"""
        search_frame = QFrame()
        search_frame.setObjectName("search_frame")
        search_frame.setStyleSheet("""
            QFrame#search_frame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)

        search_layout = QVBoxLayout(search_frame)
        search_layout.setSpacing(10)

        # الصف الأول: عنوان البحث مع الإحصائيات
        first_row = QHBoxLayout()

        # عنوان البحث
        search_title = QLabel("🔍 البحث والتصفية")
        search_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 13px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)

        # الإحصائيات المدمجة
        stats_layout = QHBoxLayout()

        # عداد العناوين الوظيفية
        self.job_titles_count_label = QLabel("📊 إجمالي: 0")
        self.job_titles_count_label.setFixedHeight(30)
        self.job_titles_count_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 6px 10px;
                border-radius: 4px;
                border: 1px solid #6f42c1;
                text-align: center;
            }
        """)

        # عداد الوظائف النشطة
        self.active_job_titles_label = QLabel("✅ نشط: 0")
        self.active_job_titles_label.setFixedHeight(30)
        self.active_job_titles_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #27ae60;
                background-color: #d5f4e6;
                padding: 6px 10px;
                border-radius: 4px;
                border: 1px solid #27ae60;
                text-align: center;
            }
        """)

        # عداد نطاقات الرواتب
        self.salary_ranges_label = QLabel("💰 نطاقات: 0")
        self.salary_ranges_label.setFixedHeight(30)
        self.salary_ranges_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #e67e22;
                background-color: #fdf2e9;
                padding: 6px 10px;
                border-radius: 4px;
                border: 1px solid #e67e22;
                text-align: center;
            }
        """)

        stats_layout.addWidget(self.job_titles_count_label)
        stats_layout.addWidget(self.active_job_titles_label)
        stats_layout.addWidget(self.salary_ranges_label)
        stats_layout.addStretch()

        first_row.addWidget(search_title)
        first_row.addStretch()
        first_row.addLayout(stats_layout)

        # الصف الثاني: حقل البحث
        second_row = QHBoxLayout()

        search_label = QLabel("🔎 البحث السريع:")
        search_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: 600;
                color: #495057;
                min-width: 100px;
            }
        """)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالعنوان أو الرمز...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                padding: 8px 12px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #6f42c1;
                background-color: #f8f9fa;
            }
        """)

        self.clear_search_btn = QPushButton("🗑️ مسح")
        self.clear_search_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #6c757d;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 60px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)

        second_row.addWidget(search_label)
        second_row.addWidget(self.search_input)
        second_row.addWidget(self.clear_search_btn)

        search_layout.addLayout(first_row)
        search_layout.addLayout(second_row)

        layout.addWidget(search_frame)
        
    def create_enhanced_job_titles_table(self, layout: QVBoxLayout):
        """إنشاء جدول العناوين الوظيفية المحسن"""
        self.job_titles_table = QTableWidget()

        # إعداد الجدول مع أعمدة محسنة
        headers = [
            "رمز الوظيفة", "العنوان الوظيفي", "الحد الأدنى للراتب",
            "الحد الأقصى للراتب", "عدد الموظفين"
        ]
        column_widths = [140, 250, 180, 180, 150]

        setup_table_widget(
            self.job_titles_table,
            headers,
            column_widths,
            sortable=True,
            selection_behavior="row"
        )

        # تحسين مظهر الجدول
        self.job_titles_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                gridline-color: #e9ecef;
                selection-background-color: #f0f8ff;
                selection-color: #6f42c1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
                text-align: center;
                color: #2c3e50;
            }
            QTableWidget::item:selected {
                background-color: #f0f8ff;
                color: #6f42c1;
                font-weight: bold;
            }
            QTableWidget::item:hover {
                background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #6f42c1;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                border: none;
                border-right: 1px solid #8e44ad;
                text-align: center;
            }
            QHeaderView::section:hover {
                background-color: #5a32a3;
            }
        """)

        # تمكين قائمة السياق
        self.job_titles_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.job_titles_table.customContextMenuRequested.connect(self.show_context_menu)

        layout.addWidget(self.job_titles_table)
        
    def create_compact_action_buttons(self, layout: QVBoxLayout):
        """إنشاء شريط الأزرار المدمج"""
        # إطار الأزرار المدمج
        buttons_frame = QFrame()
        buttons_frame.setObjectName("compact_buttons_frame")
        buttons_frame.setStyleSheet("""
            QFrame#compact_buttons_frame {
                background-color: #ffffff;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 8px;
                margin: 5px 0px;
                max-height: 60px;
            }
        """)

        # تخطيط أفقي مدمج
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(8)
        buttons_layout.setContentsMargins(10, 5, 10, 5)

        # أزرار الإجراءات المدمجة
        self.add_btn = QPushButton("➕ إضافة")
        self.add_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: bold;
                color: white;
                background-color: #28a745;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 80px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        self.edit_btn = QPushButton("✏️ تعديل")
        self.edit_btn.setEnabled(False)
        self.edit_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: bold;
                color: white;
                background-color: #6f42c1;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 70px;
                min-height: 35px;
            }
            QPushButton:hover:enabled {
                background-color: #5a32a3;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.setEnabled(False)
        self.delete_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: bold;
                color: white;
                background-color: #dc3545;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 60px;
                min-height: 35px;
            }
            QPushButton:hover:enabled {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: bold;
                color: white;
                background-color: #17a2b8;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 70px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)

        # ترتيب الأزرار المدمجة
        buttons_layout.addWidget(self.add_btn)
        buttons_layout.addWidget(self.edit_btn)
        buttons_layout.addWidget(self.delete_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.refresh_btn)

        layout.addWidget(buttons_frame)

    def show_context_menu(self, position):
        """عرض قائمة السياق للجدول"""
        # التحقق من وجود عنصر في الموضع المحدد
        item = self.job_titles_table.itemAt(position)
        if not item:
            return

        # إنشاء قائمة السياق محسنة
        from PySide6.QtWidgets import QMenu
        context_menu = QMenu(self)
        context_menu.setStyleSheet("""
            QMenu {
                background-color: #ffffff;
                border: 2px solid #ed8936;
                border-radius: 10px;
                padding: 8px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 6px;
                margin: 2px;
                font-size: 13px;
            }
            QMenu::item:selected {
                background-color: #ed8936;
                color: white;
            }
            QMenu::separator {
                height: 2px;
                background-color: #ffeaa7;
                margin: 4px 8px;
            }
        """)

        # الحصول على الصف المحدد
        row = self.job_titles_table.currentRow()
        has_selection = 0 <= row < len(self.filtered_data)

        if has_selection:
            job_title_name = self.filtered_data[row][1]

            # إضافة عنوان القائمة
            title_action = context_menu.addAction(f"💼 {job_title_name}")
            title_action.setEnabled(False)
            context_menu.addSeparator()

            # إضافة الإجراءات
            edit_action = context_menu.addAction("✏️ تعديل العنوان الوظيفي")
            edit_action.triggered.connect(self.on_edit_job_title)

            context_menu.addSeparator()

            delete_action = context_menu.addAction("🗑️ حذف العنوان الوظيفي")
            delete_action.triggered.connect(self.on_delete_job_title)

            # عرض القائمة
            context_menu.exec(self.job_titles_table.mapToGlobal(position))
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        # ربط إشارات البحث
        self.search_input.textChanged.connect(self.filter_job_titles)
        self.clear_search_btn.clicked.connect(self.clear_search)
        
        # ربط إشارات الجدول
        self.job_titles_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.job_titles_table.cellDoubleClicked.connect(self.on_cell_double_clicked)
        self.job_titles_table.cellClicked.connect(self.on_cell_clicked)
        
        # ربط إشارات الأزرار
        self.add_btn.clicked.connect(self.on_add_job_title)
        self.edit_btn.clicked.connect(self.on_edit_job_title)
        self.delete_btn.clicked.connect(self.on_delete_job_title)
        self.refresh_btn.clicked.connect(self.load_job_titles)
        
    def load_job_titles(self):
        """تحميل بيانات العناوين الوظيفية"""
        try:
            with get_db_session_context() as session:
                job_titles = session.query(JobTitle).filter_by(is_active=True).all()
                
                self.job_titles_data = []
                for title in job_titles:
                    row_data = [
                        title.code,
                        title.title,
                        format_currency(title.min_salary) if title.min_salary else "-",
                        format_currency(title.max_salary) if title.max_salary else "-",
                        str(title.employee_count)
                    ]
                    # إضافة ID للاستخدام الداخلي
                    row_data.append(title.id)
                    self.job_titles_data.append(row_data)
                
                # تطبيق التصفية
                self.filter_job_titles()
                
                # تحديث عداد العناوين الوظيفية
                total_job_titles = len(self.job_titles_data)
                active_job_titles = len([j for j in self.job_titles_data if int(j[4]) > 0])
                salary_ranges = len([j for j in self.job_titles_data if j[2] != "-" and j[3] != "-"])

                self.job_titles_count_label.setText(f"📊 إجمالي: {total_job_titles}")
                self.active_job_titles_label.setText(f"✅ نشط: {active_job_titles}")
                self.salary_ranges_label.setText(f"💰 نطاقات: {salary_ranges}")
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل بيانات العناوين الوظيفية: {e}", "error")
            
    def filter_job_titles(self):
        """تصفية العناوين الوظيفية"""
        search_text = self.search_input.text().lower()
        
        self.filtered_data = []
        
        for row in self.job_titles_data:
            # فلترة النص
            if search_text and search_text not in row[0].lower() and search_text not in row[1].lower():
                continue
            
            self.filtered_data.append(row)
        
        # تحديث الجدول
        self.update_table()
        
    def update_table(self):
        """تحديث الجدول"""
        # إزالة العمود الأخير (ID) من العرض
        display_data = [row[:-1] for row in self.filtered_data]
        populate_table(self.job_titles_table, display_data, editable=False)
        
    def clear_search(self):
        """مسح البحث"""
        self.search_input.clear()
        
    def on_selection_changed(self):
        """معالج تغيير التحديد"""
        has_selection = len(self.job_titles_table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        
        if has_selection:
            row = self.job_titles_table.currentRow()
            if 0 <= row < len(self.filtered_data):
                job_title_id = self.filtered_data[row][-1]  # آخر عنصر هو ID
                self.job_title_selected.emit(job_title_id)
                
    def on_cell_clicked(self, row, _):
        """معالج النقر على خلية"""
        # التأكد من أن الصف ضمن البيانات الفعلية
        if 0 <= row < len(self.filtered_data):
            # تحديد الصف كاملاً
            self.job_titles_table.selectRow(row)
            self.job_titles_table.setCurrentCell(row, 0)

            # تحديث حالة الأزرار
            self.on_selection_changed()

    def on_cell_double_clicked(self, row, _):
        """معالج النقر المزدوج على خلية"""
        # التأكد من أن الصف ضمن البيانات الفعلية
        if 0 <= row < len(self.filtered_data):
            # تحديد الصف أولاً
            self.job_titles_table.selectRow(row)
            self.job_titles_table.setCurrentCell(row, 0)

            # تحديث التحديد
            self.on_selection_changed()

            # تعديل المنصب مباشرة
            self.on_edit_job_title()
        
    def on_add_job_title(self):
        """معالج إضافة عنوان وظيفي"""
        self.add_job_title_requested.emit()
        
    def on_edit_job_title(self):
        """معالج تعديل عنوان وظيفي"""
        row = self.job_titles_table.currentRow()
        if 0 <= row < len(self.filtered_data):
            job_title_id = self.filtered_data[row][-1]
            self.edit_job_title_requested.emit(job_title_id)
            
    def on_delete_job_title(self):
        """معالج حذف عنوان وظيفي"""
        row = self.job_titles_table.currentRow()
        if 0 <= row < len(self.filtered_data):
            job_title_name = self.filtered_data[row][1]
            employee_count = int(self.filtered_data[row][4])
            
            if employee_count > 0:
                show_message(
                    self,
                    "لا يمكن الحذف",
                    f"لا يمكن حذف العنوان الوظيفي '{job_title_name}' لأنه مرتبط بـ {employee_count} موظف.",
                    "warning"
                )
                return
            
            if show_confirmation(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف العنوان الوظيفي '{job_title_name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه."
            ):
                job_title_id = self.filtered_data[row][-1]
                self.delete_job_title(job_title_id)
                
    def delete_job_title(self, job_title_id: int):
        """حذف عنوان وظيفي"""
        try:
            with get_db_session_context() as session:
                job_title = session.query(JobTitle).filter_by(id=job_title_id).first()
                if job_title:
                    job_title.soft_delete()
                    session.commit()
                    
                    show_message(self, "نجح", "تم حذف العنوان الوظيفي بنجاح", "information")
                    self.load_job_titles()
                else:
                    show_message(self, "خطأ", "العنوان الوظيفي غير موجود", "error")
                    
        except Exception as e:
            show_message(self, "خطأ", f"فشل في حذف العنوان الوظيفي: {e}", "error")
            
    def get_selected_job_title_id(self) -> int:
        """الحصول على معرف العنوان الوظيفي المحدد"""
        row = self.job_titles_table.currentRow()
        if 0 <= row < len(self.filtered_data):
            return self.filtered_data[row][-1]
        return -1
