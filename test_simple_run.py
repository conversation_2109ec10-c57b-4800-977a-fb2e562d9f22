#!/usr/bin/env python3
"""
اختبار بسيط لتشغيل النظام
Simple System Test
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """اختبار الاستيرادات"""
    print("🔍 اختبار الاستيرادات...")
    
    try:
        from src.config import get_config
        print("✅ تم استيراد get_config")
        
        from src.database import db_manager
        print("✅ تم استيراد db_manager")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config():
    """اختبار الإعدادات"""
    print("\n⚙️ اختبار الإعدادات...")
    
    try:
        from src.config import get_config
        
        ui_config = get_config("ui")
        font_size = ui_config.get("font_size", 12)
        
        print(f"✅ حجم الخط المحفوظ: {font_size}")
        return True
    except Exception as e:
        print(f"❌ خطأ في الإعدادات: {e}")
        return False

def test_application_creation():
    """اختبار إنشاء التطبيق"""
    print("\n🖥️ اختبار إنشاء التطبيق...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QFont
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ تم إنشاء QApplication")
        
        # تطبيق التخطيط
        app.setLayoutDirection(Qt.RightToLeft)
        print("✅ تم تطبيق التخطيط من اليمين لليسار")
        
        # تطبيق الخط
        font = QFont("Arial", 12)
        app.setFont(font)
        print("✅ تم تطبيق الخط")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window():
    """اختبار النافذة الرئيسية"""
    print("\n🏠 اختبار النافذة الرئيسية...")
    
    try:
        from src.views import MainWindow
        print("✅ تم استيراد MainWindow")
        
        # إنشاء النافذة (بدون عرضها)
        window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في النافذة الرئيسية: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n💾 اختبار قاعدة البيانات...")
    
    try:
        from src.database import db_manager
        
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        print("✅ تم تهيئة قاعدة البيانات")
        
        # اختبار الاتصال
        if db_manager.test_connection():
            print("✅ الاتصال بقاعدة البيانات يعمل")
        else:
            print("⚠️ مشكلة في الاتصال بقاعدة البيانات")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار بسيط لتشغيل النظام")
    print("=" * 50)
    
    tests = [
        ("الاستيرادات", test_imports),
        ("الإعدادات", test_config),
        ("إنشاء التطبيق", test_application_creation),
        ("النافذة الرئيسية", test_main_window),
        ("قاعدة البيانات", test_database)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"💥 فشل اختبار {test_name}")
        except Exception as e:
            print(f"💥 خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n🚀 يمكن تشغيل النظام:")
        print("python run_hr_system.py")
        return 0
    else:
        print(f"⚠️ فشل {total - passed} اختبار")
        print("يرجى مراجعة الأخطاء أعلاه")
        return 1

if __name__ == "__main__":
    sys.exit(main())
