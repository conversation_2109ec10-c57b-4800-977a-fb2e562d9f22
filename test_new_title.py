#!/usr/bin/env python3
"""
اختبار العنوان الجديد المحسن
Test New Enhanced Title
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, Q<PERSON>rame
from PySide6.QtCore import Qt


def create_title_comparison_test():
    """إنشاء نافذة مقارنة العناوين"""
    
    class TitleComparisonTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("مقارنة العناوين - قبل وبعد التحسين")
            self.setGeometry(100, 100, 1000, 700)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(30, 30, 30, 30)
            main_layout.setSpacing(30)
            
            # عنوان الاختبار
            test_title = QLabel("📝 مقارنة العناوين - قبل وبعد التحسين")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 15px;
                    padding: 20px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #e74c3c, stop: 1 #c0392b);
                    color: white;
                    border-radius: 12px;
                }
            """)
            main_layout.addWidget(test_title)
            
            # إطار المقارنة
            comparison_frame = QFrame()
            comparison_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 2px solid #bdc3c7;
                    border-radius: 12px;
                    padding: 20px;
                }
            """)
            
            comparison_layout = QVBoxLayout(comparison_frame)
            comparison_layout.setSpacing(40)
            
            # العنوان القديم
            old_section = QFrame()
            old_section.setStyleSheet("""
                QFrame {
                    background-color: #fadbd8;
                    border: 2px solid #e74c3c;
                    border-radius: 10px;
                    padding: 20px;
                }
            """)
            old_layout = QVBoxLayout(old_section)
            
            old_label = QLabel("❌ العنوان القديم:")
            old_label.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    font-weight: bold;
                    color: #c0392b;
                    margin-bottom: 10px;
                }
            """)
            old_layout.addWidget(old_label)
            
            old_title = QLabel("📊 الرسوم البيانية التفاعلية")
            old_title.setAlignment(Qt.AlignCenter)
            old_title.setStyleSheet("""
                QLabel {
                    font-size: 20px;
                    font-weight: bold;
                    color: #34495e;
                    margin: 15px 0;
                    padding: 12px;
                    letter-spacing: 1px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                              stop: 0 rgba(52, 73, 94, 0.05),
                                              stop: 1 rgba(44, 62, 80, 0.05));
                    border-radius: 8px;
                    border-left: 4px solid #3498db;
                }
            """)
            old_layout.addWidget(old_title)
            
            old_description = QLabel("• عام وغير محدد\n• لا يوضح المحتوى الفعلي\n• قد يكون مربكاً للمستخدم")
            old_description.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #7b241c;
                    margin-top: 10px;
                    line-height: 1.5;
                }
            """)
            old_layout.addWidget(old_description)
            
            comparison_layout.addWidget(old_section)
            
            # العنوان الجديد
            new_section = QFrame()
            new_section.setStyleSheet("""
                QFrame {
                    background-color: #d5f4e6;
                    border: 2px solid #27ae60;
                    border-radius: 10px;
                    padding: 20px;
                }
            """)
            new_layout = QVBoxLayout(new_section)
            
            new_label = QLabel("✅ العنوان الجديد المحسن:")
            new_label.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    font-weight: bold;
                    color: #27ae60;
                    margin-bottom: 10px;
                }
            """)
            new_layout.addWidget(new_label)
            
            new_title = QLabel("👥 توزيع الموظفين على الأقسام")
            new_title.setAlignment(Qt.AlignCenter)
            new_title.setStyleSheet("""
                QLabel {
                    font-size: 20px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 15px 0;
                    padding: 15px;
                    letter-spacing: 1.5px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                              stop: 0 rgba(52, 152, 219, 0.1),
                                              stop: 1 rgba(155, 89, 182, 0.1));
                    border-radius: 10px;
                    border: 2px solid rgba(52, 152, 219, 0.3);
                    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
                }
            """)
            new_layout.addWidget(new_title)
            
            new_description = QLabel("• محدد ووصفي\n• يوضح المحتوى بدقة\n• سهل الفهم للمستخدم\n• أيقونة مناسبة (👥)")
            new_description.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #1e8449;
                    margin-top: 10px;
                    line-height: 1.5;
                }
            """)
            new_layout.addWidget(new_description)
            
            comparison_layout.addWidget(new_section)
            
            main_layout.addWidget(comparison_frame)
            
            # معلومات التحسينات
            improvements_label = QLabel("""
            🎯 فوائد التغيير:
            
            ✅ وضوح أكبر: العنوان يوضح بدقة ما يعرضه الرسم البياني
            ✅ تحديد المحتوى: المستخدم يعرف مباشرة أنه سيرى توزيع الموظفين
            ✅ تنسيق أفضل: العنوان متناسق مع محتوى القسم
            ✅ أيقونة مناسبة: 👥 تدل على الموظفين بوضوح
            ✅ تجربة مستخدم محسنة: سهولة في التنقل والفهم
            ✅ احترافية أكبر: عنوان محدد يعكس جودة النظام
            
            🎨 التحسينات البصرية:
            • تباعد أحرف محسن (1.5px)
            • حدود ملونة (2px)
            • خلفية متدرجة محسنة
            • ظلال نصية للعمق
            • زوايا مدورة أكثر (10px)
            """)
            improvements_label.setAlignment(Qt.AlignLeft)
            improvements_label.setStyleSheet("""
                QLabel {
                    font-size: 13px;
                    color: #2c3e50;
                    background-color: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    border-left: 5px solid #3498db;
                    line-height: 1.6;
                }
            """)
            main_layout.addWidget(improvements_label)
    
    return TitleComparisonTest()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار العنوان الجديد المحسن")
    print("=" * 50)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء نافذة الاختبار
        window = create_title_comparison_test()
        window.show()
        
        print("✅ تم فتح نافذة مقارنة العناوين!")
        print("\n📝 التغيير المطبق:")
        print("❌ القديم: '📊 الرسوم البيانية التفاعلية'")
        print("✅ الجديد: '👥 توزيع الموظفين على الأقسام'")
        
        print("\n🎯 فوائد التغيير:")
        print("• وضوح أكبر في المحتوى")
        print("• تحديد دقيق لما يعرضه القسم")
        print("• تنسيق أفضل مع المحتوى")
        print("• أيقونة أكثر مناسبة")
        print("• تجربة مستخدم محسنة")
        
        print("\n🎨 التحسينات البصرية:")
        print("• تباعد أحرف محسن (1.5px)")
        print("• حدود ملونة (2px)")
        print("• خلفية متدرجة محسنة")
        print("• ظلال نصية للعمق")
        print("• زوايا مدورة أكثر (10px)")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
