"""
اختبارات الواجهات
Views Tests
"""

import pytest
from unittest.mock import Mock, patch
from PySide6.QtWidgets import QApplication
from PySide6.QtTest import QTest
from PySide6.QtCore import Qt

from src.views import (
    MainWindow, Dashboard, EmployeesView, EmployeeForm,
    DepartmentsView, DepartmentForm, JobTitlesView, JobTitleForm
)


@pytest.fixture(scope="session")
def qapp():
    """إنشاء تطبيق Qt للاختبارات"""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    yield app


@pytest.fixture
def main_window(qapp):
    """إنشاء النافذة الرئيسية للاختبار"""
    window = MainWindow()
    yield window
    window.close()


@pytest.fixture
def dashboard(qapp):
    """إنشاء لوحة التحكم للاختبار"""
    dashboard = Dashboard()
    yield dashboard


@pytest.fixture
def employees_view(qapp):
    """إنشاء واجهة الموظفين للاختبار"""
    view = EmployeesView()
    yield view


class TestMainWindow:
    """اختبارات النافذة الرئيسية"""
    
    def test_main_window_creation(self, main_window):
        """اختبار إنشاء النافذة الرئيسية"""
        assert main_window is not None
        assert main_window.windowTitle() == "نظام إدارة شؤون الموظفين"
        
    def test_main_window_components(self, main_window):
        """اختبار مكونات النافذة الرئيسية"""
        # التأكد من وجود الشريط الجانبي
        assert hasattr(main_window, 'sidebar')
        assert main_window.sidebar is not None
        
        # التأكد من وجود منطقة المحتوى
        assert hasattr(main_window, 'content_stack')
        assert main_window.content_stack is not None
        
    def test_page_navigation(self, main_window):
        """اختبار التنقل بين الصفحات"""
        # الانتقال إلى صفحة الموظفين
        main_window.show_page("employees")
        current_page = main_window.content_stack.currentWidget()
        assert current_page is not None
        
        # الانتقال إلى صفحة الأقسام
        main_window.show_page("departments")
        new_current_page = main_window.content_stack.currentWidget()
        assert new_current_page != current_page
        
    def test_invalid_page_navigation(self, main_window):
        """اختبار التنقل إلى صفحة غير موجودة"""
        # محاولة الانتقال إلى صفحة غير موجودة
        main_window.show_page("nonexistent_page")
        # يجب أن تبقى على الصفحة الحالية
        assert main_window.content_stack.currentWidget() is not None


class TestDashboard:
    """اختبارات لوحة التحكم"""
    
    def test_dashboard_creation(self, dashboard):
        """اختبار إنشاء لوحة التحكم"""
        assert dashboard is not None
        
    @patch('src.views.dashboard.get_db_session_context')
    def test_dashboard_statistics_loading(self, mock_db_context, dashboard):
        """اختبار تحميل الإحصائيات"""
        # إعداد البيانات الوهمية
        mock_session = Mock()
        mock_db_context.return_value.__enter__.return_value = mock_session
        
        # إعداد النتائج الوهمية
        mock_session.query.return_value.filter_by.return_value.count.return_value = 10
        
        # تحديث الإحصائيات
        dashboard.refresh_statistics()
        
        # التأكد من استدعاء قاعدة البيانات
        assert mock_session.query.called
        
    def test_stat_cards_creation(self, dashboard):
        """اختبار إنشاء بطاقات الإحصائيات"""
        # التأكد من وجود بطاقات الإحصائيات
        assert hasattr(dashboard, 'total_employees_card')
        assert hasattr(dashboard, 'active_employees_card')
        assert hasattr(dashboard, 'total_departments_card')


class TestEmployeesView:
    """اختبارات واجهة الموظفين"""
    
    def test_employees_view_creation(self, employees_view):
        """اختبار إنشاء واجهة الموظفين"""
        assert employees_view is not None
        
    def test_employees_table_setup(self, employees_view):
        """اختبار إعداد جدول الموظفين"""
        # التأكد من وجود الجدول
        assert hasattr(employees_view, 'employees_table')
        assert employees_view.employees_table is not None
        
        # التأكد من وجود الأعمدة المطلوبة
        table = employees_view.employees_table
        expected_columns = ["الرقم الوظيفي", "الاسم الكامل", "القسم", "العنوان الوظيفي", "الراتب الأساسي"]
        
        for i, expected_column in enumerate(expected_columns):
            header_item = table.horizontalHeaderItem(i)
            if header_item:
                assert expected_column in header_item.text()
                
    def test_search_functionality(self, employees_view):
        """اختبار وظيفة البحث"""
        # التأكد من وجود حقل البحث
        assert hasattr(employees_view, 'search_input')
        assert employees_view.search_input is not None
        
        # محاكاة إدخال نص البحث
        search_text = "محمد"
        employees_view.search_input.setText(search_text)
        
        # التأكد من تحديث النص
        assert employees_view.search_input.text() == search_text
        
    def test_filter_functionality(self, employees_view):
        """اختبار وظيفة التصفية"""
        # التأكد من وجود عناصر التصفية
        assert hasattr(employees_view, 'department_filter')
        assert hasattr(employees_view, 'status_filter')
        
    @patch('src.views.employees_view.get_db_session_context')
    def test_load_employees(self, mock_db_context, employees_view):
        """اختبار تحميل قائمة الموظفين"""
        # إعداد البيانات الوهمية
        mock_session = Mock()
        mock_db_context.return_value.__enter__.return_value = mock_session
        
        # إعداد موظف وهمي
        mock_employee = Mock()
        mock_employee.employee_number = "EMP001"
        mock_employee.full_name = "محمد أحمد"
        mock_employee.department.name = "تقنية المعلومات"
        mock_employee.job_title.title = "مطور"
        mock_employee.basic_salary = 5000
        
        mock_session.query.return_value.filter_by.return_value.all.return_value = [mock_employee]
        
        # تحميل الموظفين
        employees_view.load_employees()
        
        # التأكد من استدعاء قاعدة البيانات
        assert mock_session.query.called
        
    def test_add_employee_signal(self, employees_view):
        """اختبار إشارة إضافة موظف"""
        # التأكد من وجود الزر
        assert hasattr(employees_view, 'add_employee_btn')
        
        # محاكاة النقر على الزر
        signal_emitted = False
        
        def on_signal_emitted():
            nonlocal signal_emitted
            signal_emitted = True
            
        employees_view.add_employee_requested.connect(on_signal_emitted)
        employees_view.add_employee_btn.click()
        
        # التأكد من إرسال الإشارة
        assert signal_emitted


class TestEmployeeForm:
    """اختبارات نموذج الموظف"""
    
    @pytest.fixture
    def employee_form(self, qapp):
        """إنشاء نموذج الموظف للاختبار"""
        form = EmployeeForm()
        yield form
        form.close()
        
    def test_employee_form_creation(self, employee_form):
        """اختبار إنشاء نموذج الموظف"""
        assert employee_form is not None
        assert employee_form.windowTitle() == "إضافة موظف جديد"
        
    def test_form_fields(self, employee_form):
        """اختبار حقول النموذج"""
        # التأكد من وجود الحقول المطلوبة
        required_fields = [
            'employee_number_input', 'full_name_input', 'national_id_input',
            'phone_input', 'email_input', 'hire_date_input', 'basic_salary_input'
        ]
        
        for field_name in required_fields:
            assert hasattr(employee_form, field_name)
            
    def test_form_validation(self, employee_form):
        """اختبار التحقق من صحة البيانات"""
        # ترك الحقول فارغة ومحاولة الحفظ
        is_valid = employee_form.validate_form()
        assert not is_valid  # يجب أن يفشل التحقق
        
        # ملء الحقول المطلوبة
        employee_form.employee_number_input.setText("EMP001")
        employee_form.full_name_input.setText("محمد أحمد")
        employee_form.national_id_input.setText("1234567890")
        employee_form.phone_input.setText("0501234567")
        employee_form.basic_salary_input.setValue(5000)
        
        is_valid = employee_form.validate_form()
        assert is_valid  # يجب أن ينجح التحقق
        
    def test_clear_form(self, employee_form):
        """اختبار مسح النموذج"""
        # ملء بعض الحقول
        employee_form.full_name_input.setText("اختبار")
        employee_form.phone_input.setText("123456789")
        
        # مسح النموذج
        employee_form.clear_form()
        
        # التأكد من مسح الحقول
        assert employee_form.full_name_input.text() == ""
        assert employee_form.phone_input.text() == ""


class TestDepartmentsView:
    """اختبارات واجهة الأقسام"""
    
    @pytest.fixture
    def departments_view(self, qapp):
        """إنشاء واجهة الأقسام للاختبار"""
        view = DepartmentsView()
        yield view
        
    def test_departments_view_creation(self, departments_view):
        """اختبار إنشاء واجهة الأقسام"""
        assert departments_view is not None
        
    def test_departments_table_setup(self, departments_view):
        """اختبار إعداد جدول الأقسام"""
        assert hasattr(departments_view, 'departments_table')
        assert departments_view.departments_table is not None
        
    @patch('src.views.departments_view.get_db_session_context')
    def test_load_departments(self, mock_db_context, departments_view):
        """اختبار تحميل قائمة الأقسام"""
        mock_session = Mock()
        mock_db_context.return_value.__enter__.return_value = mock_session
        
        mock_department = Mock()
        mock_department.name = "قسم المحاسبة"
        mock_department.manager_name = "أحمد محمد"
        mock_department.employees_count = 5
        
        mock_session.query.return_value.filter_by.return_value.all.return_value = [mock_department]
        
        departments_view.load_departments()
        assert mock_session.query.called


class TestJobTitlesView:
    """اختبارات واجهة العناوين الوظيفية"""
    
    @pytest.fixture
    def job_titles_view(self, qapp):
        """إنشاء واجهة العناوين الوظيفية للاختبار"""
        view = JobTitlesView()
        yield view
        
    def test_job_titles_view_creation(self, job_titles_view):
        """اختبار إنشاء واجهة العناوين الوظيفية"""
        assert job_titles_view is not None
        
    def test_job_titles_table_setup(self, job_titles_view):
        """اختبار إعداد جدول العناوين الوظيفية"""
        assert hasattr(job_titles_view, 'job_titles_table')
        assert job_titles_view.job_titles_table is not None
