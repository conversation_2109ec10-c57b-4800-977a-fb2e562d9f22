"""
لوحة التحكم الرئيسية
Main Dashboard
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
    QFrame, QPushButton, QScrollArea
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont

from ..utils import apply_rtl_layout, set_font_size, make_bold, format_currency
from ..database import get_db_session_context
from ..models import Employee, FinancialTransaction, Department, JobTitle
from ..widgets import StatisticsChartWidget, EnhancedDashboard


class StatCard(QFrame):
    """بطاقة إحصائية"""
    
    def __init__(self, title: str, value: str, subtitle: str = "", color: str = "blue"):
        super().__init__()
        self.title = title
        self.value = value
        self.subtitle = subtitle
        self.color = color
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم المحسنة"""
        self.setObjectName(f"stat_card_{self.color}")
        self.setMinimumHeight(180)  # زيادة الارتفاع أكثر
        self.setMaximumHeight(180)
        self.setMinimumWidth(280)   # زيادة العرض أكثر

        # تطبيق التخطيط من اليمين إلى اليسار
        apply_rtl_layout(self)

        # إنشاء التخطيط المحسن
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(12)  # زيادة المسافة بين العناصر

        # عنوان البطاقة المحسن
        title_label = QLabel(self.title)
        title_label.setObjectName("stat_card_title")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)  # السماح بالتفاف النص
        set_font_size(title_label, 18)  # حجم خط 18 كما طلبت
        make_bold(title_label)
        layout.addWidget(title_label)

        # القيمة الرئيسية المحسنة
        value_label = QLabel(self.value)
        value_label.setObjectName("stat_card_value")
        value_label.setAlignment(Qt.AlignCenter)
        make_bold(value_label)
        set_font_size(value_label, 28)  # تقليل قليلاً لتوازن أفضل
        layout.addWidget(value_label)

        # العنوان الفرعي المحسن
        if self.subtitle:
            subtitle_label = QLabel(self.subtitle)
            subtitle_label.setObjectName("stat_card_subtitle")
            subtitle_label.setAlignment(Qt.AlignCenter)
            subtitle_label.setWordWrap(True)  # السماح بالتفاف النص
            set_font_size(subtitle_label, 18)  # حجم خط 18 كما طلبت
            make_bold(subtitle_label)
            layout.addWidget(subtitle_label)

        # إضافة مساحة مرنة
        layout.addStretch()
        
    def update_value(self, new_value: str, new_subtitle: str = None):
        """تحديث القيمة"""
        self.value = new_value
        if new_subtitle is not None:
            self.subtitle = new_subtitle
        
        # تحديث العناصر
        value_label = self.findChild(QLabel, "stat_card_value")
        if value_label:
            value_label.setText(self.value)
            
        if new_subtitle is not None:
            subtitle_label = self.findChild(QLabel, "stat_card_subtitle")
            if subtitle_label:
                subtitle_label.setText(self.subtitle)


class Dashboard(QWidget):
    """لوحة التحكم الرئيسية"""
    
    def __init__(self):
        super().__init__()
        self.stat_cards = {}
        
        self.setup_ui()
        self.setup_timer()
        self.load_statistics()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setObjectName("dashboard")
        
        # تطبيق التخطيط من اليمين إلى اليسار
        apply_rtl_layout(self)
        
        # إنشاء منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # إنشاء محتوى لوحة التحكم
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(30, 30, 30, 30)
        content_layout.setSpacing(30)
        
        # إضافة العنوان
        self.create_header(content_layout)
        
        # إضافة البطاقات الإحصائية
        self.create_statistics_section(content_layout)
        
        # إضافة قسم الإجراءات السريعة
        self.create_quick_actions_section(content_layout)
        
        # إضافة مساحة مرنة
        content_layout.addStretch()
        
        # إعداد منطقة التمرير
        scroll_area.setWidget(content_widget)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)
        
    def create_header(self, layout: QVBoxLayout):
        """إنشاء رأس لوحة التحكم المحسن والمميز"""
        # إنشاء إطار الهيدر المميز
        header_frame = QFrame()
        header_frame.setObjectName("dashboard_header_frame")
        header_frame.setMinimumHeight(120)
        header_frame.setMaximumHeight(120)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(40, 20, 40, 20)
        header_layout.setSpacing(10)

        # العنوان الرئيسي المميز
        main_title = QLabel("🏢 لوحة التحكم الرئيسية")
        main_title.setObjectName("dashboard_main_title")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setWordWrap(True)
        set_font_size(main_title, 18)  # حجم خط 18 كما طلبت
        make_bold(main_title)
        header_layout.addWidget(main_title)

        # العنوان الفرعي
        subtitle = QLabel("نظام إدارة شؤون الموظفين المتقدم")
        subtitle.setObjectName("dashboard_subtitle")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setWordWrap(True)
        set_font_size(subtitle, 14)
        header_layout.addWidget(subtitle)

        layout.addWidget(header_frame)

        # إضافة مساحة فاصلة
        layout.addSpacing(20)
        
    def create_statistics_section(self, layout: QVBoxLayout):
        """إنشاء قسم الإحصائيات المحسن"""
        # إنشاء إطار القسم
        stats_frame = QFrame()
        stats_frame.setObjectName("statistics_frame")
        stats_layout = QVBoxLayout(stats_frame)
        stats_layout.setContentsMargins(30, 25, 30, 25)
        stats_layout.setSpacing(25)

        # عنوان القسم المحسن
        section_title = QLabel("📊 الإحصائيات العامة")
        section_title.setObjectName("statistics_section_title")
        section_title.setAlignment(Qt.AlignCenter)
        make_bold(section_title)
        set_font_size(section_title, 18)  # حجم خط 18 كما طلبت
        stats_layout.addWidget(section_title)

        # شبكة البطاقات الإحصائية المحسنة
        stats_grid = QGridLayout()
        stats_grid.setSpacing(25)  # زيادة المسافات
        stats_grid.setContentsMargins(20, 20, 20, 20)
        
        # إنشاء مكون الرسومات البيانية التفاعلية
        self.charts_widget = StatisticsChartWidget()
        stats_layout.addWidget(self.charts_widget)

        # إضافة الإطار إلى التخطيط الرئيسي
        layout.addWidget(stats_frame)

        # الاحتفاظ بمرجع للبطاقات للتوافق مع الكود الموجود
        self.stat_cards = {}

        # تحديث فوري للبيانات
        QTimer.singleShot(500, self.load_statistics)  # تحديث بعد نصف ثانية
        
    def create_quick_actions_section(self, layout: QVBoxLayout):
        """إنشاء قسم الإجراءات السريعة المحسن"""
        # إنشاء إطار الإجراءات السريعة
        actions_frame = QFrame()
        actions_frame.setObjectName("quick_actions_frame")
        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setContentsMargins(30, 25, 30, 25)
        actions_layout.setSpacing(20)

        # عنوان القسم المحسن
        section_title = QLabel("⚡ الإجراءات السريعة")
        section_title.setObjectName("quick_actions_section_title")
        section_title.setAlignment(Qt.AlignCenter)
        make_bold(section_title)
        set_font_size(section_title, 18)
        actions_layout.addWidget(section_title)

        # تخطيط الأزرار المحسن
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(20)
        buttons_layout.setContentsMargins(20, 10, 20, 10)

        # أزرار الإجراءات السريعة المحسنة
        actions = [
            ("➕ إضافة موظف جديد", "add_employee", "#28a745"),
            ("💰 إضافة معاملة مالية", "add_transaction", "#007bff"),
            ("📊 عرض التقارير", "view_reports", "#ffc107"),
            ("💾 النسخ الاحتياطي", "backup", "#6f42c1"),
        ]

        for text, action, color in actions:
            button = QPushButton(text)
            button.setObjectName("enhanced_quick_action_button")
            button.setMinimumHeight(60)  # زيادة الارتفاع
            button.setMinimumWidth(200)  # زيادة العرض
            set_font_size(button, 18)    # حجم خط 18 كما طلبت
            make_bold(button)

            # تطبيق لون مخصص
            button.setStyleSheet(f"""
                QPushButton#enhanced_quick_action_button {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                              stop: 0 {color}, stop: 1 {self._darken_color(color)});
                    color: white;
                    border: none;
                    border-radius: 12px;
                    font-size: 18px;
                    font-weight: bold;
                    padding: 15px 20px;
                    text-align: center;
                }}
                QPushButton#enhanced_quick_action_button:hover {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                              stop: 0 {self._lighten_color(color)}, stop: 1 {color});
                }}
                QPushButton#enhanced_quick_action_button:pressed {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                              stop: 0 {self._darken_color(color)}, stop: 1 {self._darken_color(color, 20)});
                }}
            """)

            # ربط الإجراءات السريعة
            if action == "add_employee":
                button.clicked.connect(self.add_employee_quick)
            elif action == "add_transaction":
                button.clicked.connect(self.add_transaction_quick)
            elif action == "view_reports":
                button.clicked.connect(self.view_reports_quick)
            elif action == "backup":
                button.clicked.connect(self.backup_quick)

            buttons_layout.addWidget(button)

        # إضافة التخطيط إلى الإطار
        actions_layout.addLayout(buttons_layout)

        # إضافة الإطار إلى التخطيط الرئيسي
        layout.addWidget(actions_frame)
        
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.load_statistics)
        self.update_timer.start(30000)  # تحديث كل 30 ثانية
        
    def load_statistics(self):
        """تحميل الإحصائيات من قاعدة البيانات"""
        try:
            with get_db_session_context() as session:
                # عدد الموظفين النشطين
                active_employees = session.query(Employee).filter_by(is_active=True).count()
                self.stat_cards["employees"].update_value(str(active_employees))
                
                # عدد الأقسام
                departments_count = session.query(Department).filter_by(is_active=True).count()
                self.stat_cards["departments"].update_value(str(departments_count))
                
                # عدد العناوين الوظيفية
                job_titles_count = session.query(JobTitle).filter_by(is_active=True).count()
                self.stat_cards["job_titles"].update_value(str(job_titles_count))
                
                # إجمالي الرواتب الشهرية
                total_salaries = session.query(Employee).filter_by(is_active=True).with_entities(
                    Employee.basic_salary
                ).all()
                monthly_total = sum(float(salary[0]) for salary in total_salaries)
                self.stat_cards["monthly_salaries"].update_value(format_currency(monthly_total))
                
                # السلف المستحقة
                pending_advances = session.query(FinancialTransaction).filter(
                    FinancialTransaction.transaction_type == "ADVANCE",
                    FinancialTransaction.payment_status.in_(["PENDING", "PARTIALLY_PAID"]),
                    FinancialTransaction.is_active == True
                ).all()
                total_advances = sum(float(t.amount) - float(t.paid_amount) for t in pending_advances)
                self.stat_cards["pending_advances"].update_value(format_currency(total_advances))
                
                # ديون الماركت
                market_debts = session.query(FinancialTransaction).filter(
                    FinancialTransaction.transaction_type == "MARKET_DEBT",
                    FinancialTransaction.payment_status.in_(["PENDING", "PARTIALLY_PAID"]),
                    FinancialTransaction.is_active == True
                ).all()
                total_debts = sum(float(t.amount) - float(t.paid_amount) for t in market_debts)

                # تحديث الرسومات البيانية بدلاً من البطاقات
                if hasattr(self, 'charts_widget') and self.charts_widget:
                    print(f"📊 تحديث لوحة التحكم - موظفين: {active_employees}, أقسام: {departments_count}")
                    self.charts_widget.update_data(
                        employee_count=active_employees,
                        department_count=departments_count,
                        total_salary=monthly_total,
                        advance_count=len(pending_advances),
                        job_title_count=job_titles_count
                    )
                else:
                    print("⚠️ مكون الرسومات البيانية غير متاح")

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")
            
    def refresh_statistics(self):
        """تحديث الإحصائيات يدوياً"""
        self.load_statistics()

    # إشارات للإجراءات السريعة
    add_employee_requested = Signal()
    add_transaction_requested = Signal()
    view_reports_requested = Signal()
    backup_requested = Signal()

    def add_employee_quick(self):
        """إجراء سريع: إضافة موظف جديد"""
        self.add_employee_requested.emit()

    def add_transaction_quick(self):
        """إجراء سريع: إضافة معاملة مالية"""
        self.add_transaction_requested.emit()

    def view_reports_quick(self):
        """إجراء سريع: عرض التقارير"""
        self.view_reports_requested.emit()

    def backup_quick(self):
        """إجراء سريع: النسخ الاحتياطي"""
        self.backup_requested.emit()

    def _lighten_color(self, color: str, amount: int = 10) -> str:
        """تفتيح لون"""
        # إزالة # إذا كانت موجودة
        color = color.lstrip('#')

        # تحويل إلى RGB
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)

        # تفتيح اللون
        r = min(255, r + amount)
        g = min(255, g + amount)
        b = min(255, b + amount)

        return f"#{r:02x}{g:02x}{b:02x}"

    def _darken_color(self, color: str, amount: int = 10) -> str:
        """تغميق لون"""
        # إزالة # إذا كانت موجودة
        color = color.lstrip('#')

        # تحويل إلى RGB
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)

        # تغميق اللون
        r = max(0, r - amount)
        g = max(0, g - amount)
        b = max(0, b - amount)

        return f"#{r:02x}{g:02x}{b:02x}"
