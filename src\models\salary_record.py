"""
نموذج سجلات الرواتب
Salary Record model
"""

from datetime import date
from enum import Enum
from sqlalchemy import Column, Integer, Date, Numeric, ForeignKey, Boolean, Enum as SQLEnum
from sqlalchemy.orm import relationship
from .base import BaseModel


class PaymentStatus(Enum):
    """حالة صرف الراتب"""
    PENDING = "معلق"
    PAID = "مدفوع"
    CANCELLED = "ملغي"


class SalaryRecord(BaseModel):
    """نموذج سجلات الرواتب"""
    
    __tablename__ = "salary_records"
    
    # معلومات الراتب
    month = Column(Integer, nullable=False, comment="الشهر")
    year = Column(Integer, nullable=False, comment="السنة")
    basic_salary = Column(Numeric(12, 2), nullable=False, comment="الراتب الأساسي")
    daily_salary = Column(Numeric(12, 2), nullable=False, comment="الراتب اليومي")
    working_days = Column(Integer, nullable=False, default=30, comment="عدد أيام الدوام")
    
    # المعاملات المالية
    total_advances = Column(Numeric(12, 2), nullable=False, default=0, comment="إجمالي السلف")
    total_deductions = Column(Numeric(12, 2), nullable=False, default=0, comment="إجمالي الخصومات")
    total_bonuses = Column(Numeric(12, 2), nullable=False, default=0, comment="إجمالي المكافآت")
    total_market_debts = Column(Numeric(12, 2), nullable=False, default=0, comment="إجمالي ديون الماركت")
    
    # الراتب النهائي
    gross_salary = Column(Numeric(12, 2), nullable=False, comment="إجمالي الراتب")
    net_salary = Column(Numeric(12, 2), nullable=False, comment="صافي الراتب")
    
    # حالة الصرف
    payment_status = Column(
        SQLEnum(PaymentStatus),
        nullable=False,
        default=PaymentStatus.PENDING,
        comment="حالة الصرف"
    )
    payment_date = Column(Date, nullable=True, comment="تاريخ الصرف")
    
    # المفاتيح الخارجية
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=False)
    
    # العلاقات
    employee = relationship("Employee", back_populates="salary_records")
    
    def __repr__(self) -> str:
        return f"<SalaryRecord(id={self.id}, employee_id={self.employee_id}, month={self.month}, year={self.year})>"
    
    def __str__(self) -> str:
        return f"راتب {self.employee.full_name} - {self.month}/{self.year}"
    
    @property
    def period_display(self) -> str:
        """عرض الفترة بشكل مقروء"""
        months = [
            "", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        return f"{months[self.month]} {self.year}"
    
    @property
    def is_paid(self) -> bool:
        """هل تم صرف الراتب"""
        return self.payment_status == PaymentStatus.PAID
    
    def calculate_salary(self) -> None:
        """
        حساب الراتب
        صافي الراتب = (الراتب اليومي × عدد أيام الدوام) + المكافآت + الحوافز - السلف - الديون
        """
        # تحويل جميع القيم إلى float لتجنب مشاكل نوع البيانات
        daily_salary = float(self.daily_salary)
        working_days = float(self.working_days)
        total_bonuses = float(self.total_bonuses)
        total_advances = float(self.total_advances)
        total_deductions = float(self.total_deductions)
        total_market_debts = float(self.total_market_debts)

        # حساب الراتب الأساسي حسب أيام الدوام
        basic_salary_for_days = daily_salary * working_days

        # حساب إجمالي الراتب (الراتب الأساسي + المكافآت والحوافز)
        gross_salary = basic_salary_for_days + total_bonuses

        # حساب صافي الراتب (إجمالي الراتب - السلف - الخصومات - الديون)
        net_salary = gross_salary - total_advances - total_deductions - total_market_debts

        # تحويل النتائج إلى Decimal للحفظ في قاعدة البيانات
        from decimal import Decimal
        self.gross_salary = Decimal(str(round(gross_salary, 2)))
        self.net_salary = Decimal(str(round(net_salary, 2)))
    
    def pay_salary(self, payment_date: date = None) -> bool:
        """صرف الراتب"""
        if self.payment_status == PaymentStatus.PAID:
            return False
        
        self.payment_status = PaymentStatus.PAID
        self.payment_date = payment_date or date.today()
        return True
    
    def cancel_payment(self) -> bool:
        """إلغاء صرف الراتب"""
        if self.payment_status != PaymentStatus.PAID:
            return False
        
        self.payment_status = PaymentStatus.PENDING
        self.payment_date = None
        return True
    
    @classmethod
    def get_monthly_records(cls, session, month: int, year: int):
        """الحصول على سجلات راتب شهر معين"""
        return session.query(cls).filter(
            cls.month == month,
            cls.year == year,
            cls.is_active == True
        ).all()
    
    @classmethod
    def get_employee_records(cls, session, employee_id: int, year: int = None):
        """الحصول على سجلات راتب موظف معين"""
        query = session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.is_active == True
        )
        
        if year:
            query = query.filter(cls.year == year)
        
        return query.order_by(cls.year.desc(), cls.month.desc()).all()
    
    @classmethod
    def exists_for_period(cls, session, employee_id: int, month: int, year: int) -> bool:
        """التحقق من وجود سجل راتب لفترة معينة"""
        return session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.month == month,
            cls.year == year,
            cls.is_active == True
        ).first() is not None
