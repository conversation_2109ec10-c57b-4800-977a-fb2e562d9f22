"""
واجهة إدارة المعاملات المالية
Financial Transactions Management View
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QComboBox, QLabel, QFrame, QHeaderView,
    QAbstractItemView, QDateEdit, QTabWidget
)
from PySide6.QtCore import Qt, Signal, QDate
from PySide6.QtGui import QFont

from ..utils import (
    apply_rtl_layout, setup_table_widget, populate_table, show_message,
    show_confirmation, format_currency, format_date, get_transaction_type_display,
    get_payment_status_display, setup_combobox, get_combobox_value
)
from ..database import get_db_session_context
from ..models import FinancialTransaction, Employee, TransactionType, PaymentStatus


class FinancialView(QWidget):
    """واجهة إدارة المعاملات المالية"""
    
    # إشارات
    transaction_selected = Signal(int)  # إشارة اختيار معاملة
    add_transaction_requested = Signal()  # إشارة طلب إضافة معاملة
    edit_transaction_requested = Signal(int)  # إشارة طلب تعديل معاملة
    view_employee_account_requested = Signal(int)  # إشارة عرض كشف حساب موظف
    
    def __init__(self):
        super().__init__()
        self.transactions_data = []
        self.filtered_data = []
        
        self.setup_ui()
        self.setup_connections()
        self.load_transactions()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setObjectName("financial_view")
        apply_rtl_layout(self)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # إنشاء رأس الصفحة المحسن
        self.create_enhanced_header(main_layout)

        # إنشاء شريط البحث والإحصائيات المدمج
        self.create_integrated_search_stats(main_layout)

        # إنشاء جدول المعاملات المحسن
        self.create_enhanced_transactions_table(main_layout)

        # إنشاء شريط الأزرار المدمج
        self.create_compact_action_buttons(main_layout)
        
    def create_enhanced_header(self, layout: QVBoxLayout):
        """إنشاء رأس الصفحة المحسن"""
        # إنشاء إطار العنوان الرئيسي المدمج
        header_frame = QFrame()
        header_frame.setObjectName("compact_header_frame")
        header_frame.setFixedHeight(120)  # ارتفاع محسن لتوفير مساحة أفضل للعناوين
        header_frame.setStyleSheet("""
            QFrame#compact_header_frame {
                background-color: #fff3cd;
                border: 2px solid #ffc107;
                border-radius: 10px;
                margin: 5px;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 8, 15, 8)
        header_layout.setSpacing(5)

        # العنوان الرئيسي المتغير
        self.main_title = QLabel("💰 إدارة المعاملات المالية")
        self.main_title.setAlignment(Qt.AlignCenter)
        self.main_title.setFixedHeight(60)  # ارتفاع محسن للعنوان الرئيسي
        self.main_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 22px;
                font-weight: bold;
                color: #2c3e50;
                background-color: transparent;
                padding: 8px;
                margin: 0px;
            }
        """)

        # العنوان الفرعي المتغير
        self.subtitle = QLabel("💳 إدارة السلف والمكافآت والخصومات والديون")
        self.subtitle.setAlignment(Qt.AlignCenter)
        self.subtitle.setFixedHeight(40)  # ارتفاع محسن للعنوان الفرعي
        self.subtitle.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #34495e;
                background-color: #fffbf0;
                padding: 6px 15px;
                border-radius: 6px;
                border-left: 3px solid #ffc107;
                margin: 2px;
            }
        """)

        # تجميع العناصر
        header_layout.addWidget(self.main_title)
        header_layout.addWidget(self.subtitle)

        layout.addWidget(header_frame)

    def update_page_title(self, title: str, subtitle: str):
        """تحديث عنوان الصفحة"""
        self.main_title.setText(title)
        self.subtitle.setText(subtitle)
        
    def create_tabs(self, layout: QVBoxLayout):
        """إنشاء التبويبات"""
        self.tab_widget = QTabWidget()
        
        # تبويب جميع المعاملات
        self.all_transactions_tab = self.create_all_transactions_tab()
        self.tab_widget.addTab(self.all_transactions_tab, "جميع المعاملات")
        
        # تبويب الديون المعلقة
        self.pending_debts_tab = self.create_pending_debts_tab()
        self.tab_widget.addTab(self.pending_debts_tab, "الديون المعلقة")
        
        layout.addWidget(self.tab_widget)
        
    def create_all_transactions_tab(self):
        """إنشاء تبويب جميع المعاملات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # شريط البحث والتصفية
        self.create_search_bar(layout)
        
        # جدول المعاملات
        self.create_transactions_table(layout)
        
        # شريط الأزرار
        self.create_action_buttons(layout)
        
        return tab
        
    def create_pending_debts_tab(self):
        """إنشاء تبويب الديون المعلقة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # جدول الديون المعلقة
        self.pending_debts_table = QTableWidget()
        headers = [
            "الموظف", "نوع المعاملة", "المبلغ الأصلي", "المبلغ المدفوع",
            "المبلغ المتبقي", "تاريخ المعاملة", "حالة الدفع"
        ]
        column_widths = [150, 120, 120, 120, 120, 120, 120]
        
        setup_table_widget(
            self.pending_debts_table,
            headers,
            column_widths,
            sortable=True,
            selection_behavior="row"
        )
        
        layout.addWidget(self.pending_debts_table)
        
        # أزرار إدارة الديون
        debts_buttons_layout = QHBoxLayout()
        
        self.pay_debt_btn = QPushButton("تسجيل دفعة")
        self.pay_debt_btn.setObjectName("success_button")
        self.pay_debt_btn.setMinimumWidth(120)
        self.pay_debt_btn.setEnabled(False)
        
        self.view_account_btn = QPushButton("كشف الحساب")
        self.view_account_btn.setMinimumWidth(120)
        self.view_account_btn.setEnabled(False)
        
        self.refresh_debts_btn = QPushButton("تحديث")
        self.refresh_debts_btn.setMinimumWidth(100)
        
        debts_buttons_layout.addWidget(self.pay_debt_btn)
        debts_buttons_layout.addWidget(self.view_account_btn)
        debts_buttons_layout.addStretch()
        debts_buttons_layout.addWidget(self.refresh_debts_btn)
        
        layout.addLayout(debts_buttons_layout)
        
        return tab
        
    def create_integrated_search_stats(self, layout: QVBoxLayout):
        """إنشاء البحث والإحصائيات المدمجة"""
        search_frame = QFrame()
        search_frame.setObjectName("search_frame")
        search_frame.setStyleSheet("""
            QFrame#search_frame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)

        search_layout = QVBoxLayout(search_frame)
        search_layout.setSpacing(10)

        # الصف الأول: عنوان البحث مع الإحصائيات
        first_row = QHBoxLayout()

        # عنوان البحث
        search_title = QLabel("🔍 البحث والتصفية")
        search_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 13px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)

        # الإحصائيات المدمجة
        stats_layout = QHBoxLayout()

        # عداد المعاملات
        self.transactions_count_label = QLabel("📊 إجمالي: 0")
        self.transactions_count_label.setFixedHeight(30)
        self.transactions_count_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 6px 10px;
                border-radius: 4px;
                border: 1px solid #ffc107;
                text-align: center;
            }
        """)

        # عداد السلف
        self.advances_label = QLabel("💸 سلف: 0")
        self.advances_label.setFixedHeight(30)
        self.advances_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #dc3545;
                background-color: #f8d7da;
                padding: 6px 10px;
                border-radius: 4px;
                border: 1px solid #dc3545;
                text-align: center;
            }
        """)

        # عداد المكافآت
        self.bonuses_label = QLabel("🎁 مكافآت: 0")
        self.bonuses_label.setFixedHeight(30)
        self.bonuses_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #28a745;
                background-color: #d4edda;
                padding: 6px 10px;
                border-radius: 4px;
                border: 1px solid #28a745;
                text-align: center;
            }
        """)

        # عداد الديون المعلقة
        self.pending_debts_label = QLabel("⏳ معلق: 0")
        self.pending_debts_label.setFixedHeight(30)
        self.pending_debts_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #fd7e14;
                background-color: #fff3cd;
                padding: 6px 10px;
                border-radius: 4px;
                border: 1px solid #fd7e14;
                text-align: center;
            }
        """)

        stats_layout.addWidget(self.transactions_count_label)
        stats_layout.addWidget(self.advances_label)
        stats_layout.addWidget(self.bonuses_label)
        stats_layout.addWidget(self.pending_debts_label)
        stats_layout.addStretch()

        first_row.addWidget(search_title)
        first_row.addStretch()
        first_row.addLayout(stats_layout)

        # الصف الثاني: حقل البحث والتصفية
        second_row = QHBoxLayout()

        search_label = QLabel("🔎 البحث السريع:")
        search_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: 600;
                color: #495057;
                min-width: 100px;
            }
        """)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث باسم الموظف...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                padding: 8px 12px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #ffc107;
                background-color: #f8f9fa;
            }
        """)

        # تصفية حسب نوع المعاملة
        type_label = QLabel("💳 النوع:")
        type_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: 600;
                color: #495057;
                min-width: 50px;
            }
        """)

        self.transaction_type_filter = QComboBox()
        self.transaction_type_filter.setStyleSheet("""
            QComboBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                padding: 6px 10px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #ffffff;
                min-width: 120px;
            }
            QComboBox:focus {
                border-color: #ffc107;
            }
        """)

        # تصفية حسب حالة الدفع
        status_label = QLabel("📊 الحالة:")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: 600;
                color: #495057;
                min-width: 50px;
            }
        """)

        self.payment_status_filter = QComboBox()
        self.payment_status_filter.setStyleSheet("""
            QComboBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                padding: 6px 10px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #ffffff;
                min-width: 100px;
            }
            QComboBox:focus {
                border-color: #ffc107;
            }
        """)

        # زر مسح التصفية
        self.clear_filters_btn = QPushButton("🗑️ مسح")
        self.clear_filters_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #6c757d;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 60px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)

        second_row.addWidget(search_label)
        second_row.addWidget(self.search_input)
        second_row.addWidget(type_label)
        second_row.addWidget(self.transaction_type_filter)
        second_row.addWidget(status_label)
        second_row.addWidget(self.payment_status_filter)
        second_row.addWidget(self.clear_filters_btn)

        search_layout.addLayout(first_row)
        search_layout.addLayout(second_row)

        layout.addWidget(search_frame)
        
        # تحميل بيانات التصفية
        self.load_filter_data()
        
    def create_enhanced_transactions_table(self, layout: QVBoxLayout):
        """إنشاء جدول المعاملات المحسن"""
        self.transactions_table = QTableWidget()

        # إعداد الجدول مع أعمدة محسنة
        headers = [
            "الموظف", "نوع المعاملة", "المبلغ", "تاريخ المعاملة",
            "حالة الدفع", "المبلغ المدفوع", "المبلغ المتبقي", "الملاحظات"
        ]

        column_widths = [180, 140, 130, 130, 130, 130, 130, 220]

        setup_table_widget(
            self.transactions_table,
            headers,
            column_widths,
            sortable=True,
            selection_behavior="row"
        )

        # تحسين مظهر الجدول
        self.transactions_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                gridline-color: #e9ecef;
                selection-background-color: #fff3cd;
                selection-color: #856404;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
                text-align: center;
                color: #2c3e50;
            }
            QTableWidget::item:selected {
                background-color: #fff3cd;
                color: #856404;
                font-weight: bold;
            }
            QTableWidget::item:hover {
                background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #ffc107;
                color: #212529;
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                border: none;
                border-right: 1px solid #e0a800;
                text-align: center;
            }
            QHeaderView::section:hover {
                background-color: #e0a800;
            }
        """)

        # تمكين قائمة السياق
        self.transactions_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.transactions_table.customContextMenuRequested.connect(self.show_context_menu)

        layout.addWidget(self.transactions_table)
        
    def create_compact_action_buttons(self, layout: QVBoxLayout):
        """إنشاء شريط الأزرار المدمج"""
        # إطار الأزرار المدمج
        buttons_frame = QFrame()
        buttons_frame.setObjectName("compact_buttons_frame")
        buttons_frame.setStyleSheet("""
            QFrame#compact_buttons_frame {
                background-color: #ffffff;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 8px;
                margin: 5px 0px;
                max-height: 60px;
            }
        """)

        # تخطيط أفقي مدمج
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(8)
        buttons_layout.setContentsMargins(10, 5, 10, 5)

        # أزرار الإجراءات المدمجة
        self.add_btn = QPushButton("➕ إضافة")
        self.add_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: bold;
                color: white;
                background-color: #28a745;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 80px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        self.edit_btn = QPushButton("✏️ تعديل")
        self.edit_btn.setEnabled(False)
        self.edit_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: bold;
                color: white;
                background-color: #ffc107;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 70px;
                min-height: 35px;
            }
            QPushButton:hover:enabled {
                background-color: #e0a800;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.setEnabled(False)
        self.delete_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: bold;
                color: white;
                background-color: #dc3545;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 60px;
                min-height: 35px;
            }
            QPushButton:hover:enabled {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: bold;
                color: white;
                background-color: #6f42c1;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 70px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)

        # ترتيب الأزرار المدمجة
        buttons_layout.addWidget(self.add_btn)
        buttons_layout.addWidget(self.edit_btn)
        buttons_layout.addWidget(self.delete_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.refresh_btn)

        layout.addWidget(buttons_frame)

    def show_context_menu(self, position):
        """عرض قائمة السياق للجدول"""
        # التحقق من وجود عنصر في الموضع المحدد
        item = self.transactions_table.itemAt(position)
        if not item:
            return

        # إنشاء قائمة السياق
        from PySide6.QtWidgets import QMenu
        context_menu = QMenu(self)

        # الحصول على الصف المحدد
        row = self.transactions_table.currentRow()
        has_selection = 0 <= row < len(self.filtered_data)

        if has_selection:
            employee_name = self.filtered_data[row][0]
            transaction_type = self.filtered_data[row][1]

            # إضافة عنوان القائمة
            title_action = context_menu.addAction(f"💰 {employee_name} - {transaction_type}")
            title_action.setEnabled(False)
            context_menu.addSeparator()

            # إضافة الإجراءات
            edit_action = context_menu.addAction("✏️ تعديل المعاملة")
            edit_action.triggered.connect(self.on_edit_transaction)

            context_menu.addSeparator()

            delete_action = context_menu.addAction("🗑️ حذف المعاملة")
            delete_action.triggered.connect(self.on_delete_transaction)

            # عرض القائمة
            context_menu.exec(self.transactions_table.mapToGlobal(position))
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        # ربط إشارات البحث والتصفية
        self.search_input.textChanged.connect(self.filter_transactions)
        self.transaction_type_filter.currentTextChanged.connect(self.filter_transactions)
        self.payment_status_filter.currentTextChanged.connect(self.filter_transactions)
        self.clear_filters_btn.clicked.connect(self.clear_filters)
        
        # ربط إشارات الجدول
        self.transactions_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.transactions_table.itemDoubleClicked.connect(self.on_item_double_clicked)
        
        # ربط إشارات الأزرار
        self.add_btn.clicked.connect(self.on_add_transaction)
        self.edit_btn.clicked.connect(self.on_edit_transaction)
        self.delete_btn.clicked.connect(self.on_delete_transaction)
        self.refresh_btn.clicked.connect(self.load_transactions)
        
        # ربط إشارات إضافية (إذا كانت موجودة)
        # self.pending_debts_table.itemSelectionChanged.connect(self.on_debt_selection_changed)
        # self.pay_debt_btn.clicked.connect(self.on_pay_debt)
        # self.view_account_btn.clicked.connect(self.on_view_account)
        # self.refresh_debts_btn.clicked.connect(self.load_pending_debts)
        
        # ربط تغيير التبويب (إذا كان موجود)
        # self.tab_widget.currentChanged.connect(self.on_tab_changed)
        
    def load_filter_data(self):
        """تحميل بيانات التصفية"""
        try:
            # تحميل أنواع المعاملات
            type_items = [("جميع الأنواع", "")]
            for trans_type in TransactionType:
                type_items.append((trans_type.value, trans_type.name))
            setup_combobox(self.transaction_type_filter, type_items)
            
            # تحميل حالات الدفع
            status_items = [("جميع الحالات", "")]
            for status in PaymentStatus:
                status_items.append((status.value, status.name))
            setup_combobox(self.payment_status_filter, status_items)
            
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل بيانات التصفية: {e}", "error")
            
    def load_transactions(self):
        """تحميل بيانات المعاملات"""
        try:
            with get_db_session_context() as session:
                transactions = session.query(FinancialTransaction).filter_by(is_active=True).all()
                
                self.transactions_data = []
                for trans in transactions:
                    row_data = [
                        trans.employee.full_name if trans.employee else "",
                        get_transaction_type_display(trans.transaction_type.name),
                        format_currency(trans.amount),
                        format_date(trans.transaction_date),
                        get_payment_status_display(trans.payment_status.name),
                        format_currency(trans.paid_amount),
                        format_currency(trans.remaining_amount),
                        trans.description or ""
                    ]
                    # إضافة ID للاستخدام الداخلي
                    row_data.append(trans.id)
                    self.transactions_data.append(row_data)
                
                # تطبيق التصفية
                self.filter_transactions()
                
                # تحديث الإحصائيات
                total_transactions = len(self.transactions_data)
                advances_count = len([t for t in self.transactions_data if "سلفة" in t[1]])
                bonuses_count = len([t for t in self.transactions_data if "مكافأة" in t[1]])
                pending_count = len([t for t in self.transactions_data if "معلق" in t[4]])

                self.transactions_count_label.setText(f"📊 إجمالي: {total_transactions}")
                self.advances_label.setText(f"💸 سلف: {advances_count}")
                self.bonuses_label.setText(f"🎁 مكافآت: {bonuses_count}")
                self.pending_debts_label.setText(f"⏳ معلق: {pending_count}")
                
                # تحديث الديون المعلقة (إذا كانت موجودة)
                # self.load_pending_debts()
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل بيانات المعاملات: {e}", "error")
            
    def load_pending_debts(self):
        """تحميل الديون المعلقة"""
        try:
            with get_db_session_context() as session:
                pending_debts = FinancialTransaction.get_pending_debts(session)
                
                debts_data = []
                for debt in pending_debts:
                    row_data = [
                        debt.employee.full_name if debt.employee else "",
                        get_transaction_type_display(debt.transaction_type.name),
                        format_currency(debt.amount),
                        format_currency(debt.paid_amount),
                        format_currency(debt.remaining_amount),
                        format_date(debt.transaction_date),
                        get_payment_status_display(debt.payment_status.name)
                    ]
                    # إضافة ID للاستخدام الداخلي
                    row_data.append(debt.id)
                    debts_data.append(row_data)
                
                # تحديث جدول الديون المعلقة
                display_data = [row[:-1] for row in debts_data]
                populate_table(self.pending_debts_table, display_data, editable=False)
                
                # حفظ البيانات للاستخدام الداخلي
                self.pending_debts_data = debts_data
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل الديون المعلقة: {e}", "error")
            
    def filter_transactions(self):
        """تصفية المعاملات"""
        search_text = self.search_input.text().lower()
        transaction_type = get_combobox_value(self.transaction_type_filter)
        payment_status = get_combobox_value(self.payment_status_filter)

        self.filtered_data = []

        for row in self.transactions_data:
            # فلترة النص
            if search_text and search_text not in row[0].lower():
                continue

            # فلترة نوع المعاملة
            if transaction_type and transaction_type != "":
                if transaction_type not in row[1]:
                    continue

            # فلترة حالة الدفع
            if payment_status and payment_status != "":
                if payment_status not in row[4]:
                    continue

            self.filtered_data.append(row)

        # تحديث الجدول
        self.update_table()
        
    def update_table(self):
        """تحديث الجدول"""
        # إزالة العمود الأخير (ID) من العرض
        display_data = [row[:-1] for row in self.filtered_data]
        populate_table(self.transactions_table, display_data, editable=False)
        
    def clear_filters(self):
        """مسح جميع التصفيات"""
        self.search_input.clear()
        self.transaction_type_filter.setCurrentIndex(0)
        self.payment_status_filter.setCurrentIndex(0)
        
    def on_selection_changed(self):
        """معالج تغيير التحديد"""
        has_selection = len(self.transactions_table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        
        if has_selection:
            row = self.transactions_table.currentRow()
            if 0 <= row < len(self.filtered_data):
                transaction_id = self.filtered_data[row][-1]
                self.transaction_selected.emit(transaction_id)
                
    def on_debt_selection_changed(self):
        """معالج تغيير تحديد الديون"""
        # has_selection = len(self.pending_debts_table.selectedItems()) > 0
        # self.pay_debt_btn.setEnabled(has_selection)
        # self.view_account_btn.setEnabled(has_selection)
        pass
        
    def on_item_double_clicked(self, item):
        """معالج النقر المزدوج"""
        self.on_edit_transaction()
        
    def on_add_transaction(self):
        """معالج إضافة معاملة"""
        self.add_transaction_requested.emit()
        
    def on_edit_transaction(self):
        """معالج تعديل معاملة"""
        row = self.transactions_table.currentRow()
        if 0 <= row < len(self.filtered_data):
            transaction_id = self.filtered_data[row][-1]
            self.edit_transaction_requested.emit(transaction_id)
            
    def on_delete_transaction(self):
        """معالج حذف معاملة"""
        row = self.transactions_table.currentRow()
        if 0 <= row < len(self.filtered_data):
            employee_name = self.filtered_data[row][0]
            transaction_type = self.filtered_data[row][1]
            
            if show_confirmation(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف معاملة '{transaction_type}' للموظف '{employee_name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه."
            ):
                transaction_id = self.filtered_data[row][-1]
                self.delete_transaction(transaction_id)
                
    def on_pay_debt(self):
        """معالج تسجيل دفعة"""
        # row = self.pending_debts_table.currentRow()
        # if 0 <= row < len(self.pending_debts_data):
        #     transaction_id = self.pending_debts_data[row][-1]
        #     # سيتم تطوير نافذة تسجيل الدفعة لاحقاً
        #     show_message(self, "معلومات", f"تسجيل دفعة للمعاملة ID: {transaction_id}", "information")
        show_message(self, "معلومات", "سيتم تطوير هذه الميزة قريباً", "information")

    def on_view_account(self):
        """معالج عرض كشف الحساب"""
        # row = self.pending_debts_table.currentRow()
        # if 0 <= row < len(self.pending_debts_data):
        #     # الحصول على معرف الموظف من المعاملة
        #     transaction_id = self.pending_debts_data[row][-1]
        #     # سيتم تطوير كشف الحساب لاحقاً
        #     show_message(self, "معلومات", f"كشف حساب للمعاملة ID: {transaction_id}", "information")
        show_message(self, "معلومات", "سيتم تطوير هذه الميزة قريباً", "information")

    def on_tab_changed(self, index):
        """معالج تغيير التبويب"""
        # if index == 1:  # تبويب الديون المعلقة
        #     self.load_pending_debts()
        pass
            
    def delete_transaction(self, transaction_id: int):
        """حذف معاملة"""
        try:
            with get_db_session_context() as session:
                transaction = session.query(FinancialTransaction).filter_by(id=transaction_id).first()
                if transaction:
                    transaction.soft_delete()
                    session.commit()
                    
                    show_message(self, "نجح", "تم حذف المعاملة بنجاح", "information")
                    self.load_transactions()
                else:
                    show_message(self, "خطأ", "المعاملة غير موجودة", "error")
                    
        except Exception as e:
            show_message(self, "خطأ", f"فشل في حذف المعاملة: {e}", "error")
            
    def get_selected_transaction_id(self) -> int:
        """الحصول على معرف المعاملة المحددة"""
        row = self.transactions_table.currentRow()
        if 0 <= row < len(self.filtered_data):
            return self.filtered_data[row][-1]
        return -1
