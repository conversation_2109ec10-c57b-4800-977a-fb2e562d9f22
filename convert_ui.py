#!/usr/bin/env python3
"""
سكريبت تحويل ملفات UI إلى Python
UI to Python Converter Script
"""

import os
import subprocess
import sys

def convert_ui_to_py(ui_file, py_file):
    """تحويل ملف .ui إلى .py"""
    try:
        # تشغيل أمر التحويل
        result = subprocess.run([
            'pyside6-uic', 
            ui_file, 
            '-o', 
            py_file
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ تم تحويل {ui_file} إلى {py_file} بنجاح!")
            return True
        else:
            print(f"❌ خطأ في التحويل: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ PySide6 غير مثبت أو غير موجود في PATH")
        print("قم بتثبيته باستخدام: pip install PySide6")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🎨 سكريبت تحويل ملفات Qt Designer")
    print("=" * 40)
    
    # قائمة الملفات للتحويل
    ui_files = [
        ("ui_files/main_window.ui", "src/ui/ui_main_window.py"),
        # يمكن إضافة المزيد من الملفات هنا
    ]
    
    # إنشاء مجلد الوجهة إذا لم يكن موجوداً
    os.makedirs("src/ui", exist_ok=True)
    
    success_count = 0
    total_count = len(ui_files)
    
    for ui_file, py_file in ui_files:
        if os.path.exists(ui_file):
            print(f"\n🔄 تحويل {ui_file}...")
            if convert_ui_to_py(ui_file, py_file):
                success_count += 1
        else:
            print(f"⚠️ الملف غير موجود: {ui_file}")
    
    print(f"\n📊 النتائج:")
    print(f"✅ نجح: {success_count}/{total_count}")
    print(f"❌ فشل: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n🎉 تم تحويل جميع الملفات بنجاح!")
    else:
        print(f"\n⚠️ فشل في تحويل {total_count - success_count} ملف")

if __name__ == "__main__":
    main()
