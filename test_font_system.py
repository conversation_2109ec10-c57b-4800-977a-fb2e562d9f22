#!/usr/bin/env python3
"""
اختبار نظام الخطوط المحسن
Test Enhanced Font System
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget
from PySide6.QtWidgets import QPushButton, QLabel, QLineEdit, QComboBox, QTableWidget
from PySide6.QtWidgets import QGroupBox, QCheckBox, QTabWidget, QTextEdit
from PySide6.QtCore import Qt


def create_test_window():
    """إنشاء نافذة اختبار لعرض جميع العناصر"""
    
    class FontTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار نظام الخطوط المحسن")
            self.setGeometry(100, 100, 800, 600)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            
            # العنوان
            title = QLabel("🔤 اختبار نظام الخطوط المحسن")
            title.setObjectName("app_title")
            title.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(title)
            
            # مجموعة الأزرار
            buttons_group = QGroupBox("الأزرار")
            buttons_layout = QHBoxLayout(buttons_group)
            
            btn1 = QPushButton("زر عادي")
            btn2 = QPushButton("زر الشريط الجانبي")
            btn2.setObjectName("sidebar_button")
            btn3 = QPushButton("زر آخر")
            
            buttons_layout.addWidget(btn1)
            buttons_layout.addWidget(btn2)
            buttons_layout.addWidget(btn3)
            main_layout.addWidget(buttons_group)
            
            # مجموعة التسميات
            labels_group = QGroupBox("التسميات")
            labels_layout = QVBoxLayout(labels_group)
            
            label1 = QLabel("تسمية عادية")
            label2 = QLabel("تسمية مهمة")
            label2.setStyleSheet("font-weight: bold;")
            label3 = QLabel("تسمية أخرى")
            
            labels_layout.addWidget(label1)
            labels_layout.addWidget(label2)
            labels_layout.addWidget(label3)
            main_layout.addWidget(labels_group)
            
            # مجموعة حقول الإدخال
            inputs_group = QGroupBox("حقول الإدخال")
            inputs_layout = QVBoxLayout(inputs_group)
            
            line_edit = QLineEdit("حقل نص")
            combo_box = QComboBox()
            combo_box.addItems(["خيار 1", "خيار 2", "خيار 3"])
            text_edit = QTextEdit("منطقة نص متعددة الأسطر")
            text_edit.setMaximumHeight(80)
            
            inputs_layout.addWidget(line_edit)
            inputs_layout.addWidget(combo_box)
            inputs_layout.addWidget(text_edit)
            main_layout.addWidget(inputs_group)
            
            # مجموعة العناصر الأخرى
            others_group = QGroupBox("عناصر أخرى")
            others_layout = QVBoxLayout(others_group)
            
            checkbox = QCheckBox("مربع اختيار")
            
            # جدول صغير
            table = QTableWidget(3, 2)
            table.setHorizontalHeaderLabels(["العمود 1", "العمود 2"])
            table.setMaximumHeight(120)
            
            others_layout.addWidget(checkbox)
            others_layout.addWidget(table)
            main_layout.addWidget(others_group)
            
            # أزرار التحكم في حجم الخط
            font_controls = QGroupBox("التحكم في حجم الخط")
            font_layout = QHBoxLayout(font_controls)
            
            decrease_btn = QPushButton("تصغير الخط")
            increase_btn = QPushButton("تكبير الخط")
            reset_btn = QPushButton("إعادة تعيين")
            
            decrease_btn.clicked.connect(self.decrease_font)
            increase_btn.clicked.connect(self.increase_font)
            reset_btn.clicked.connect(self.reset_font)
            
            font_layout.addWidget(decrease_btn)
            font_layout.addWidget(increase_btn)
            font_layout.addWidget(reset_btn)
            main_layout.addWidget(font_controls)
            
            self.current_font_size = 12
        
        def decrease_font(self):
            """تصغير الخط"""
            if self.current_font_size > 8:
                self.current_font_size -= 1
                self.apply_font_size()
        
        def increase_font(self):
            """تكبير الخط"""
            if self.current_font_size < 24:
                self.current_font_size += 1
                self.apply_font_size()
        
        def reset_font(self):
            """إعادة تعيين حجم الخط"""
            self.current_font_size = 12
            self.apply_font_size()
        
        def apply_font_size(self):
            """تطبيق حجم الخط الجديد"""
            app = QApplication.instance()
            if not app:
                return
            
            font_size = self.current_font_size
            
            # إنشاء أنماط CSS
            font_style = f"""
            * {{
                font-family: "Arial", "Tahoma", sans-serif;
                font-size: {font_size}px;
            }}
            
            QPushButton {{
                font-size: {font_size}px;
                font-weight: 500;
                padding: 8px 16px;
            }}
            
            QPushButton#sidebar_button {{
                font-size: {font_size}px;
                font-weight: 500;
                background-color: #3498db;
                color: white;
                border-radius: 5px;
            }}
            
            QLabel {{
                font-size: {font_size}px;
            }}
            
            QLabel#app_title {{
                font-size: {font_size + 6}px;
                font-weight: bold;
                color: #2c3e50;
                margin: 10px;
            }}
            
            QLineEdit, QTextEdit, QComboBox {{
                font-size: {font_size}px;
                padding: 6px;
            }}
            
            QTableWidget {{
                font-size: {font_size}px;
            }}
            
            QHeaderView::section {{
                font-size: {font_size}px;
                font-weight: bold;
                padding: 8px;
            }}
            
            QGroupBox {{
                font-size: {font_size}px;
                font-weight: bold;
                margin: 10px;
                padding-top: 10px;
            }}
            
            QCheckBox {{
                font-size: {font_size}px;
            }}
            """
            
            app.setStyleSheet(font_style)
            print(f"تم تطبيق حجم الخط: {font_size}px")
    
    return FontTestWindow()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نظام الخطوط المحسن")
    print("=" * 50)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء نافذة الاختبار
        window = create_test_window()
        window.show()
        
        print("✅ تم فتح نافذة اختبار الخطوط!")
        print("\n🎯 يمكنك الآن:")
        print("1. مشاهدة جميع العناصر بحجم الخط الحالي")
        print("2. استخدام أزرار التحكم لتغيير حجم الخط")
        print("3. مراقبة تطبيق التغييرات على جميع العناصر")
        print("4. اختبار أحجام خطوط مختلفة (8-24)")
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
