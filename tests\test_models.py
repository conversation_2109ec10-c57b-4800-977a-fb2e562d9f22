"""
اختبارات النماذج
Models Tests
"""

import pytest
from datetime import date, datetime
from decimal import Decimal
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.models import (
    Base, Employee, Department, JobTitle, SalaryRecord, 
    FinancialTransaction, AuditLog, EmploymentStatus, 
    TransactionType, PaymentStatus, ActionType
)


@pytest.fixture
def db_session():
    """إنشاء جلسة قاعدة بيانات للاختبار"""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    yield session
    
    session.close()


@pytest.fixture
def sample_department(db_session):
    """إنشاء قسم نموذجي للاختبار"""
    department = Department(
        name="قسم تقنية المعلومات",
        description="قسم مسؤول عن تقنية المعلومات",
        manager_name="أحمد محمد"
    )
    db_session.add(department)
    db_session.commit()
    return department


@pytest.fixture
def sample_job_title(db_session):
    """إنشاء عنوان وظيفي نموذجي للاختبار"""
    job_title = JobTitle(
        title="مطور برمجيات",
        description="مطور تطبيقات ومواقع ويب",
        base_salary=Decimal("5000.00")
    )
    db_session.add(job_title)
    db_session.commit()
    return job_title


@pytest.fixture
def sample_employee(db_session, sample_department, sample_job_title):
    """إنشاء موظف نموذجي للاختبار"""
    employee = Employee(
        employee_number="EMP001",
        full_name="محمد أحمد علي",
        national_id="1234567890",
        phone="0501234567",
        email="<EMAIL>",
        hire_date=date(2023, 1, 1),
        basic_salary=Decimal("5000.00"),
        employment_status=EmploymentStatus.ACTIVE,
        department_id=sample_department.id,
        job_title_id=sample_job_title.id
    )
    db_session.add(employee)
    db_session.commit()
    return employee


class TestDepartment:
    """اختبارات نموذج القسم"""
    
    def test_create_department(self, db_session):
        """اختبار إنشاء قسم جديد"""
        department = Department(
            name="قسم المحاسبة",
            description="قسم مسؤول عن المحاسبة والمالية",
            manager_name="سارة أحمد"
        )
        
        db_session.add(department)
        db_session.commit()
        
        assert department.id is not None
        assert department.name == "قسم المحاسبة"
        assert department.is_active is True
        assert department.created_at is not None
        
    def test_department_str_representation(self, sample_department):
        """اختبار تمثيل القسم كنص"""
        assert str(sample_department) == "قسم تقنية المعلومات"
        
    def test_department_employees_relationship(self, db_session, sample_department, sample_employee):
        """اختبار علاقة القسم بالموظفين"""
        assert len(sample_department.employees) == 1
        assert sample_department.employees[0] == sample_employee


class TestJobTitle:
    """اختبارات نموذج العنوان الوظيفي"""
    
    def test_create_job_title(self, db_session):
        """اختبار إنشاء عنوان وظيفي جديد"""
        job_title = JobTitle(
            title="محاسب",
            description="محاسب مالي",
            base_salary=Decimal("4000.00")
        )
        
        db_session.add(job_title)
        db_session.commit()
        
        assert job_title.id is not None
        assert job_title.title == "محاسب"
        assert job_title.base_salary == Decimal("4000.00")
        assert job_title.is_active is True
        
    def test_job_title_str_representation(self, sample_job_title):
        """اختبار تمثيل العنوان الوظيفي كنص"""
        assert str(sample_job_title) == "مطور برمجيات"


class TestEmployee:
    """اختبارات نموذج الموظف"""
    
    def test_create_employee(self, db_session, sample_department, sample_job_title):
        """اختبار إنشاء موظف جديد"""
        employee = Employee(
            employee_number="EMP002",
            full_name="فاطمة محمد",
            national_id="9876543210",
            phone="0509876543",
            email="<EMAIL>",
            hire_date=date(2023, 2, 1),
            basic_salary=Decimal("4500.00"),
            employment_status=EmploymentStatus.ACTIVE,
            department_id=sample_department.id,
            job_title_id=sample_job_title.id
        )
        
        db_session.add(employee)
        db_session.commit()
        
        assert employee.id is not None
        assert employee.employee_number == "EMP002"
        assert employee.full_name == "فاطمة محمد"
        assert employee.is_active is True
        
    def test_employee_str_representation(self, sample_employee):
        """اختبار تمثيل الموظف كنص"""
        assert str(sample_employee) == "EMP001 - محمد أحمد علي"
        
    def test_employee_relationships(self, sample_employee, sample_department, sample_job_title):
        """اختبار علاقات الموظف"""
        assert sample_employee.department == sample_department
        assert sample_employee.job_title == sample_job_title
        
    def test_employee_current_balance(self, db_session, sample_employee):
        """اختبار حساب الرصيد الحالي للموظف"""
        # إضافة معاملة مالية
        transaction = FinancialTransaction(
            employee_id=sample_employee.id,
            transaction_type=TransactionType.ADVANCE,
            amount=Decimal("1000.00"),
            remaining_amount=Decimal("500.00"),
            transaction_date=date.today(),
            description="سلفة",
            payment_status=PaymentStatus.PARTIAL
        )
        db_session.add(transaction)
        db_session.commit()
        
        balance = sample_employee.get_current_balance(db_session)
        assert balance == Decimal("-500.00")  # رصيد سالب (دين)


class TestFinancialTransaction:
    """اختبارات نموذج المعاملة المالية"""
    
    def test_create_financial_transaction(self, db_session, sample_employee):
        """اختبار إنشاء معاملة مالية جديدة"""
        transaction = FinancialTransaction(
            employee_id=sample_employee.id,
            transaction_type=TransactionType.ADVANCE,
            amount=Decimal("2000.00"),
            remaining_amount=Decimal("2000.00"),
            transaction_date=date.today(),
            description="سلفة شهرية",
            payment_status=PaymentStatus.PENDING
        )
        
        db_session.add(transaction)
        db_session.commit()
        
        assert transaction.id is not None
        assert transaction.amount == Decimal("2000.00")
        assert transaction.remaining_amount == Decimal("2000.00")
        assert transaction.is_active is True
        
    def test_transaction_str_representation(self, db_session, sample_employee):
        """اختبار تمثيل المعاملة المالية كنص"""
        transaction = FinancialTransaction(
            employee_id=sample_employee.id,
            transaction_type=TransactionType.BONUS,
            amount=Decimal("500.00"),
            transaction_date=date.today(),
            description="مكافأة"
        )
        
        expected = f"مكافأة - {sample_employee.full_name} - 500.00"
        assert str(transaction) == expected
        
    def test_get_pending_debts(self, db_session, sample_employee):
        """اختبار الحصول على الديون المعلقة"""
        # إضافة دين معلق
        transaction = FinancialTransaction(
            employee_id=sample_employee.id,
            transaction_type=TransactionType.ADVANCE,
            amount=Decimal("1500.00"),
            remaining_amount=Decimal("1000.00"),
            transaction_date=date.today(),
            description="سلفة معلقة",
            payment_status=PaymentStatus.PARTIAL
        )
        db_session.add(transaction)
        db_session.commit()
        
        pending_debts = FinancialTransaction.get_pending_debts(db_session)
        assert len(pending_debts) == 1
        assert pending_debts[0].remaining_amount > 0


class TestSalaryRecord:
    """اختبارات نموذج سجل الراتب"""
    
    def test_create_salary_record(self, db_session, sample_employee):
        """اختبار إنشاء سجل راتب جديد"""
        salary_record = SalaryRecord(
            employee_id=sample_employee.id,
            month=1,
            year=2024,
            basic_salary=Decimal("5000.00"),
            allowances=Decimal("500.00"),
            deductions=Decimal("200.00"),
            gross_salary=Decimal("5300.00"),
            net_salary=Decimal("5100.00")
        )
        
        db_session.add(salary_record)
        db_session.commit()
        
        assert salary_record.id is not None
        assert salary_record.month == 1
        assert salary_record.year == 2024
        assert salary_record.net_salary == Decimal("5100.00")
        assert salary_record.is_active is True
        
    def test_salary_record_str_representation(self, db_session, sample_employee):
        """اختبار تمثيل سجل الراتب كنص"""
        salary_record = SalaryRecord(
            employee_id=sample_employee.id,
            month=2,
            year=2024,
            basic_salary=Decimal("5000.00"),
            net_salary=Decimal("5000.00")
        )
        
        expected = f"راتب {sample_employee.full_name} - 2024/2"
        assert str(salary_record) == expected


class TestAuditLog:
    """اختبارات نموذج سجل التدقيق"""
    
    def test_create_audit_log(self, db_session):
        """اختبار إنشاء سجل تدقيق جديد"""
        audit_log = AuditLog(
            action_type=ActionType.CREATE,
            table_name="employees",
            record_id="1",
            description="إضافة موظف جديد",
            user_name="admin",
            ip_address="***********"
        )
        
        db_session.add(audit_log)
        db_session.commit()
        
        assert audit_log.id is not None
        assert audit_log.action_type == ActionType.CREATE
        assert audit_log.table_name == "employees"
        assert audit_log.timestamp is not None
        
    def test_log_action_method(self, db_session):
        """اختبار طريقة تسجيل العملية"""
        AuditLog.log_action(
            session=db_session,
            action_type=ActionType.UPDATE,
            table_name="employees",
            record_id="1",
            description="تحديث بيانات الموظف",
            user_name="admin"
        )
        
        logs = db_session.query(AuditLog).all()
        assert len(logs) == 1
        assert logs[0].action_type == ActionType.UPDATE
        
    def test_get_recent_logs(self, db_session):
        """اختبار الحصول على السجلات الحديثة"""
        # إضافة عدة سجلات
        for i in range(5):
            AuditLog.log_action(
                session=db_session,
                action_type=ActionType.VIEW,
                description=f"عملية رقم {i}",
                user_name="user"
            )
        
        recent_logs = AuditLog.get_recent_logs(db_session, limit=3)
        assert len(recent_logs) == 3
        
        # التأكد من الترتيب (الأحدث أولاً)
        timestamps = [log.timestamp for log in recent_logs]
        assert timestamps == sorted(timestamps, reverse=True)
