#!/usr/bin/env python3
"""
اختبار النقر على زر تفاصيل الموظف
Test Employee Details Button Click
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication
from src.database import db_manager
from src.views.employees_view import EmployeesView


def test_button_click():
    """اختبار النقر على زر التفاصيل"""
    print("🧪 اختبار النقر على زر تفاصيل الموظف...")
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(app.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    app.setStyleSheet(f.read())
        except:
            pass
        
        # إنشاء عرض الموظفين
        print("🔧 إنشاء عرض الموظفين...")
        employees_view = EmployeesView()
        
        # عرض النافذة
        employees_view.show()
        
        print("✅ تم فتح نافذة الموظفين!")
        print("\nيمكنك الآن:")
        print("1. تحديد موظف من القائمة")
        print("2. النقر على زر 'عرض التفاصيل'")
        print("3. مراقبة الرسائل في وحدة التحكم")
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار زر تفاصيل الموظف")
    print("=" * 40)
    
    result = test_button_click()
    
    if result == 0:
        print("✅ الاختبار انتهى بنجاح!")
    else:
        print("💥 حدث خطأ في الاختبار!")
    
    return result


if __name__ == "__main__":
    sys.exit(main())
