#!/usr/bin/env python3
"""
اختبار بطاقة المستخدم الجديدة بتصميم عمودي
Test New Vertical User Card Design
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QFrame, QHBoxLayout, QPushButton
from PySide6.QtCore import Qt


def create_new_user_card_test():
    """إنشاء نافذة اختبار بطاقة المستخدم الجديدة"""
    
    class NewUserCardTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار بطاقة المستخدم الجديدة - تصميم عمودي واضح")
            self.setGeometry(100, 100, 1200, 800)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            # إنشاء الشريط العلوي الجديد
            self.create_new_header()
            main_layout.addWidget(self.header)
            
            # محتوى الاختبار
            content_widget = QWidget()
            content_layout = QVBoxLayout(content_widget)
            content_layout.setContentsMargins(30, 30, 30, 30)
            content_layout.setSpacing(25)
            
            # عنوان الاختبار
            test_title = QLabel("🔄 اختبار بطاقة المستخدم الجديدة - تصميم عمودي واضح")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 20px;
                    padding: 20px;
                    background-color: #3498db;
                    color: white;
                    border-radius: 12px;
                }
            """)
            content_layout.addWidget(test_title)
            
            # معلومات التصميم الجديد
            design_info = QLabel("""
            🎨 التصميم الجديد لبطاقة المستخدم:
            
            📐 التخطيط العمودي الجديد:
            • تغيير من التخطيط الأفقي إلى العمودي
            • حجم جديد: 200×120px (أطول وأضيق)
            • ترتيب العناصر من الأعلى للأسفل:
              1. أيقونة المستخدم في الأعلى
              2. اسم المستخدم في الوسط
              3. حالة الاتصال في الأسفل
            
            🎯 الألوان الواضحة:
            • خلفية بيضاء نظيفة (#ffffff)
            • حدود زرقاء واضحة (#3498db)
            • أيقونة زرقاء على خلفية رمادية فاتحة
            • نص أسود واضح (#2c3e50)
            • حالة اتصال خضراء (#27ae60)
            
            👨‍💼 الأيقونة المحسنة:
            • حجم أكبر: 36px (كان 32px)
            • خلفية دائرية رمادية فاتحة
            • حدود زرقاء سميكة (3px)
            • توسيط مثالي في أعلى البطاقة
            • حجم ثابت: 60×60px
            
            📝 النصوص الواضحة:
            • اسم المستخدم: "المدير العام" (16px، عريض)
            • حالة الاتصال: "🟢 متصل" (12px، عريض)
            • جميع النصوص متوسطة ومقروءة
            • ألوان متباينة للوضوح
            
            🔧 التحسينات التقنية:
            • إزالة التعقيدات والتدرجات المشتتة
            • استخدام ألوان صلبة واضحة
            • تباعد مناسب بين العناصر (8px)
            • هوامش متوازنة (15px من كل جانب)
            • ظلال خفيفة للعمق بدون إفراط
            
            ✨ فوائد التصميم الجديد:
            • وضوح أكبر في القراءة
            • تنظيم أفضل للمعلومات
            • مساحة أكثر كفاءة
            • تباين لوني ممتاز
            • سهولة في التمييز
            • تصميم نظيف ومهني
            """)
            design_info.setAlignment(Qt.AlignLeft)
            design_info.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #2c3e50;
                    background-color: #f8f9fa;
                    padding: 25px;
                    border-radius: 12px;
                    border-left: 4px solid #3498db;
                    line-height: 1.6;
                    border: 1px solid #dee2e6;
                }
            """)
            content_layout.addWidget(design_info)
            
            # مقارنة التصاميم
            comparison_frame = QFrame()
            comparison_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 2px solid #dee2e6;
                    border-radius: 12px;
                    padding: 20px;
                }
            """)
            
            comparison_layout = QVBoxLayout(comparison_frame)
            
            comparison_title = QLabel("📊 مقارنة التصاميم")
            comparison_title.setAlignment(Qt.AlignCenter)
            comparison_title.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 15px;
                    padding: 10px;
                    background-color: #ecf0f1;
                    border-radius: 8px;
                }
            """)
            comparison_layout.addWidget(comparison_title)
            
            comparison_text = QLabel("""
            📏 مقارنة شاملة:
            
            ┌─────────────────────┬─────────────────┬─────────────────┐
            │ الخاصية             │ التصميم السابق   │ التصميم الجديد   │
            ├─────────────────────┼─────────────────┼─────────────────┤
            │ التخطيط             │ أفقي            │ عمودي           │
            │ الحجم               │ 280×90px        │ 200×120px       │
            │ الخلفية             │ تدرج أخضر       │ أبيض نظيف       │
            │ الحدود              │ أخضر داكن       │ أزرق واضح       │
            │ الأيقونة            │ 32px أفقي      │ 36px عمودي     │
            │ النصوص             │ 3 نصوص         │ 2 نص واضح      │
            │ الوضوح             │ متوسط           │ ممتاز           │
            │ سهولة القراءة       │ جيد             │ ممتاز           │
            │ التباين اللوني      │ منخفض           │ عالي            │
            └─────────────────────┴─────────────────┴─────────────────┘
            
            ❌ مشاكل التصميم السابق:
            • تدرجات لونية معقدة
            • نصوص كثيرة مزدحمة
            • تباين لوني ضعيف
            • صعوبة في القراءة
            
            ✅ مزايا التصميم الجديد:
            • ألوان واضحة ومتباينة
            • تنظيم عمودي منطقي
            • نصوص أقل وأوضح
            • سهولة في القراءة والفهم
            • تصميم نظيف ومهني
            
            🎯 النتيجة: تحسن كبير في الوضوح والاحترافية!
            """)
            comparison_text.setStyleSheet("""
                QLabel {
                    font-size: 13px;
                    color: #34495e;
                    font-family: 'Courier New', monospace;
                    line-height: 1.5;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    border: 1px solid #dee2e6;
                }
            """)
            comparison_layout.addWidget(comparison_text)
            
            content_layout.addWidget(comparison_frame)
            content_layout.addStretch()
            
            main_layout.addWidget(content_widget)
            
        def create_new_header(self):
            """إنشاء الشريط العلوي مع التصميم الجديد"""
            self.header = QFrame()
            self.header.setObjectName("main_header_frame")
            self.header.setFixedHeight(100)

            header_layout = QHBoxLayout(self.header)
            header_layout.setContentsMargins(30, 20, 30, 20)
            header_layout.setSpacing(30)

            # قسم العنوان المحسن
            title_section = QVBoxLayout()
            title_section.setSpacing(8)

            # العنوان الرئيسي المحسن
            main_title = QLabel("🏢 نظام إدارة الموارد البشرية")
            main_title.setAlignment(Qt.AlignLeft)
            main_title.setStyleSheet("""
                QLabel {
                    font-size: 28px;
                    font-weight: bold;
                    color: #2c3e50;
                    letter-spacing: 2px;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                              stop: 0 rgba(52, 152, 219, 0.1),
                                              stop: 1 rgba(155, 89, 182, 0.1));
                    padding: 8px 15px;
                    border-radius: 8px;
                    border-left: 4px solid #3498db;
                }
            """)

            # العنوان الفرعي المحسن
            subtitle = QLabel("💼 نظام شامل ومتطور لإدارة الموظفين والمعاملات المالية")
            subtitle.setAlignment(Qt.AlignLeft)
            subtitle.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    font-weight: 500;
                    color: #7f8c8d;
                    letter-spacing: 1px;
                    padding: 5px 15px;
                    font-style: italic;
                }
            """)

            title_section.addWidget(main_title)
            title_section.addWidget(subtitle)

            # قسم معلومات المستخدم الجديد
            user_section = QHBoxLayout()
            user_section.setSpacing(20)

            # بطاقة المستخدم بتصميم عمودي واضح
            user_card = QFrame()
            user_card.setFixedSize(200, 120)
            user_card.setStyleSheet("""
                QFrame {
                    background-color: #ffffff;
                    border: 3px solid #3498db;
                    border-radius: 20px;
                    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.2);
                }
            """)
            
            user_card_layout = QVBoxLayout(user_card)
            user_card_layout.setContentsMargins(15, 15, 15, 15)
            user_card_layout.setSpacing(8)

            # أيقونة المستخدم في الأعلى
            user_icon = QLabel("👨‍💼")
            user_icon.setStyleSheet("""
                QLabel {
                    font-size: 36px;
                    color: #3498db;
                    background-color: #ecf0f1;
                    border-radius: 30px;
                    padding: 15px;
                    border: 3px solid #3498db;
                }
            """)
            user_icon.setAlignment(Qt.AlignCenter)
            user_icon.setFixedSize(60, 60)
            
            # توسيط الأيقونة
            icon_layout = QHBoxLayout()
            icon_layout.addStretch()
            icon_layout.addWidget(user_icon)
            icon_layout.addStretch()
            user_card_layout.addLayout(icon_layout)
            
            # اسم المستخدم
            user_name = QLabel("المدير العام")
            user_name.setStyleSheet("""
                QLabel {
                    color: #2c3e50;
                    font-size: 16px;
                    font-weight: bold;
                    text-align: center;
                }
            """)
            user_name.setAlignment(Qt.AlignCenter)
            user_card_layout.addWidget(user_name)
            
            # حالة الاتصال
            status_label = QLabel("🟢 متصل")
            status_label.setStyleSheet("""
                QLabel {
                    color: #27ae60;
                    font-size: 12px;
                    font-weight: 600;
                    text-align: center;
                }
            """)
            status_label.setAlignment(Qt.AlignCenter)
            user_card_layout.addWidget(status_label)

            # زر الإعدادات (نفس الشكل السابق)
            settings_btn = QPushButton("⚙️ الإعدادات")
            settings_btn.setFixedSize(140, 50)
            settings_btn.setToolTip("إعدادات النظام")
            settings_btn.clicked.connect(lambda: print("🔄 تم النقر على زر الإعدادات"))
            settings_btn.setStyleSheet("""
                QPushButton {
                    font-size: 14px;
                    font-weight: bold;
                    color: white;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #95a5a6, stop: 1 #7f8c8d);
                    border: none;
                    border-radius: 8px;
                    padding: 12px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #bdc3c7, stop: 1 #95a5a6);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #7f8c8d, stop: 1 #6c7b7d);
                }
            """)

            user_section.addWidget(user_card)
            user_section.addWidget(settings_btn)

            # تجميع الأقسام
            header_layout.addLayout(title_section)
            header_layout.addStretch()
            header_layout.addLayout(user_section)
    
    return NewUserCardTest()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار بطاقة المستخدم الجديدة - تصميم عمودي واضح")
    print("=" * 70)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            enhanced_style_file = styles_dir / "enhanced_dashboard.qss"
            
            combined_styles = ""
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read() + "\n"
                    
            if enhanced_style_file.exists():
                with open(enhanced_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read()
                    
            app.setStyleSheet(combined_styles)
            print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_new_user_card_test()
        window.show()
        
        print("✅ تم فتح نافذة اختبار التصميم الجديد!")
        print("\n🔄 التصميم الجديد:")
        print("• تخطيط عمودي بدلاً من الأفقي")
        print("• حجم جديد: 200×120px")
        print("• خلفية بيضاء نظيفة")
        print("• حدود زرقاء واضحة")
        print("• أيقونة كبيرة في الأعلى (36px)")
        print("• نصوص واضحة ومتوسطة")
        
        print("\n🎨 الألوان الجديدة:")
        print("• خلفية: أبيض نظيف")
        print("• حدود: أزرق واضح")
        print("• أيقونة: أزرق على رمادي فاتح")
        print("• نصوص: أسود وأخضر واضح")
        
        print("\n🎯 الفوائد:")
        print("• وضوح أكبر في القراءة")
        print("• تنظيم أفضل للمعلومات")
        print("• تباين لوني ممتاز")
        print("• تصميم نظيف ومهني")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
