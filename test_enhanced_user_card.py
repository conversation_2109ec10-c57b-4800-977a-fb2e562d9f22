#!/usr/bin/env python3
"""
اختبار بطاقة المستخدم المحسنة واسم البرنامج
Test Enhanced User Card and Program Name
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QFrame, QHBoxLayout, QPushButton
from PySide6.QtCore import Qt


def create_enhanced_user_card_test():
    """إنشاء نافذة اختبار بطاقة المستخدم المحسنة"""
    
    class EnhancedUserCardTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار بطاقة المستخدم المحسنة واسم البرنامج")
            self.setGeometry(50, 50, 1400, 900)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            # إنشاء الشريط العلوي المحسن
            self.create_enhanced_header()
            main_layout.addWidget(self.header)
            
            # محتوى الاختبار
            content_widget = QWidget()
            content_layout = QVBoxLayout(content_widget)
            content_layout.setContentsMargins(30, 30, 30, 30)
            content_layout.setSpacing(25)
            
            # عنوان الاختبار
            test_title = QLabel("✨ اختبار بطاقة المستخدم المحسنة واسم البرنامج")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 26px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 20px;
                    padding: 20px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #e74c3c, stop: 1 #c0392b);
                    color: white;
                    border-radius: 12px;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                }
            """)
            content_layout.addWidget(test_title)
            
            # معلومات التحسينات
            improvements_info = QLabel("""
            🎨 التحسينات المطبقة على اسم البرنامج وبطاقة المستخدم:
            
            🏢 اسم البرنامج المحسن:
            • حجم خط كبير: 28px (كان 24px) - زيادة 17%
            • لون مميز: #2c3e50 مع خلفية متدرجة
            • تباعد أحرف محسن: 2px للوضوح
            • ظلال نصية للعمق البصري
            • خلفية متدرجة: أزرق → بنفسجي
            • حدود جانبية ملونة (4px أزرق)
            • أيقونة مبنى مميزة: 🏢
            
            👨‍💼 بطاقة المستخدم المحسنة:
            • حجم أكبر: 280×90px (كان 200×80px)
            • لون أخضر مميز: تدرج من #27ae60 إلى #229954
            • حدود سميكة: 3px أخضر داكن
            • ظلال خارجية للعمق
            • أيقونة مدير محسنة: 👨‍💼 (32px)
            • خلفية دائرية للأيقونة مع تدرج أبيض شفاف
            • حدود للأيقونة: 2px أبيض شفاف
            
            📝 النصوص المحسنة في البطاقة:
            • رسالة الترحيب: "🌟 مرحباً بك في النظام" (14px)
            • اسم المستخدم: "👑 المدير العام" (18px)
            • حالة الاتصال: "🟢 متصل الآن" (12px)
            • تباعد أحرف محسن لجميع النصوص
            • ظلال نصية للعمق
            
            🎯 الألوان والتدرجات:
            • اسم البرنامج: خلفية أزرق → بنفسجي فاتح
            • بطاقة المستخدم: تدرج أخضر → أخضر داكن
            • أيقونة المستخدم: تدرج أبيض شفاف
            • النصوص: أبيض مع شفافيات متدرجة
            • الحدود: ألوان متناسقة مع الخلفيات
            
            📐 الأبعاد المحسنة:
            • ارتفاع الشريط: 100px (مناسب للمحتوى الجديد)
            • عرض بطاقة المستخدم: 280px (+80px)
            • ارتفاع بطاقة المستخدم: 90px (+10px)
            • حجم أيقونة المستخدم: 50×50px (+10px)
            • زر الإعدادات: 140×50px (بدون تغيير)
            
            ✨ الميزات الجديدة:
            • أيقونات تعبيرية مميزة (🏢، 👨‍💼، 🌟، 👑، 🟢)
            • تدرجات لونية احترافية
            • ظلال وتأثيرات بصرية
            • تباعد أحرف محسن للوضوح
            • معلومة حالة الاتصال
            • تصميم متناسق ومتوازن
            """)
            improvements_info.setAlignment(Qt.AlignLeft)
            improvements_info.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #2c3e50;
                    background-color: #f8f9fa;
                    padding: 25px;
                    border-radius: 12px;
                    border-left: 4px solid #e74c3c;
                    line-height: 1.6;
                    border: 1px solid #dee2e6;
                }
            """)
            content_layout.addWidget(improvements_info)
            
            # مقارنة قبل وبعد
            comparison_frame = QFrame()
            comparison_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 2px solid #dee2e6;
                    border-radius: 12px;
                    padding: 20px;
                }
            """)
            
            comparison_layout = QVBoxLayout(comparison_frame)
            
            comparison_title = QLabel("📊 مقارنة التحسينات")
            comparison_title.setAlignment(Qt.AlignCenter)
            comparison_title.setStyleSheet("""
                QLabel {
                    font-size: 20px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 15px;
                    padding: 12px;
                    background-color: #ecf0f1;
                    border-radius: 8px;
                }
            """)
            comparison_layout.addWidget(comparison_title)
            
            comparison_text = QLabel("""
            📏 جدول مقارنة التحسينات:
            
            ┌─────────────────────────┬──────────────┬──────────────┬─────────────┐
            │ العنصر                  │ قبل التحسين   │ بعد التحسين   │ التحسن      │
            ├─────────────────────────┼──────────────┼──────────────┼─────────────┤
            │ حجم خط اسم البرنامج     │ 24px         │ 28px         │ +17%       │
            │ عرض بطاقة المستخدم      │ 200px        │ 280px        │ +40%       │
            │ ارتفاع بطاقة المستخدم    │ 80px         │ 90px         │ +13%       │
            │ حجم أيقونة المستخدم     │ 40×40px      │ 50×50px      │ +25%       │
            │ حجم خط اسم المستخدم     │ 16px         │ 18px         │ +13%       │
            │ حجم خط رسالة الترحيب    │ 12px         │ 14px         │ +17%       │
            │ عدد النصوص في البطاقة   │ 2            │ 3            │ +50%       │
            │ عدد الأيقونات           │ 1            │ 5            │ +400%      │
            └─────────────────────────┴──────────────┴──────────────┴─────────────┘
            
            🎨 التحسينات البصرية:
            ❌ قبل: تصميم بسيط بلون أزرق واحد
            ✅ بعد: تدرجات ملونة مع أخضر مميز وتأثيرات بصرية
            
            📝 التحسينات النصية:
            ❌ قبل: نصوص بسيطة بدون أيقونات
            ✅ بعد: نصوص مع أيقونات تعبيرية ومعلومات إضافية
            
            🎯 النتيجة: تحسن كبير في المظهر والوضوح والاحترافية!
            """)
            comparison_text.setStyleSheet("""
                QLabel {
                    font-size: 13px;
                    color: #34495e;
                    font-family: 'Courier New', monospace;
                    line-height: 1.5;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    border: 1px solid #dee2e6;
                }
            """)
            comparison_layout.addWidget(comparison_text)
            
            content_layout.addWidget(comparison_frame)
            content_layout.addStretch()
            
            main_layout.addWidget(content_widget)
            
        def create_enhanced_header(self):
            """إنشاء الشريط العلوي المحسن"""
            self.header = QFrame()
            self.header.setObjectName("main_header_frame")
            self.header.setFixedHeight(100)

            header_layout = QHBoxLayout(self.header)
            header_layout.setContentsMargins(30, 20, 30, 20)
            header_layout.setSpacing(30)

            # قسم العنوان المحسن
            title_section = QVBoxLayout()
            title_section.setSpacing(8)

            # العنوان الرئيسي المحسن
            main_title = QLabel("🏢 نظام إدارة الموارد البشرية")
            main_title.setAlignment(Qt.AlignLeft)
            main_title.setStyleSheet("""
                QLabel {
                    font-size: 28px;
                    font-weight: bold;
                    color: #2c3e50;
                    letter-spacing: 2px;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                              stop: 0 rgba(52, 152, 219, 0.1),
                                              stop: 1 rgba(155, 89, 182, 0.1));
                    padding: 8px 15px;
                    border-radius: 8px;
                    border-left: 4px solid #3498db;
                }
            """)

            # العنوان الفرعي المحسن
            subtitle = QLabel("💼 نظام شامل ومتطور لإدارة الموظفين والمعاملات المالية")
            subtitle.setAlignment(Qt.AlignLeft)
            subtitle.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    font-weight: 500;
                    color: #7f8c8d;
                    letter-spacing: 1px;
                    padding: 5px 15px;
                    font-style: italic;
                }
            """)

            title_section.addWidget(main_title)
            title_section.addWidget(subtitle)

            # قسم معلومات المستخدم المحسن
            user_section = QHBoxLayout()
            user_section.setSpacing(20)

            # بطاقة المستخدم المحسنة
            user_card = QFrame()
            user_card.setFixedSize(280, 90)
            user_card.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #27ae60, stop: 1 #229954);
                    border-radius: 15px;
                    border: 3px solid #1e8449;
                    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
                }
            """)
            
            user_card_layout = QHBoxLayout(user_card)
            user_card_layout.setContentsMargins(20, 12, 20, 12)
            user_card_layout.setSpacing(15)

            # أيقونة المستخدم المحسنة
            user_icon = QLabel("👨‍💼")
            user_icon.setStyleSheet("""
                QLabel {
                    font-size: 32px;
                    color: white;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 rgba(255, 255, 255, 0.3),
                                              stop: 1 rgba(255, 255, 255, 0.1));
                    border-radius: 25px;
                    padding: 10px;
                    min-width: 50px;
                    max-width: 50px;
                    min-height: 50px;
                    max-height: 50px;
                    border: 2px solid rgba(255, 255, 255, 0.4);
                }
            """)
            user_icon.setAlignment(Qt.AlignCenter)
            user_card_layout.addWidget(user_icon)
            
            # معلومات المستخدم المحسنة
            user_info_layout = QVBoxLayout()
            user_info_layout.setSpacing(4)
            
            welcome_label = QLabel("🌟 مرحباً بك في النظام")
            welcome_label.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.95);
                    font-size: 14px;
                    font-weight: 600;
                    letter-spacing: 0.5px;
                }
            """)
            user_info_layout.addWidget(welcome_label)
            
            user_name = QLabel("👑 المدير العام")
            user_name.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 18px;
                    font-weight: bold;
                    letter-spacing: 1px;
                    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
                }
            """)
            user_info_layout.addWidget(user_name)
            
            status_label = QLabel("🟢 متصل الآن")
            status_label.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 12px;
                    font-weight: 500;
                    letter-spacing: 0.3px;
                }
            """)
            user_info_layout.addWidget(status_label)
            
            user_card_layout.addLayout(user_info_layout)
            user_card_layout.addStretch()

            # زر الإعدادات (نفس الشكل السابق)
            settings_btn = QPushButton("⚙️ الإعدادات")
            settings_btn.setFixedSize(140, 50)
            settings_btn.setToolTip("إعدادات النظام")
            settings_btn.clicked.connect(lambda: print("🔄 تم النقر على زر الإعدادات"))
            settings_btn.setStyleSheet("""
                QPushButton {
                    font-size: 14px;
                    font-weight: bold;
                    color: white;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #95a5a6, stop: 1 #7f8c8d);
                    border: none;
                    border-radius: 8px;
                    padding: 12px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #bdc3c7, stop: 1 #95a5a6);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #7f8c8d, stop: 1 #6c7b7d);
                }
            """)

            user_section.addWidget(user_card)
            user_section.addWidget(settings_btn)

            # تجميع الأقسام
            header_layout.addLayout(title_section)
            header_layout.addStretch()
            header_layout.addLayout(user_section)
    
    return EnhancedUserCardTest()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار بطاقة المستخدم المحسنة واسم البرنامج")
    print("=" * 70)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            enhanced_style_file = styles_dir / "enhanced_dashboard.qss"
            
            combined_styles = ""
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read() + "\n"
                    
            if enhanced_style_file.exists():
                with open(enhanced_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read()
                    
            app.setStyleSheet(combined_styles)
            print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_enhanced_user_card_test()
        window.show()
        
        print("✅ تم فتح نافذة اختبار التحسينات!")
        print("\n🏢 تحسينات اسم البرنامج:")
        print("• حجم خط أكبر: 28px (+17%)")
        print("• لون مميز مع خلفية متدرجة")
        print("• ظلال نصية وحدود ملونة")
        print("• أيقونة مبنى مميزة")
        
        print("\n👨‍💼 تحسينات بطاقة المستخدم:")
        print("• حجم أكبر: 280×90px (+40% عرض)")
        print("• لون أخضر مميز مع تدرجات")
        print("• أيقونة مدير محسنة (32px)")
        print("• 3 نصوص مع أيقونات تعبيرية")
        print("• ظلال وتأثيرات بصرية")
        
        print("\n⚙️ زر الإعدادات:")
        print("• نفس الشكل السابق (بدون تغيير)")
        print("• 140×50px مع تدرج رمادي")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
