#!/usr/bin/env python3
"""
اختبار نظام الديون الجديد
Test New Debt System
"""

import sys
from pathlib import Path
from datetime import date

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.database import db_manager, get_db_session_context
from src.models import Employee, SalaryRecord, FinancialTransaction, TransactionType


def test_debt_system():
    """اختبار نظام الديون الجديد"""
    print("🧪 اختبار نظام الديون الجديد...")
    print("=" * 60)
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        with get_db_session_context() as session:
            # البحث عن الموظف
            employee = session.query(Employee).filter_by(is_active=True).first()
            salary_record = session.query(SalaryRecord).first()
            
            if not employee or not salary_record:
                print("❌ لا يوجد موظف أو سجل راتب")
                return False
            
            print(f"👤 الموظف: {employee.full_name}")
            print(f"📅 الفترة: {salary_record.month}/{salary_record.year}")
            
            # عرض الوضع الحالي
            current_month = salary_record.month
            current_year = salary_record.year
            
            original_advances = employee.get_total_advances(current_month, current_year)
            original_debts = employee.get_total_debts(current_month, current_year)
            
            print(f"\n📊 الوضع الحالي:")
            print(f"   إجمالي السلف: {original_advances:,.0f} د.ع")
            print(f"   إجمالي الديون: {original_debts:,.0f} د.ع")
            print(f"   استقطاع السلف في الراتب: {salary_record.total_advances:,.0f} د.ع")
            print(f"   استقطاع الديون في الراتب: {salary_record.total_market_debts:,.0f} د.ع")
            print(f"   صافي الراتب: {salary_record.net_salary:,.0f} د.ع")
            
            # محاكاة التعديل
            print(f"\n🔄 محاكاة التعديل:")
            print(f"   السلف الأصلية: {original_advances:,.0f} د.ع")
            print(f"   الديون الأصلية: {original_debts:,.0f} د.ع")
            
            # تعديل الاستقطاع (كما في المثال)
            new_advance_deduction = 25000  # بدلاً من 50000
            new_debt_deduction = 10000     # بدلاً من 20000
            
            print(f"   استقطاع السلف الجديد: {new_advance_deduction:,.0f} د.ع")
            print(f"   استقطاع الديون الجديد: {new_debt_deduction:,.0f} د.ع")
            
            # حساب الباقي
            remaining_advances = max(0, original_advances - new_advance_deduction)
            remaining_debts = max(0, original_debts - new_debt_deduction)
            
            print(f"\n💰 النتائج:")
            print(f"   باقي السلف: {remaining_advances:,.0f} د.ع")
            print(f"   باقي الديون: {remaining_debts:,.0f} د.ع")
            print(f"   إجمالي الباقي: {remaining_advances + remaining_debts:,.0f} د.ع")
            
            # تطبيق التعديل
            print(f"\n🔧 تطبيق التعديل...")
            
            # تحديث سجل الراتب
            salary_record.total_advances = new_advance_deduction
            salary_record.total_market_debts = new_debt_deduction
            
            # إعادة حساب الراتب
            old_net = salary_record.net_salary
            salary_record.calculate_salary()
            new_net = salary_record.net_salary
            
            print(f"   صافي الراتب: {old_net:,.0f} -> {new_net:,.0f} د.ع")
            print(f"   الفرق: {new_net - old_net:,.0f} د.ع")
            
            # إنشاء ديون للباقي
            if remaining_advances > 0 or remaining_debts > 0:
                print(f"\n📝 إنشاء ديون للباقي...")
                
                # تاريخ الشهر القادم
                next_month_date = date.today().replace(day=1)
                if next_month_date.month == 12:
                    next_month_date = next_month_date.replace(year=next_month_date.year + 1, month=1)
                else:
                    next_month_date = next_month_date.replace(month=next_month_date.month + 1)
                
                # إنشاء دين للسلف الباقية
                if remaining_advances > 0:
                    advance_debt = FinancialTransaction(
                        employee_id=employee.id,
                        transaction_type=TransactionType.ADVANCE,
                        amount=remaining_advances,
                        description=f"باقي سلف من {current_month}/{current_year}",
                        transaction_date=next_month_date
                    )
                    session.add(advance_debt)
                    print(f"   ✅ دين سلف: {remaining_advances:,.0f} د.ع")
                
                # إنشاء دين للديون الباقية
                if remaining_debts > 0:
                    market_debt = FinancialTransaction(
                        employee_id=employee.id,
                        transaction_type=TransactionType.MARKET_DEBT,
                        amount=remaining_debts,
                        description=f"باقي ديون من {current_month}/{current_year}",
                        transaction_date=next_month_date
                    )
                    session.add(market_debt)
                    print(f"   ✅ دين ماركت: {remaining_debts:,.0f} د.ع")
            
            # حفظ التغييرات
            session.commit()
            print(f"\n💾 تم حفظ جميع التغييرات")
            
            # التحقق من النتائج
            print(f"\n🔍 التحقق من النتائج:")
            
            # التحقق من الراتب المحدث
            session.refresh(salary_record)
            print(f"   صافي الراتب المحفوظ: {salary_record.net_salary:,.0f} د.ع")
            
            # التحقق من الديون الجديدة للشهر القادم
            next_month = next_month_date.month
            next_year = next_month_date.year
            
            future_advances = employee.get_total_advances(next_month, next_year)
            future_debts = employee.get_total_debts(next_month, next_year)
            
            print(f"   ديون الشهر القادم ({next_month}/{next_year}):")
            print(f"     السلف: {future_advances:,.0f} د.ع")
            print(f"     الديون: {future_debts:,.0f} د.ع")
            print(f"     الإجمالي: {future_advances + future_debts:,.0f} د.ع")
            
            return True
            
    except Exception as e:
        print(f"\n❌ خطأ في اختبار نظام الديون: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """الدالة الرئيسية"""
    success = test_debt_system()
    
    if success:
        print("\n🎉 اختبار نظام الديون نجح!")
        print("\n✅ النتائج:")
        print("• تم تعديل استقطاع الراتب الشهري")
        print("• تم إنشاء ديون للمبالغ الباقية")
        print("• الديون ستظهر في الشهر القادم")
        print("• يمكن استقطاعها أو تسديدها لاحقاً")
        
        print("\n🚀 يمكنك الآن:")
        print("1. تشغيل النظام: python run_hr_system.py")
        print("2. الذهاب إلى قسم الرواتب")
        print("3. تعديل تفاصيل الراتب")
        print("4. مشاهدة النظام الجديد يعمل")
        return 0
    else:
        print("💥 فشل اختبار نظام الديون!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
