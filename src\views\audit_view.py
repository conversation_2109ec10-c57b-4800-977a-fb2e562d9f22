"""
واجهة تدقيق الأنشطة
Audit Log View
"""

from datetime import date, datetime
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QComboBox, QLabel, QFrame, QHeaderView,
    QAbstractItemView, QDateEdit, QTextEdit, QDialog, QDialogButtonBox
)
from PySide6.QtCore import Qt, Signal, QDate, QTimer
from PySide6.QtGui import QFont, QColor

from ..utils import (
    apply_rtl_layout, setup_table_widget, populate_table, show_message,
    format_date, format_datetime, setup_combobox, get_combobox_value
)
from ..database import get_db_session_context
from ..models import AuditLog, ActionType



class AuditView(QWidget):
    """واجهة تدقيق الأنشطة"""
    
    def __init__(self):
        super().__init__()
        self.audit_logs_data = []
        self.filtered_data = []
        self.auto_refresh_timer = QTimer()
        
        self.setup_ui()
        self.setup_connections()
        self.load_audit_logs()
        self.start_auto_refresh()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setObjectName("audit_view")
        apply_rtl_layout(self)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # إنشاء رأس الصفحة
        self.create_header(main_layout)
        
        # إنشاء شريط البحث والتصفية
        self.create_search_bar(main_layout)
        
        # إنشاء جدول سجلات التدقيق
        self.create_audit_table(main_layout)
        
        # إنشاء شريط الأزرار
        self.create_action_buttons(main_layout)
        
    def create_header(self, layout: QVBoxLayout):
        """إنشاء رأس الصفحة المتناسق مع النظام"""
        # إنشاء إطار العنوان الرئيسي المدمج
        header_frame = QFrame()
        header_frame.setObjectName("compact_header_frame")
        header_frame.setFixedHeight(120)
        header_frame.setStyleSheet("""
            QFrame#compact_header_frame {
                background-color: #fff3e0;
                border: 2px solid #ff9800;
                border-radius: 10px;
                margin: 5px;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 8, 15, 8)
        header_layout.setSpacing(5)

        # إضافة مساحة مرنة في الأعلى لتوسيط المحتوى
        header_layout.addStretch()

        # إنشاء حاوي للتوسيط الأفقي للعنوان الرئيسي
        main_title_container = QHBoxLayout()
        main_title_container.addStretch()

        main_title = QLabel("🔍 إدارة تدقيق الأنشطة")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 22px;
                font-weight: bold;
                color: #2c3e50;
                background-color: transparent;
                padding: 5px;
                margin: 0px;
            }
        """)

        main_title_container.addWidget(main_title)
        main_title_container.addStretch()

        # إنشاء حاوي للتوسيط الأفقي للعنوان الفرعي
        subtitle_container = QHBoxLayout()
        subtitle_container.addStretch()

        subtitle = QLabel("📊 مراقبة وتتبع جميع العمليات والأنشطة في النظام")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #34495e;
                background-color: #fff8f0;
                padding: 6px 15px;
                border-radius: 6px;
                border-left: 3px solid #ff9800;
                margin: 2px;
            }
        """)

        subtitle_container.addWidget(subtitle)
        subtitle_container.addStretch()

        # تجميع العناصر
        header_layout.addLayout(main_title_container)
        header_layout.addLayout(subtitle_container)

        # إضافة مساحة مرنة في الأسفل لتوسيط المحتوى
        header_layout.addStretch()

        layout.addWidget(header_frame)
        
    def create_search_bar(self, layout: QVBoxLayout):
        """إنشاء شريط البحث والتصفية المحسن"""
        # إطار الفلاتر المتناسق
        filters_frame = QFrame()
        filters_frame.setObjectName("search_frame")
        filters_frame.setStyleSheet("""
            QFrame#search_frame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)

        filters_layout = QVBoxLayout(filters_frame)
        filters_layout.setSpacing(8)

        # الصف الأول: عنوان البحث مع الإحصائيات
        first_row = QHBoxLayout()

        # عنوان البحث
        search_title = QLabel("🔍 فلاتر تدقيق الأنشطة")
        search_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 13px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)

        # معلومات النتائج
        self.logs_count_label = QLabel("📋 إجمالي النتائج: 0")
        self.logs_count_label.setFixedHeight(25)
        self.logs_count_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 11px;
                font-weight: bold;
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 4px 8px;
                border-radius: 4px;
                border: 1px solid #ff9800;
                text-align: center;
            }
        """)

        first_row.addWidget(search_title)
        first_row.addStretch()
        first_row.addWidget(self.logs_count_label)

        # الصف الثاني: الفلاتر المدمجة
        second_row = QHBoxLayout()
        second_row.setSpacing(10)

        # حقل البحث
        search_label = QLabel("🔍 البحث:")
        search_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                font-weight: 600;
                color: #495057;
                min-width: 50px;
            }
        """)
        second_row.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث في الوصف...")
        self.search_input.setMinimumHeight(30)
        self.search_input.setStyleSheet("""
            QLineEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 11px;
                padding: 4px 8px;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                background-color: #ffffff;
                min-width: 150px;
            }
            QLineEdit:focus {
                border-color: #ff9800;
            }
        """)
        second_row.addWidget(self.search_input)

        # تصفية حسب نوع العملية
        action_label = QLabel("⚡ العملية:")
        action_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                font-weight: 600;
                color: #495057;
                min-width: 50px;
            }
        """)
        second_row.addWidget(action_label)

        self.action_type_filter = QComboBox()
        self.action_type_filter.setMinimumHeight(30)
        self.action_type_filter.setStyleSheet("""
            QComboBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 11px;
                padding: 4px 8px;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                background-color: #ffffff;
                min-width: 120px;
            }
            QComboBox:focus {
                border-color: #ff9800;
            }
        """)
        second_row.addWidget(self.action_type_filter)

        # تصفية حسب المستخدم
        user_label = QLabel("👤 المستخدم:")
        user_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                font-weight: 600;
                color: #495057;
                min-width: 60px;
            }
        """)
        second_row.addWidget(user_label)

        self.user_filter = QComboBox()
        self.user_filter.setMinimumHeight(30)
        self.user_filter.setStyleSheet("""
            QComboBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 11px;
                padding: 4px 8px;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                background-color: #ffffff;
                min-width: 100px;
            }
            QComboBox:focus {
                border-color: #ff9800;
            }
        """)
        second_row.addWidget(self.user_filter)

        # الصف الثالث: باقي الفلاتر والأزرار
        third_row = QHBoxLayout()
        third_row.setSpacing(10)

        # تصفية حسب الجدول
        table_label = QLabel("🗃️ الجدول:")
        table_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                font-weight: 600;
                color: #495057;
                min-width: 50px;
            }
        """)
        third_row.addWidget(table_label)

        self.table_filter = QComboBox()
        self.table_filter.setMinimumHeight(30)
        self.table_filter.setStyleSheet("""
            QComboBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 11px;
                padding: 4px 8px;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                background-color: #ffffff;
                min-width: 100px;
            }
            QComboBox:focus {
                border-color: #ff9800;
            }
        """)
        third_row.addWidget(self.table_filter)

        # تصفية حسب التاريخ
        date_label = QLabel("📅 من:")
        date_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                font-weight: 600;
                color: #495057;
                min-width: 30px;
            }
        """)
        third_row.addWidget(date_label)

        self.date_from_filter = QDateEdit()
        self.date_from_filter.setDate(QDate.currentDate().addDays(-7))
        self.date_from_filter.setCalendarPopup(True)
        self.date_from_filter.setMinimumHeight(30)
        self.date_from_filter.setStyleSheet("""
            QDateEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 11px;
                padding: 4px 8px;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                background-color: #ffffff;
                min-width: 100px;
            }
            QDateEdit:focus {
                border-color: #ff9800;
            }
        """)
        third_row.addWidget(self.date_from_filter)

        to_date_label = QLabel("إلى:")
        to_date_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                font-weight: 600;
                color: #495057;
                min-width: 30px;
            }
        """)
        third_row.addWidget(to_date_label)

        self.date_to_filter = QDateEdit()
        self.date_to_filter.setDate(QDate.currentDate())
        self.date_to_filter.setCalendarPopup(True)
        self.date_to_filter.setMinimumHeight(30)
        self.date_to_filter.setStyleSheet("""
            QDateEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 11px;
                padding: 4px 8px;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                background-color: #ffffff;
                min-width: 100px;
            }
            QDateEdit:focus {
                border-color: #ff9800;
            }
        """)
        third_row.addWidget(self.date_to_filter)

        # زر مسح التصفية
        self.clear_filter_btn = QPushButton("🗑️ مسح")
        self.clear_filter_btn.setMinimumHeight(30)
        self.clear_filter_btn.setStyleSheet("""
            QPushButton {
                font-size: 11px;
                font-weight: bold;
                color: white;
                background-color: #6c757d;
                border: none;
                border-radius: 5px;
                padding: 6px 12px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        third_row.addWidget(self.clear_filter_btn)

        # إضافة مساحة مرنة
        third_row.addStretch()

        filters_layout.addLayout(first_row)
        filters_layout.addLayout(second_row)
        filters_layout.addLayout(third_row)

        layout.addWidget(filters_frame)

        # تحميل بيانات التصفية
        self.load_filter_data()
        
    def create_audit_table(self, layout: QVBoxLayout):
        """إنشاء جدول سجلات التدقيق المحسن"""
        # إطار الجدول المتناسق
        table_frame = QFrame()
        table_frame.setObjectName("table_frame")
        table_frame.setStyleSheet("""
            QFrame#table_frame {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)

        table_layout = QVBoxLayout(table_frame)
        table_layout.setSpacing(10)

        # شريط عنوان الجدول
        table_header = QHBoxLayout()

        table_title = QLabel("📋 سجلات تدقيق الأنشطة")
        table_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)

        table_header.addWidget(table_title)
        table_header.addStretch()

        table_layout.addLayout(table_header)

        # الجدول المحسن
        self.audit_table = QTableWidget()
        self.audit_table.setAlternatingRowColors(True)
        self.audit_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.audit_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.audit_table.setSortingEnabled(True)
        self.audit_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                gridline-color: #e9ecef;
                selection-background-color: #fff3e0;
                selection-color: #2c3e50;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
                text-align: center;
                color: #2c3e50;
            }
            QTableWidget::item:selected {
                background-color: #fff3e0;
                color: #2c3e50;
                font-weight: bold;
            }
            QTableWidget::item:hover {
                background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #ff9800;
                color: white;
                font-weight: bold;
                font-size: 12px;
                padding: 10px;
                border: none;
                border-right: 1px solid #f57c00;
                text-align: center;
            }
            QHeaderView::section:hover {
                background-color: #f57c00;
            }
        """)

        # إعداد الجدول
        headers = [
            "⏰ الوقت", "⚡ نوع العملية", "👤 المستخدم", "🗃️ الجدول",
            "🔢 معرف السجل", "📝 الوصف", "🌐 عنوان IP"
        ]

        self.audit_table.setColumnCount(len(headers))
        self.audit_table.setHorizontalHeaderLabels(headers)

        # تحديد عرض الأعمدة
        column_widths = [160, 140, 120, 110, 120, 350, 130]
        for i, width in enumerate(column_widths):
            self.audit_table.setColumnWidth(i, width)

        # إعدادات إضافية للجدول
        self.audit_table.horizontalHeader().setStretchLastSection(False)
        self.audit_table.verticalHeader().setVisible(False)
        self.audit_table.setAlternatingRowColors(True)

        table_layout.addWidget(self.audit_table)
        layout.addWidget(table_frame)
        
    def create_action_buttons(self, layout: QVBoxLayout):
        """إنشاء شريط الأزرار المحسن"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setObjectName("buttons_frame")
        buttons_frame.setStyleSheet("""
            QFrame#buttons_frame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                margin: 5px 0px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        # أزرار الإجراءات الرئيسية
        self.view_details_btn = QPushButton("🔍 عرض التفاصيل")
        self.view_details_btn.setMinimumHeight(35)
        self.view_details_btn.setEnabled(False)
        self.view_details_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #2196F3;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 120px;
            }
            QPushButton:hover:enabled {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #bdbdbd;
                color: #757575;
            }
        """)

        self.export_btn = QPushButton("📤 تصدير Excel")
        self.export_btn.setMinimumHeight(35)
        self.export_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #4CAF50;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        self.clear_old_logs_btn = QPushButton("🗑️ مسح السجلات القديمة")
        self.clear_old_logs_btn.setMinimumHeight(35)
        self.clear_old_logs_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #f44336;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)

        # أزرار التحكم
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setMinimumHeight(35)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #ff9800;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
        """)

        # زر الإحصائيات المتقدمة
        self.analytics_btn = QPushButton("📊 إحصائيات متقدمة")
        self.analytics_btn.setMinimumHeight(35)
        self.analytics_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #9c27b0;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 130px;
            }
            QPushButton:hover {
                background-color: #7b1fa2;
            }
        """)

        # خانة التحديث التلقائي
        self.auto_refresh_checkbox = QPushButton("⚡ التحديث التلقائي")
        self.auto_refresh_checkbox.setCheckable(True)
        self.auto_refresh_checkbox.setChecked(True)
        self.auto_refresh_checkbox.setMinimumHeight(35)
        self.auto_refresh_checkbox.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #9c27b0;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #7b1fa2;
            }
            QPushButton:checked {
                background-color: #4caf50;
            }
            QPushButton:checked:hover {
                background-color: #45a049;
            }
        """)

        # ترتيب الأزرار
        buttons_layout.addWidget(self.view_details_btn)
        buttons_layout.addWidget(self.export_btn)
        buttons_layout.addWidget(self.clear_old_logs_btn)
        buttons_layout.addWidget(self.analytics_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.auto_refresh_checkbox)
        buttons_layout.addWidget(self.refresh_btn)

        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        # ربط إشارات البحث والتصفية
        self.search_input.textChanged.connect(self.filter_audit_logs)
        self.action_type_filter.currentTextChanged.connect(self.filter_audit_logs)
        self.user_filter.currentTextChanged.connect(self.filter_audit_logs)
        self.table_filter.currentTextChanged.connect(self.filter_audit_logs)
        self.date_from_filter.dateChanged.connect(self.filter_audit_logs)
        self.date_to_filter.dateChanged.connect(self.filter_audit_logs)
        self.clear_filter_btn.clicked.connect(self.clear_filters)
        
        # ربط إشارات الجدول
        self.audit_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.audit_table.itemDoubleClicked.connect(self.on_item_double_clicked)
        
        # ربط إشارات الأزرار
        self.view_details_btn.clicked.connect(self.view_details)
        self.export_btn.clicked.connect(self.export_logs)
        self.clear_old_logs_btn.clicked.connect(self.clear_old_logs)
        self.analytics_btn.clicked.connect(self.show_analytics)
        self.refresh_btn.clicked.connect(self.load_audit_logs)
        self.auto_refresh_checkbox.toggled.connect(self.toggle_auto_refresh)
        
        # ربط مؤقت التحديث التلقائي
        self.auto_refresh_timer.timeout.connect(self.load_audit_logs)
        
    def load_filter_data(self):
        """تحميل بيانات التصفية"""
        try:
            with get_db_session_context() as session:
                # تحميل أنواع العمليات
                action_items = [("جميع العمليات", "")]
                for action_type in ActionType:
                    action_items.append((action_type.value, action_type.name))
                setup_combobox(self.action_type_filter, action_items)
                
                # تحميل المستخدمين
                users = session.query(AuditLog.user_name).distinct().filter(
                    AuditLog.user_name.isnot(None)
                ).all()
                user_items = [("جميع المستخدمين", "")] + [(user[0], user[0]) for user in users]
                setup_combobox(self.user_filter, user_items)
                
                # تحميل الجداول
                tables = session.query(AuditLog.table_name).distinct().filter(
                    AuditLog.table_name.isnot(None)
                ).all()
                table_items = [("جميع الجداول", "")] + [(table[0], table[0]) for table in tables]
                setup_combobox(self.table_filter, table_items)
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل بيانات التصفية: {e}", "error")
            
    def load_audit_logs(self):
        """تحميل سجلات التدقيق"""
        try:
            with get_db_session_context() as session:
                # الحصول على أحدث 1000 سجل
                audit_logs = AuditLog.get_recent_logs(session, limit=1000)
                
                self.audit_logs_data = []
                for log in audit_logs:
                    row_data = [
                        format_datetime(log.timestamp),
                        log.action_type.value,
                        log.user_name or "",
                        log.table_name or "",
                        log.record_id or "",
                        log.description[:100] + "..." if len(log.description) > 100 else log.description,
                        log.ip_address or ""
                    ]
                    # إضافة ID للاستخدام الداخلي
                    row_data.append(log.id)
                    self.audit_logs_data.append(row_data)
                
                # تطبيق التصفية
                self.filter_audit_logs()
                
                # تحديث عداد السجلات
                self.logs_count_label.setText(f"📋 إجمالي النتائج: {len(self.audit_logs_data)}")
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل سجلات التدقيق: {e}", "error")
            
    def filter_audit_logs(self):
        """تصفية سجلات التدقيق"""
        search_text = self.search_input.text().lower()
        action_type = get_combobox_value(self.action_type_filter)
        user_name = get_combobox_value(self.user_filter)
        table_name = get_combobox_value(self.table_filter)
        date_from = self.date_from_filter.date().toPython()
        date_to = self.date_to_filter.date().toPython()
        
        self.filtered_data = []
        
        for row in self.audit_logs_data:
            # فلترة النص
            if search_text and search_text not in row[5].lower():
                continue
            
            # فلترة نوع العملية
            if action_type and action_type != "" and action_type not in row[1]:
                continue
            
            # فلترة المستخدم
            if user_name and user_name != "" and user_name != row[2]:
                continue
            
            # فلترة الجدول
            if table_name and table_name != "" and table_name != row[3]:
                continue
            
            # فلترة التاريخ
            try:
                log_date = datetime.fromisoformat(row[0].replace("Z", "+00:00")).date()
                if log_date < date_from or log_date > date_to:
                    continue
            except:
                pass
            
            self.filtered_data.append(row)
        
        # تحديث الجدول
        self.update_table()
        
    def update_table(self):
        """تحديث الجدول المحسن"""
        # إزالة العمود الأخير (ID) من العرض
        display_data = [row[:-1] for row in self.filtered_data]

        # تحديث الجدول
        self.audit_table.setRowCount(len(display_data))

        for row_idx, row_data in enumerate(display_data):
            for col_idx, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                item.setTextAlignment(Qt.AlignCenter)

                # تلوين الصفوف حسب نوع العملية
                if col_idx == 1:  # عمود نوع العملية
                    if "إنشاء" in str(cell_data):
                        item.setBackground(QColor("#e8f5e8"))
                    elif "تعديل" in str(cell_data):
                        item.setBackground(QColor("#fff3cd"))
                    elif "حذف" in str(cell_data):
                        item.setBackground(QColor("#f8d7da"))
                    elif "تسجيل دخول" in str(cell_data):
                        item.setBackground(QColor("#d1ecf1"))
                    elif "تسجيل خروج" in str(cell_data):
                        item.setBackground(QColor("#f0f0f0"))

                # جعل الخلايا غير قابلة للتعديل
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                self.audit_table.setItem(row_idx, col_idx, item)

        # تحديث عداد النتائج المفلترة
        self.logs_count_label.setText(f"📋 إجمالي النتائج: {len(self.filtered_data)}")
        
    def clear_filters(self):
        """مسح جميع التصفيات"""
        self.search_input.clear()
        self.action_type_filter.setCurrentIndex(0)
        self.user_filter.setCurrentIndex(0)
        self.table_filter.setCurrentIndex(0)
        self.date_from_filter.setDate(QDate.currentDate().addDays(-7))
        self.date_to_filter.setDate(QDate.currentDate())
        
    def on_selection_changed(self):
        """معالج تغيير التحديد"""
        has_selection = len(self.audit_table.selectedItems()) > 0
        self.view_details_btn.setEnabled(has_selection)
        
    def on_item_double_clicked(self, item):
        """معالج النقر المزدوج"""
        self.view_details()
        
    def view_details(self):
        """عرض تفاصيل سجل التدقيق المحسنة"""
        row = self.audit_table.currentRow()
        if 0 <= row < len(self.filtered_data):
            log_id = self.filtered_data[row][-1]

            try:
                with get_db_session_context() as session:
                    audit_log = session.query(AuditLog).filter_by(id=log_id).first()
                    if audit_log:
                        from .audit_detail_dialog import AuditDetailDialog
                        dialog = AuditDetailDialog(audit_log, self)
                        dialog.exec()
                    else:
                        show_message(self, "خطأ", "سجل التدقيق غير موجود", "error")

            except Exception as e:
                show_message(self, "خطأ", f"فشل في عرض تفاصيل سجل التدقيق: {e}", "error")
                
    def export_logs(self):
        """تصدير سجلات التدقيق إلى Excel"""
        try:
            from datetime import datetime
            import os

            if not self.filtered_data:
                show_message(self, "تنبيه", "لا توجد بيانات للتصدير", "warning")
                return

            # إنشاء مجلد التصدير إذا لم يكن موجوداً
            export_dir = "exports"
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)

            # اسم الملف مع الوقت الحالي
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{export_dir}/audit_logs_{timestamp}.csv"

            # كتابة البيانات إلى ملف CSV
            with open(filename, 'w', encoding='utf-8-sig') as f:
                # كتابة العناوين
                headers = ["الوقت", "نوع العملية", "المستخدم", "الجدول", "معرف السجل", "الوصف", "عنوان IP"]
                f.write(','.join(f'"{header}"' for header in headers) + '\n')

                # كتابة البيانات
                for row in self.filtered_data:
                    # إزالة العمود الأخير (ID) من التصدير
                    row_data = row[:-1]
                    f.write(','.join(f'"{str(cell).replace(chr(34), chr(34)+chr(34))}"' for cell in row_data) + '\n')

            show_message(
                self,
                "نجح",
                f"تم تصدير {len(self.filtered_data)} سجل بنجاح إلى:\n{filename}",
                "information"
            )

        except Exception as e:
            show_message(self, "خطأ", f"فشل في تصدير سجلات التدقيق: {e}", "error")
        
    def clear_old_logs(self):
        """مسح السجلات القديمة"""
        from ..utils import show_confirmation
        
        if show_confirmation(
            self,
            "تأكيد مسح السجلات القديمة",
            "هل أنت متأكد من مسح السجلات الأقدم من 30 يوماً؟\n\nهذا الإجراء لا يمكن التراجع عنه."
        ):
            try:
                with get_db_session_context() as session:
                    # حذف السجلات الأقدم من 30 يوماً
                    cutoff_date = datetime.now().replace(day=1)  # بداية الشهر الحالي
                    deleted_count = session.query(AuditLog).filter(
                        AuditLog.timestamp < cutoff_date
                    ).delete()
                    
                    session.commit()
                    
                    show_message(self, "نجح", f"تم مسح {deleted_count} سجل قديم", "information")
                    self.load_audit_logs()
                    
            except Exception as e:
                show_message(self, "خطأ", f"فشل في مسح السجلات القديمة: {e}", "error")
                
    def start_auto_refresh(self):
        """بدء التحديث التلقائي"""
        self.auto_refresh_timer.start(30000)  # كل 30 ثانية
        
    def stop_auto_refresh(self):
        """إيقاف التحديث التلقائي"""
        self.auto_refresh_timer.stop()
        
    def toggle_auto_refresh(self, enabled: bool):
        """تبديل التحديث التلقائي"""
        if enabled:
            self.start_auto_refresh()
        else:
            self.stop_auto_refresh()

    def show_analytics(self):
        """عرض الإحصائيات المتقدمة"""
        try:
            from .audit_analytics_view import AuditAnalyticsView
            from PySide6.QtWidgets import QDialog, QVBoxLayout

            # إنشاء نافذة حوار للإحصائيات
            dialog = QDialog(self)
            dialog.setWindowTitle("إحصائيات تدقيق الأنشطة المتقدمة")
            dialog.setModal(True)
            dialog.resize(1200, 800)

            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(0, 0, 0, 0)

            # إضافة واجهة الإحصائيات
            analytics_view = AuditAnalyticsView()
            layout.addWidget(analytics_view)

            dialog.exec()

        except Exception as e:
            show_message(self, "خطأ", f"فشل في عرض الإحصائيات المتقدمة: {e}", "error")
