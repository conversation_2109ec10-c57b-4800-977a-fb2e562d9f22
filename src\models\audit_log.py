"""
نموذج تدقيق الأنشطة
Audit Log model
"""

from datetime import datetime
from enum import Enum
from sqlalchemy import Column, String, Text, DateTime, Enum as SQLEnum, JSON, Float
from .base import BaseModel


class ActionType(Enum):
    """نوع العملية"""
    CREATE = "إنشاء"
    UPDATE = "تعديل"
    DELETE = "حذف"
    LOGIN = "تسجيل دخول"
    LOGOUT = "تسجيل خروج"
    SALARY_PAYMENT = "صرف راتب"
    FINANCIAL_TRANSACTION = "معاملة مالية"
    BACKUP = "نسخ احتياطي"
    RESTORE = "استعادة"
    REPORT_GENERATION = "توليد تقرير"
    EXPORT_DATA = "تصدير بيانات"
    IMPORT_DATA = "استيراد بيانات"
    SYSTEM_MAINTENANCE = "صيانة النظام"
    SECURITY_EVENT = "حدث أمني"
    DATA_VALIDATION = "التحقق من البيانات"
    CONFIGURATION_CHANGE = "تغيير إعدادات"
    USER_MANAGEMENT = "إدارة المستخدمين"
    PERMISSION_CHANGE = "تغيير صلاحيات"


class AuditLog(BaseModel):
    """نموذج تدقيق الأنشطة"""
    
    __tablename__ = "audit_logs"
    
    # معلومات العملية
    action_type = Column(
        SQLEnum(ActionType),
        nullable=False,
        comment="نوع العملية"
    )
    table_name = Column(String(100), nullable=True, comment="اسم الجدول المتأثر")
    record_id = Column(String(50), nullable=True, comment="معرف السجل المتأثر")
    
    # تفاصيل العملية
    description = Column(Text, nullable=False, comment="وصف العملية")
    old_values = Column(JSON, nullable=True, comment="القيم القديمة")
    new_values = Column(JSON, nullable=True, comment="القيم الجديدة")
    
    # معلومات المستخدم والنظام
    user_name = Column(String(100), nullable=True, comment="اسم المستخدم")
    ip_address = Column(String(45), nullable=True, comment="عنوان IP")
    user_agent = Column(Text, nullable=True, comment="معلومات المتصفح")
    session_id = Column(String(100), nullable=True, comment="معرف الجلسة")
    request_id = Column(String(100), nullable=True, comment="معرف الطلب")
    execution_time = Column(Float, nullable=True, comment="وقت التنفيذ بالثواني")
    error_message = Column(Text, nullable=True, comment="رسالة الخطأ إن وجدت")
    additional_data = Column(JSON, nullable=True, comment="بيانات إضافية")
    user_agent = Column(String(500), nullable=True, comment="معلومات المتصفح")
    
    # الوقت
    timestamp = Column(DateTime, nullable=False, default=datetime.utcnow, comment="وقت العملية")
    
    def __repr__(self) -> str:
        return f"<AuditLog(id={self.id}, action='{self.action_type.value}', table='{self.table_name}')>"
    
    def __str__(self) -> str:
        return f"{self.action_type.value} - {self.description} - {self.timestamp}"
    
    @classmethod
    def log_action(
        cls,
        session,
        action_type: ActionType,
        description: str,
        table_name: str = None,
        record_id: str = None,
        old_values: dict = None,
        new_values: dict = None,
        user_name: str = None,
        ip_address: str = None,
        user_agent: str = None,
        session_id: str = None,
        request_id: str = None,
        execution_time: float = None,
        error_message: str = None,
        additional_data: dict = None
    ) -> 'AuditLog':
        """تسجيل عملية في سجل التدقيق مع معلومات شاملة"""

        log_entry = cls(
            action_type=action_type,
            table_name=table_name,
            record_id=str(record_id) if record_id else None,
            description=description,
            old_values=old_values,
            new_values=new_values,
            user_name=user_name,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            request_id=request_id,
            execution_time=execution_time,
            error_message=error_message,
            additional_data=additional_data,
            timestamp=datetime.utcnow()
        )

        session.add(log_entry)
        session.commit()
        return log_entry
    
    @classmethod
    def get_logs_by_table(cls, session, table_name: str, limit: int = 100):
        """الحصول على سجلات تدقيق جدول معين"""
        return session.query(cls).filter(
            cls.table_name == table_name
        ).order_by(cls.timestamp.desc()).limit(limit).all()
    
    @classmethod
    def get_logs_by_user(cls, session, user_name: str, limit: int = 100):
        """الحصول على سجلات تدقيق مستخدم معين"""
        return session.query(cls).filter(
            cls.user_name == user_name
        ).order_by(cls.timestamp.desc()).limit(limit).all()
    
    @classmethod
    def get_logs_by_action(cls, session, action_type: ActionType, limit: int = 100):
        """الحصول على سجلات تدقيق نوع عملية معين"""
        return session.query(cls).filter(
            cls.action_type == action_type
        ).order_by(cls.timestamp.desc()).limit(limit).all()

    @classmethod
    def get_recent_logs(cls, session, limit: int = 100):
        """الحصول على أحدث سجلات التدقيق"""
        return session.query(cls).order_by(cls.timestamp.desc()).limit(limit).all()

    @classmethod
    def get_logs_by_date_range(cls, session, start_date, end_date, limit: int = 1000):
        """الحصول على سجلات التدقيق في نطاق تاريخ معين"""
        return session.query(cls).filter(
            cls.timestamp >= start_date,
            cls.timestamp <= end_date
        ).order_by(cls.timestamp.desc()).limit(limit).all()

    @classmethod
    def get_activity_summary(cls, session, days: int = 7):
        """الحصول على ملخص الأنشطة لعدد أيام معين"""
        from datetime import datetime, timedelta
        from sqlalchemy import func

        cutoff_date = datetime.utcnow() - timedelta(days=days)

        # إحصائيات حسب نوع العملية
        action_stats = session.query(
            cls.action_type,
            func.count(cls.id).label('count')
        ).filter(
            cls.timestamp >= cutoff_date
        ).group_by(cls.action_type).all()

        # إحصائيات حسب المستخدم
        user_stats = session.query(
            cls.user_name,
            func.count(cls.id).label('count')
        ).filter(
            cls.timestamp >= cutoff_date,
            cls.user_name.isnot(None)
        ).group_by(cls.user_name).order_by(func.count(cls.id).desc()).limit(10).all()

        # إحصائيات حسب الجدول
        table_stats = session.query(
            cls.table_name,
            func.count(cls.id).label('count')
        ).filter(
            cls.timestamp >= cutoff_date,
            cls.table_name.isnot(None)
        ).group_by(cls.table_name).order_by(func.count(cls.id).desc()).limit(10).all()

        return {
            'action_stats': [(action.value if hasattr(action, 'value') else str(action), count) for action, count in action_stats],
            'user_stats': user_stats,
            'table_stats': table_stats,
            'total_activities': sum(count for _, count in action_stats)
        }

    @classmethod
    def cleanup_old_logs(cls, session, days: int = 90):
        """تنظيف السجلات القديمة"""
        from datetime import datetime, timedelta

        cutoff_date = datetime.utcnow() - timedelta(days=days)
        deleted_count = session.query(cls).filter(
            cls.timestamp < cutoff_date
        ).delete()

        session.commit()
        return deleted_count
    

    
    @classmethod
    def search_logs(cls, session, search_term: str, limit: int = 100):
        """البحث في سجلات التدقيق"""
        return session.query(cls).filter(
            cls.description.contains(search_term)
        ).order_by(cls.timestamp.desc()).limit(limit).all()
