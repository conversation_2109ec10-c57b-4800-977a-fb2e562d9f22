#!/usr/bin/env python3
"""
اختبار تعديل عدد أيام الدوام
Test Working Days Modification
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.database import db_manager, get_db_session_context
from src.models import SalaryRecord


def test_working_days_modification():
    """اختبار تعديل عدد أيام الدوام"""
    print("🧪 اختبار تعديل عدد أيام الدوام...")
    print("=" * 50)
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        with get_db_session_context() as session:
            # البحث عن سجل راتب
            salary_record = session.query(SalaryRecord).first()
            
            if not salary_record:
                print("❌ لا يوجد سجلات رواتب في النظام")
                return False
            
            print(f"👤 الموظف: {salary_record.employee.full_name}")
            print(f"📅 الفترة: {salary_record.month}/{salary_record.year}")
            
            # عرض القيم الحالية
            print(f"\n📊 القيم الحالية:")
            print(f"   أيام الدوام: {salary_record.working_days}")
            print(f"   الراتب اليومي: {salary_record.daily_salary:,.0f} د.ع")
            print(f"   إجمالي الراتب: {salary_record.gross_salary:,.0f} د.ع")
            print(f"   صافي الراتب: {salary_record.net_salary:,.0f} د.ع")
            
            # تغيير عدد أيام الدوام
            new_working_days = 25  # تغيير من 30 إلى 25
            print(f"\n🔄 تغيير أيام الدوام من {salary_record.working_days} إلى {new_working_days}")
            
            salary_record.working_days = new_working_days
            
            # إعادة حساب الراتب
            print("🧮 إعادة حساب الراتب...")
            salary_record.calculate_salary()
            
            # عرض القيم الجديدة
            print(f"\n📊 القيم الجديدة:")
            print(f"   أيام الدوام: {salary_record.working_days}")
            print(f"   الراتب اليومي: {salary_record.daily_salary:,.0f} د.ع")
            print(f"   إجمالي الراتب: {salary_record.gross_salary:,.0f} د.ع")
            print(f"   صافي الراتب: {salary_record.net_salary:,.0f} د.ع")
            
            # حفظ التغييرات
            session.commit()
            print("\n💾 تم حفظ التغييرات في قاعدة البيانات")
            
            # التحقق من الحفظ
            session.refresh(salary_record)
            print(f"\n✅ التحقق من الحفظ:")
            print(f"   أيام الدوام المحفوظة: {salary_record.working_days}")
            print(f"   صافي الراتب المحفوظ: {salary_record.net_salary:,.0f} د.ع")
            
            # اختبار تغيير آخر
            print(f"\n🔄 اختبار آخر: تغيير أيام الدوام إلى 28")
            salary_record.working_days = 28
            salary_record.calculate_salary()
            
            print(f"   أيام الدوام: {salary_record.working_days}")
            print(f"   صافي الراتب: {salary_record.net_salary:,.0f} د.ع")
            
            session.commit()
            print("💾 تم حفظ التغيير الثاني")
            
            return True
            
    except Exception as e:
        print(f"\n❌ خطأ في اختبار تعديل أيام الدوام: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """الدالة الرئيسية"""
    success = test_working_days_modification()
    
    if success:
        print("\n🎉 اختبار تعديل أيام الدوام نجح!")
        print("\nالنتائج:")
        print("✅ تم تغيير عدد أيام الدوام بنجاح")
        print("✅ تم إعادة حساب الراتب تلقائياً")
        print("✅ تم حفظ التغييرات في قاعدة البيانات")
        print("\nيمكنك الآن تشغيل النظام ومشاهدة التغييرات:")
        print("python run_hr_system.py")
        return 0
    else:
        print("💥 فشل اختبار تعديل أيام الدوام!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
