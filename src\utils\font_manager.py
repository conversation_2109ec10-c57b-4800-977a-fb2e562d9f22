#!/usr/bin/env python3
"""
مدير الخطوط المحسن
Enhanced Font Manager
"""

from PySide6.QtWidgets import QApplication
from PySide6.QtGui import QFont
from PySide6.QtCore import QObject, Signal


class FontManager(QObject):
    """مدير الخطوط المحسن"""
    
    font_changed = Signal(int)  # إشارة تغيير حجم الخط
    
    def __init__(self):
        super().__init__()
        self.current_font_size = 12
        self.font_family = "Arial"
        
    def set_font_size(self, size: int):
        """تعيين حجم الخط"""
        if 8 <= size <= 72:  # حدود آمنة لحجم الخط - تم زيادة الحد الأقصى إلى 72
            self.current_font_size = size
            self.apply_font_to_application()
            self.font_changed.emit(size)
            
    def get_font_size(self) -> int:
        """الحصول على حجم الخط الحالي"""
        return self.current_font_size
        
    def apply_font_to_application(self):
        """تطبيق الخط على التطبيق بالكامل"""
        app = QApplication.instance()
        if not app:
            return
            
        # إنشاء الخط الجديد
        font = QFont(self.font_family, self.current_font_size)
        font.setStyleHint(QFont.SansSerif)
        
        # تطبيق الخط على التطبيق
        app.setFont(font)
        
        # تطبيق الخط على جميع العناصر الموجودة
        self.apply_font_to_all_widgets(font)
        
        # تطبيق أنماط CSS محدثة
        self.apply_font_styles()
        
    def apply_font_to_all_widgets(self, font: QFont):
        """تطبيق الخط على جميع العناصر"""
        app = QApplication.instance()
        if not app:
            return
            
        # تطبيق على جميع العناصر
        for widget in app.allWidgets():
            if widget and hasattr(widget, 'setFont'):
                try:
                    widget.setFont(font)
                except:
                    pass  # تجاهل الأخطاء
                    
    def apply_font_styles(self):
        """تطبيق أنماط CSS للخطوط"""
        app = QApplication.instance()
        if not app:
            return
            
        size = self.current_font_size
        
        # إنشاء أنماط CSS شاملة
        font_css = f"""
        /* أنماط الخط الشاملة */
        QWidget {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        /* الأزرار */
        QPushButton {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
            font-weight: 500;
        }}
        
        /* أزرار الشريط الجانبي */
        QPushButton#sidebar_button {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
            font-weight: 500;
        }}
        
        /* التسميات */
        QLabel {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        /* تسمية العنوان */
        QLabel#app_title {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size + 6}px;
            font-weight: bold;
        }}
        
        /* حقول الإدخال */
        QLineEdit {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        QTextEdit {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        QPlainTextEdit {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        /* القوائم المنسدلة */
        QComboBox {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        QComboBox QAbstractItemView {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        /* الجداول */
        QTableWidget {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        QTableView {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        QHeaderView::section {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
            font-weight: bold;
        }}
        
        /* التبويبات */
        QTabWidget {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        QTabBar::tab {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        /* المجموعات */
        QGroupBox {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
            font-weight: bold;
        }}
        
        /* مربعات الاختيار */
        QCheckBox {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        QRadioButton {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        /* القوائم */
        QListWidget {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        QTreeWidget {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        /* شريط القوائم */
        QMenuBar {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        QMenu {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        /* شريط الحالة */
        QStatusBar {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        /* شريط الأدوات */
        QToolBar {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        /* مربعات الحوار */
        QDialog {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        
        /* مربعات الرسائل */
        QMessageBox {{
            font-family: "{self.font_family}", "Tahoma", sans-serif;
            font-size: {size}px;
        }}
        """
        
        # الحصول على الأنماط الحالية
        current_style = app.styleSheet()
        
        # إزالة أنماط الخط القديمة
        lines = current_style.split('\n')
        filtered_lines = []
        skip_font_section = False
        
        for line in lines:
            if '/* أنماط الخط' in line or '/* أنماط الخط الشاملة */' in line:
                skip_font_section = True
                continue
            elif skip_font_section and (line.strip() == '' or line.strip().startswith('/*')):
                if not line.strip().startswith('/*'):
                    skip_font_section = False
                continue
            elif not skip_font_section:
                filtered_lines.append(line)
        
        # دمج الأنماط الجديدة
        new_style = '\n'.join(filtered_lines) + '\n' + font_css
        app.setStyleSheet(new_style)
        
        print(f"تم تطبيق حجم الخط {size}px على جميع العناصر")
        
    def load_font_size_from_config(self):
        """تحميل حجم الخط من الإعدادات"""
        try:
            from ..config import get_config
            ui_config = get_config("ui")
            saved_size = ui_config.get("font_size", 12)
            self.set_font_size(saved_size)
            return saved_size
        except Exception as e:
            print(f"خطأ في تحميل حجم الخط: {e}")
            self.set_font_size(12)
            return 12
            
    def save_font_size_to_config(self, size: int):
        """حفظ حجم الخط في الإعدادات"""
        try:
            from ..config import update_config
            update_config("ui", "font_size", size)
            print(f"تم حفظ حجم الخط: {size}")
            return True
        except Exception as e:
            print(f"خطأ في حفظ حجم الخط: {e}")
            return False


# إنشاء مثيل عام لمدير الخطوط
font_manager = FontManager()


def get_font_manager() -> FontManager:
    """الحصول على مدير الخطوط"""
    return font_manager


def set_global_font_size(size: int):
    """تعيين حجم الخط العام"""
    font_manager.set_font_size(size)


def get_global_font_size() -> int:
    """الحصول على حجم الخط العام"""
    return font_manager.get_font_size()
