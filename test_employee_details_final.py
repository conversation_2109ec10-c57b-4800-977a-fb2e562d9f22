#!/usr/bin/env python3
"""
اختبار نهائي لتفاصيل الموظف
Final Test for Employee Details
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication
from src.database import db_manager, get_db_session_context
from src.models import Employee


def test_employee_details_complete():
    """اختبار شامل لتفاصيل الموظف"""
    print("🧪 اختبار شامل لتفاصيل الموظف...")
    print("=" * 50)
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        # البحث عن موظف
        with get_db_session_context() as session:
            employee = session.query(Employee).filter_by(is_active=True).first()
            
            if not employee:
                print("❌ لا يوجد موظفين في النظام")
                return False
            
            print(f"✅ الموظف: {employee.full_name}")
            print(f"   معرف الموظف: {employee.id}")
            
            # إنشاء QApplication
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
                app.setLayoutDirection(app.RightToLeft)
            
            # تحميل الأنماط
            try:
                styles_dir = Path("src/styles")
                main_style_file = styles_dir / "main.qss"
                if main_style_file.exists():
                    with open(main_style_file, 'r', encoding='utf-8') as f:
                        app.setStyleSheet(f.read())
                print("✅ تم تحميل الأنماط")
            except Exception as e:
                print(f"⚠️ فشل في تحميل الأنماط: {e}")
            
            # إنشاء نافذة التفاصيل
            print("\n🔧 إنشاء نافذة تفاصيل الموظف...")
            from src.views.employee_details import EmployeeDetailsDialog
            
            dialog = EmployeeDetailsDialog(employee.id)
            
            # التحقق من تحميل البيانات
            if hasattr(dialog, 'employee_data'):
                print("✅ تم تحميل بيانات الموظف بنجاح")
                
                # عرض ملخص البيانات
                data = dialog.employee_data
                print(f"\n📋 ملخص البيانات المحملة:")
                print(f"   الاسم: {data['full_name']}")
                print(f"   الرقم الوظيفي: {data['employee_number']}")
                print(f"   العمر: {data['age']} سنة")
                print(f"   الراتب: {data['basic_salary']:,.0f} د.ع")
                print(f"   القسم: {data['department_name'] or 'غير محدد'}")
                print(f"   العنوان الوظيفي: {data['job_title_name'] or 'غير محدد'}")
                
            else:
                print("❌ فشل في تحميل بيانات الموظف")
                return False
            
            # عرض النافذة
            print(f"\n🖥️ عرض نافذة التفاصيل...")
            dialog.show()
            
            print("✅ تم فتح نافذة تفاصيل الموظف بنجاح!")
            print("\n🎯 النافذة تحتوي على:")
            print("• المعلومات الأساسية للموظف")
            print("• المعلومات الوظيفية")
            print("• المعاملات المالية")
            print("• سجل الرواتب")
            
            print("\n👆 يمكنك الآن:")
            print("1. مراجعة جميع التفاصيل")
            print("2. التنقل بين التبويبات")
            print("3. إغلاق النافذة")
            print("\nاضغط Ctrl+C لإنهاء الاختبار")
            
            # تشغيل التطبيق
            return app.exec()
            
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار نهائي لنافذة تفاصيل الموظف")
    print("=" * 60)
    
    result = test_employee_details_complete()
    
    if result == 0:
        print("\n🎉 الاختبار نجح بالكامل!")
        print("\n✅ تم إصلاح جميع المشاكل:")
        print("• إصلاح مشكلة national_id")
        print("• إزالة الحقول غير الموجودة")
        print("• إصلاح مشكلة Session")
        print("• تحسين تحميل البيانات")
        print("• إضافة معلومات إضافية مفيدة")
        
        print("\n🚀 النظام جاهز للاستخدام:")
        print("python run_hr_system.py")
        print("ثم اذهب إلى الموظفين > حدد موظف > عرض التفاصيل")
        
        return 0
    else:
        print("💥 فشل الاختبار!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
