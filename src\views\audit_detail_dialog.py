"""
نافذة تفاصيل سجل التدقيق المحسنة
Enhanced Audit Detail Dialog
"""

import json
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QTextEdit, 
    QTableWidget, QTableWidgetItem, QPushButton, QFrame,
    QTabWidget, QWidget, QScrollArea, QGridLayout
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QColor

from ..utils import apply_rtl_layout, format_datetime, show_message
from ..database.connection import get_db_session_context
from ..models.audit_log import AuditLog


class AuditDetailDialog(QDialog):
    """نافذة تفاصيل سجل التدقيق المحسنة"""
    
    def __init__(self, audit_log: AuditLog, parent=None):
        super().__init__(parent)
        self.audit_log = audit_log
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم المحسنة"""
        self.setWindowTitle("🔍 تفاصيل سجل التدقيق")
        self.setModal(True)
        self.resize(900, 750)
        apply_rtl_layout(self)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # العنوان الرئيسي
        self.create_header(main_layout)
        
        # التبويبات للتفاصيل
        self.create_details_tabs(main_layout)
        
        # أزرار الحوار
        self.create_dialog_buttons(main_layout)
        
    def create_header(self, layout: QVBoxLayout):
        """إنشاء العنوان الرئيسي"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2196F3, stop:1 #1976D2);
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        
        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(5)
        
        # العنوان
        title = QLabel(f"🔍 تفاصيل العملية: {self.audit_log.action_type.value}")
        title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: white;
                background: transparent;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        
        # معلومات سريعة
        quick_info = QLabel(f"📅 {self.format_timestamp()} | 👤 {self.audit_log.user_name or 'غير محدد'}")
        quick_info.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                color: #E3F2FD;
                background: transparent;
            }
        """)
        quick_info.setAlignment(Qt.AlignCenter)
        
        header_layout.addWidget(title)
        header_layout.addWidget(quick_info)
        
        layout.addWidget(header_frame)
        
    def create_details_tabs(self, layout: QVBoxLayout):
        """إنشاء تبويبات التفاصيل"""
        tabs_widget = QTabWidget()
        tabs_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
                padding: 10px;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-bottom-color: #C2C7CB;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                min-width: 120px;
                padding: 8px 12px;
                margin: 1px;
                font-weight: bold;
                font-size: 12px;
                color: #495057;
            }
            QTabBar::tab:selected {
                background-color: #2196F3;
                color: white;
                border-color: #2196F3;
            }
            QTabBar::tab:hover:!selected {
                background-color: #e3f2fd;
                color: #1976D2;
            }
        """)
        
        # تبويب المعلومات الأساسية
        self.create_basic_info_tab(tabs_widget)
        
        # تبويب التغييرات
        self.create_changes_tab(tabs_widget)
        
        # تبويب المعلومات التقنية
        self.create_technical_info_tab(tabs_widget)
        
        # تبويب السياق
        self.create_context_tab(tabs_widget)
        
        layout.addWidget(tabs_widget)
        
    def create_basic_info_tab(self, tabs_widget: QTabWidget):
        """إنشاء تبويب المعلومات الأساسية"""
        basic_widget = QWidget()
        layout = QVBoxLayout(basic_widget)
        layout.setSpacing(15)
        
        # بطاقة معلومات العملية
        operation_card = self.create_info_card("⚡ معلومات العملية", [
            ("نوع العملية", self.get_action_type_display()),
            ("الوصف", self.audit_log.description),
            ("الوقت", self.format_timestamp()),
            ("المدة المقدرة", self.estimate_duration())
        ])
        layout.addWidget(operation_card)
        
        # بطاقة معلومات المستخدم
        user_card = self.create_info_card("👤 معلومات المستخدم", [
            ("اسم المستخدم", self.audit_log.user_name or "غير محدد"),
            ("عنوان IP", self.audit_log.ip_address or "غير محدد"),
            ("معلومات المتصفح", getattr(self.audit_log, 'user_agent', None) or "غير محدد"),
            ("مستوى المخاطر", self.assess_risk_level())
        ])
        layout.addWidget(user_card)
        
        # بطاقة معلومات البيانات
        if self.audit_log.table_name or self.audit_log.record_id:
            data_card = self.create_info_card("🗃️ معلومات البيانات", [
                ("الجدول المتأثر", self.audit_log.table_name or "غير محدد"),
                ("معرف السجل", self.audit_log.record_id or "غير محدد"),
                ("نوع التأثير", self.get_impact_type()),
                ("حالة البيانات", self.get_data_status())
            ])
            layout.addWidget(data_card)
        
        layout.addStretch()
        tabs_widget.addTab(basic_widget, "📋 المعلومات الأساسية")
        
    def create_changes_tab(self, tabs_widget: QTabWidget):
        """إنشاء تبويب التغييرات"""
        changes_widget = QWidget()
        layout = QVBoxLayout(changes_widget)
        layout.setSpacing(15)
        
        if self.audit_log.old_values or self.audit_log.new_values:
            # جدول مقارنة التغييرات
            comparison_frame = QFrame()
            comparison_frame.setStyleSheet("""
                QFrame {
                    background-color: #f8f9fa;
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            
            comparison_layout = QVBoxLayout(comparison_frame)
            
            title_label = QLabel("🔄 مقارنة التغييرات")
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                }
            """)
            comparison_layout.addWidget(title_label)
            
            # جدول المقارنة
            changes_table = QTableWidget()
            self.setup_changes_table(changes_table)
            comparison_layout.addWidget(changes_table)
            
            layout.addWidget(comparison_frame)
        else:
            # رسالة عدم وجود تغييرات
            no_changes_label = QLabel("ℹ️ لا توجد تغييرات مسجلة لهذه العملية")
            no_changes_label.setAlignment(Qt.AlignCenter)
            no_changes_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #6c757d;
                    padding: 50px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    border: 2px dashed #dee2e6;
                }
            """)
            layout.addWidget(no_changes_label)
        
        layout.addStretch()
        tabs_widget.addTab(changes_widget, "🔄 التغييرات")
        
    def create_technical_info_tab(self, tabs_widget: QTabWidget):
        """إنشاء تبويب المعلومات التقنية"""
        technical_widget = QWidget()
        layout = QVBoxLayout(technical_widget)
        layout.setSpacing(15)
        
        # معلومات النظام
        system_card = self.create_info_card("🖥️ معلومات النظام", [
            ("معرف السجل", str(self.audit_log.id)),
            ("الطابع الزمني UTC", str(self.audit_log.timestamp)),
            ("حجم البيانات", self.calculate_data_size()),
            ("تنسيق البيانات", "JSON" if (self.audit_log.old_values or self.audit_log.new_values) else "نص")
        ])
        layout.addWidget(system_card)
        
        # معلومات الشبكة
        if self.audit_log.ip_address:
            network_card = self.create_info_card("🌐 معلومات الشبكة", [
                ("عنوان IP", self.audit_log.ip_address),
                ("نوع الشبكة", self.get_network_type()),
                ("الموقع المقدر", self.get_estimated_location()),
                ("مستوى الأمان", self.get_security_level())
            ])
            layout.addWidget(network_card)
        
        # البيانات الخام
        raw_data_frame = QFrame()
        raw_data_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        raw_data_layout = QVBoxLayout(raw_data_frame)
        
        raw_title = QLabel("📄 البيانات الخام")
        raw_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        raw_data_layout.addWidget(raw_title)
        
        raw_text = QTextEdit()
        raw_text.setPlainText(self.get_raw_data())
        raw_text.setReadOnly(True)
        raw_text.setMaximumHeight(200)
        raw_text.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                font-family: 'Courier New', monospace;
                font-size: 11px;
                border: 1px solid #34495e;
                border-radius: 4px;
                padding: 10px;
            }
        """)
        raw_data_layout.addWidget(raw_text)
        
        layout.addWidget(raw_data_frame)
        layout.addStretch()
        
        tabs_widget.addTab(technical_widget, "🔧 المعلومات التقنية")
        
    def create_context_tab(self, tabs_widget: QTabWidget):
        """إنشاء تبويب السياق"""
        context_widget = QWidget()
        layout = QVBoxLayout(context_widget)
        layout.setSpacing(15)
        
        # العمليات ذات الصلة
        try:
            with get_db_session_context() as session:
                # العمليات السابقة واللاحقة
                related_operations = self.get_related_operations(session)
                
                if related_operations:
                    related_frame = QFrame()
                    related_frame.setStyleSheet("""
                        QFrame {
                            background-color: #f8f9fa;
                            border: 2px solid #dee2e6;
                            border-radius: 8px;
                            padding: 15px;
                        }
                    """)
                    
                    related_layout = QVBoxLayout(related_frame)
                    
                    related_title = QLabel("🔗 العمليات ذات الصلة")
                    related_title.setStyleSheet("""
                        QLabel {
                            font-size: 14px;
                            font-weight: bold;
                            color: #2c3e50;
                            margin-bottom: 10px;
                        }
                    """)
                    related_layout.addWidget(related_title)
                    
                    # جدول العمليات ذات الصلة
                    related_table = QTableWidget()
                    self.setup_related_operations_table(related_table, related_operations)
                    related_layout.addWidget(related_table)
                    
                    layout.addWidget(related_frame)
                
        except Exception as e:
            error_label = QLabel(f"⚠️ خطأ في تحميل السياق: {e}")
            error_label.setStyleSheet("color: #e74c3c; padding: 20px;")
            layout.addWidget(error_label)
        
        layout.addStretch()
        tabs_widget.addTab(context_widget, "🔗 السياق")
        
    def create_dialog_buttons(self, layout: QVBoxLayout):
        """إنشاء أزرار الحوار"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-top: 2px solid #dee2e6;
                padding: 15px;
                margin-top: 10px;
            }
        """)
        
        buttons_layout = QHBoxLayout(buttons_frame)
        
        # زر إغلاق
        close_btn = QPushButton("✖️ إغلاق")
        close_btn.setMinimumHeight(40)
        close_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #6c757d;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_btn.clicked.connect(self.accept)
        
        # زر تصدير
        export_btn = QPushButton("📤 تصدير التفاصيل")
        export_btn.setMinimumHeight(40)
        export_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #28a745;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        export_btn.clicked.connect(self.export_details)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(export_btn)
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)

    # الوظائف المساعدة
    def create_info_card(self, title: str, info_list: list) -> QFrame:
        """إنشاء بطاقة معلومات"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setSpacing(10)

        # عنوان البطاقة
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border-bottom: 2px solid #3498db;
                padding-bottom: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # معلومات البطاقة
        info_layout = QGridLayout()
        info_layout.setSpacing(8)

        for i, (label, value) in enumerate(info_list):
            # التسمية
            label_widget = QLabel(f"{label}:")
            label_widget.setStyleSheet("""
                QLabel {
                    font-weight: bold;
                    color: #495057;
                    font-size: 12px;
                }
            """)

            # القيمة
            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("""
                QLabel {
                    color: #2c3e50;
                    font-size: 12px;
                    padding: 4px 8px;
                    background-color: #f8f9fa;
                    border-radius: 4px;
                }
            """)
            value_widget.setWordWrap(True)

            info_layout.addWidget(label_widget, i, 0)
            info_layout.addWidget(value_widget, i, 1)

        layout.addLayout(info_layout)
        return card

    def format_timestamp(self) -> str:
        """تنسيق الطابع الزمني"""
        return self.audit_log.timestamp.strftime('%Y-%m-%d %H:%M:%S')

    def get_action_type_display(self) -> str:
        """الحصول على عرض نوع العملية مع الأيقونة"""
        action_icons = {
            "إنشاء": "➕",
            "تعديل": "✏️",
            "حذف": "🗑️",
            "تسجيل دخول": "🔑",
            "تسجيل خروج": "🚪",
            "صرف راتب": "💰",
            "معاملة مالية": "💳",
            "نسخ احتياطي": "💾",
            "استعادة": "🔄",
            "توليد تقرير": "📊"
        }

        action_value = self.audit_log.action_type.value
        icon = action_icons.get(action_value, "⚡")
        return f"{icon} {action_value}"

    def estimate_duration(self) -> str:
        """تقدير مدة العملية"""
        action_durations = {
            "إنشاء": "< 1 ثانية",
            "تعديل": "< 1 ثانية",
            "حذف": "< 1 ثانية",
            "تسجيل دخول": "2-3 ثواني",
            "تسجيل خروج": "< 1 ثانية",
            "صرف راتب": "1-2 ثانية",
            "معاملة مالية": "1-2 ثانية",
            "نسخ احتياطي": "5-30 دقيقة",
            "استعادة": "10-60 دقيقة",
            "توليد تقرير": "2-10 ثواني"
        }

        return action_durations.get(self.audit_log.action_type.value, "غير محدد")

    def assess_risk_level(self) -> str:
        """تقييم مستوى المخاطر"""
        high_risk_actions = ["حذف", "استعادة", "نسخ احتياطي"]
        medium_risk_actions = ["تعديل", "صرف راتب", "معاملة مالية"]

        action_value = self.audit_log.action_type.value

        if action_value in high_risk_actions:
            return "🔴 عالي"
        elif action_value in medium_risk_actions:
            return "🟡 متوسط"
        else:
            return "🟢 منخفض"

    def get_impact_type(self) -> str:
        """نوع التأثير على البيانات"""
        action_value = self.audit_log.action_type.value

        if action_value == "إنشاء":
            return "إضافة بيانات جديدة"
        elif action_value == "تعديل":
            return "تعديل بيانات موجودة"
        elif action_value == "حذف":
            return "إزالة بيانات"
        else:
            return "عملية نظام"

    def get_data_status(self) -> str:
        """حالة البيانات"""
        if self.audit_log.action_type.value == "حذف":
            return "محذوف"
        elif self.audit_log.new_values:
            return "محدث"
        else:
            return "مستقر"

    def calculate_data_size(self) -> str:
        """حساب حجم البيانات"""
        total_size = 0

        if self.audit_log.old_values:
            total_size += len(json.dumps(self.audit_log.old_values, ensure_ascii=False))

        if self.audit_log.new_values:
            total_size += len(json.dumps(self.audit_log.new_values, ensure_ascii=False))

        total_size += len(self.audit_log.description)

        if total_size < 1024:
            return f"{total_size} بايت"
        elif total_size < 1024 * 1024:
            return f"{total_size / 1024:.1f} كيلوبايت"
        else:
            return f"{total_size / (1024 * 1024):.1f} ميجابايت"

    def get_network_type(self) -> str:
        """نوع الشبكة"""
        if not self.audit_log.ip_address:
            return "غير محدد"

        ip = self.audit_log.ip_address

        if ip.startswith("192.168.") or ip.startswith("10.") or ip.startswith("172."):
            return "شبكة محلية (LAN)"
        elif ip == "127.0.0.1" or ip == "localhost":
            return "محلي (Localhost)"
        else:
            return "شبكة عامة (WAN)"

    def get_estimated_location(self) -> str:
        """الموقع المقدر"""
        if not self.audit_log.ip_address:
            return "غير محدد"

        ip = self.audit_log.ip_address

        if ip.startswith("192.168.") or ip.startswith("10.") or ip.startswith("172."):
            return "داخل الشبكة المحلية"
        elif ip == "127.0.0.1":
            return "الجهاز المحلي"
        else:
            return "خارج الشبكة المحلية"

    def get_security_level(self) -> str:
        """مستوى الأمان"""
        if not self.audit_log.ip_address:
            return "غير محدد"

        ip = self.audit_log.ip_address

        if ip == "127.0.0.1":
            return "🟢 آمن (محلي)"
        elif ip.startswith("192.168.") or ip.startswith("10."):
            return "🟡 آمن نسبياً (شبكة محلية)"
        else:
            return "🔴 يتطلب مراجعة (شبكة خارجية)"

    def get_raw_data(self) -> str:
        """الحصول على البيانات الخام"""
        raw_data = {
            "id": self.audit_log.id,
            "timestamp": str(self.audit_log.timestamp),
            "action_type": self.audit_log.action_type.name,
            "table_name": self.audit_log.table_name,
            "record_id": self.audit_log.record_id,
            "description": self.audit_log.description,
            "old_values": self.audit_log.old_values,
            "new_values": self.audit_log.new_values,
            "user_name": self.audit_log.user_name,
            "ip_address": self.audit_log.ip_address
        }

        return json.dumps(raw_data, ensure_ascii=False, indent=2)

    def setup_changes_table(self, table: QTableWidget):
        """إعداد جدول التغييرات"""
        headers = ["الحقل", "القيمة القديمة", "القيمة الجديدة", "نوع التغيير"]
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)

        # جمع جميع الحقول
        all_fields = set()
        if self.audit_log.old_values:
            all_fields.update(self.audit_log.old_values.keys())
        if self.audit_log.new_values:
            all_fields.update(self.audit_log.new_values.keys())

        table.setRowCount(len(all_fields))

        for row, field in enumerate(sorted(all_fields)):
            old_value = self.audit_log.old_values.get(field, "") if self.audit_log.old_values else ""
            new_value = self.audit_log.new_values.get(field, "") if self.audit_log.new_values else ""

            # تحديد نوع التغيير
            if old_value and new_value:
                change_type = "تعديل"
                change_color = "#fff3cd"  # أصفر
            elif new_value and not old_value:
                change_type = "إضافة"
                change_color = "#d4edda"  # أخضر
            elif old_value and not new_value:
                change_type = "حذف"
                change_color = "#f8d7da"  # أحمر
            else:
                change_type = "لا تغيير"
                change_color = "#f8f9fa"  # رمادي

            # إضافة البيانات للجدول
            items = [
                QTableWidgetItem(str(field)),
                QTableWidgetItem(str(old_value)),
                QTableWidgetItem(str(new_value)),
                QTableWidgetItem(change_type)
            ]

            for col, item in enumerate(items):
                item.setTextAlignment(Qt.AlignCenter)
                item.setBackground(QColor(change_color))
                table.setItem(row, col, item)

        # تحديد عرض الأعمدة
        column_widths = [150, 200, 200, 100]
        for i, width in enumerate(column_widths):
            table.setColumnWidth(i, width)

        table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-size: 12px;
                gridline-color: #e9ecef;
            }
            QHeaderView::section {
                background-color: #495057;
                color: white;
                font-weight: bold;
                font-size: 12px;
                padding: 8px;
                border: none;
            }
        """)

    def get_related_operations(self, session) -> list:
        """الحصول على العمليات ذات الصلة"""
        related_ops = []

        try:
            # العمليات على نفس السجل
            if self.audit_log.table_name and self.audit_log.record_id:
                same_record_ops = session.query(AuditLog).filter(
                    AuditLog.table_name == self.audit_log.table_name,
                    AuditLog.record_id == self.audit_log.record_id,
                    AuditLog.id != self.audit_log.id
                ).order_by(AuditLog.timestamp.desc()).limit(5).all()

                related_ops.extend(same_record_ops)

            # العمليات من نفس المستخدم في نفس الوقت تقريباً
            if self.audit_log.user_name:
                time_window = timedelta(minutes=5)
                start_time = self.audit_log.timestamp - time_window
                end_time = self.audit_log.timestamp + time_window

                same_user_ops = session.query(AuditLog).filter(
                    AuditLog.user_name == self.audit_log.user_name,
                    AuditLog.timestamp.between(start_time, end_time),
                    AuditLog.id != self.audit_log.id
                ).order_by(AuditLog.timestamp.desc()).limit(3).all()

                related_ops.extend(same_user_ops)

            # إزالة التكرارات
            seen_ids = set()
            unique_ops = []
            for op in related_ops:
                if op.id not in seen_ids:
                    unique_ops.append(op)
                    seen_ids.add(op.id)

            return unique_ops[:10]  # أقصى 10 عمليات

        except Exception as e:
            print(f"خطأ في الحصول على العمليات ذات الصلة: {e}")
            return []

    def setup_related_operations_table(self, table: QTableWidget, operations: list):
        """إعداد جدول العمليات ذات الصلة"""
        headers = ["الوقت", "نوع العملية", "المستخدم", "الوصف"]
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        table.setRowCount(len(operations))

        for row, op in enumerate(operations):
            items = [
                QTableWidgetItem(op.timestamp.strftime('%H:%M:%S')),
                QTableWidgetItem(op.action_type.value),
                QTableWidgetItem(op.user_name or "غير محدد"),
                QTableWidgetItem(op.description[:50] + "..." if len(op.description) > 50 else op.description)
            ]

            for col, item in enumerate(items):
                item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, col, item)

        # تحديد عرض الأعمدة
        column_widths = [100, 120, 120, 300]
        for i, width in enumerate(column_widths):
            table.setColumnWidth(i, width)

        table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-size: 11px;
                gridline-color: #e9ecef;
            }
            QHeaderView::section {
                background-color: #6c757d;
                color: white;
                font-weight: bold;
                font-size: 11px;
                padding: 6px;
                border: none;
            }
        """)

    def export_details(self):
        """تصدير تفاصيل السجل"""
        try:
            from datetime import datetime
            import os

            # إنشاء مجلد التصدير
            export_dir = "exports"
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)

            # اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{export_dir}/audit_detail_{self.audit_log.id}_{timestamp}.json"

            # البيانات للتصدير
            export_data = {
                "audit_log_id": self.audit_log.id,
                "export_timestamp": datetime.now().isoformat(),
                "basic_info": {
                    "action_type": self.audit_log.action_type.value,
                    "description": self.audit_log.description,
                    "timestamp": str(self.audit_log.timestamp),
                    "user_name": self.audit_log.user_name,
                    "ip_address": self.audit_log.ip_address,
                    "table_name": self.audit_log.table_name,
                    "record_id": self.audit_log.record_id
                },
                "changes": {
                    "old_values": self.audit_log.old_values,
                    "new_values": self.audit_log.new_values
                },
                "analysis": {
                    "risk_level": self.assess_risk_level(),
                    "impact_type": self.get_impact_type(),
                    "data_status": self.get_data_status(),
                    "estimated_duration": self.estimate_duration(),
                    "network_type": self.get_network_type(),
                    "security_level": self.get_security_level()
                }
            }

            # كتابة الملف
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            show_message(
                self,
                "نجح",
                f"تم تصدير تفاصيل السجل بنجاح إلى:\n{filename}",
                "information"
            )

        except Exception as e:
            show_message(self, "خطأ", f"فشل في تصدير التفاصيل: {e}", "error")
