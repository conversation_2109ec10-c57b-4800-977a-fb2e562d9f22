# نظام إدارة شؤون الموظفين - HR Management System

نظام شامل ومتكامل لإدارة شؤون الموظفين مبني باستخدام Python و PySide6 مع قاعدة بيانات PostgreSQL.

## الميزات الرئيسية

- **إدارة الموظفين**: إضافة، تعديل، حذف، وبحث متقدم
- **إدارة الأقسام والعناوين الوظيفية**: جداول مرجعية قابلة للتخصيص
- **المعاملات المالية**: إدارة السلف، الخصومات، المكافآت، وديون الماركت
- **نظام الرواتب**: حساب وصرف الرواتب الشهرية
- **التقارير الشاملة**: تقارير مفصلة مع إمكانية التصدير والطباعة
- **النسخ الاحتياطي**: نظام تلقائي للنسخ الاحتياطي والاستعادة
- **تدقيق الأنشطة**: تسجيل جميع العمليات الهامة
- **واجهة عصرية**: تصميم Flat مع دعم الوضع الليلي والنهاري

## متطلبات النظام

- Python 3.9 أو أحدث
- PostgreSQL 12 أو أحدث
- نظام التشغيل: Windows, macOS, Linux

## التثبيت

1. تأكد من تثبيت Python 3.9+ و Poetry
2. استنسخ المشروع:
   ```bash
   git clone <repository-url>
   cd hr-management-system
   ```

3. ثبت التبعيات:
   ```bash
   poetry install
   ```

4. أعد إعداد قاعدة البيانات:
   ```bash
   poetry run python src/database/setup.py
   ```

5. شغل التطبيق:
   ```bash
   poetry run python src/main.py
   ```

## هيكل المشروع

```
src/
├── models/          # نماذج البيانات
├── views/           # واجهات المستخدم
├── viewmodels/      # منطق العرض
├── controllers/     # منطق الأعمال
├── services/        # الخدمات
├── utils/           # الأدوات المساعدة
├── assets/          # الموارد المرئية
├── styles/          # ملفات التصميم
├── database/        # إعداد قاعدة البيانات
├── security/        # وحدات الأمان
├── reports/         # قوالب التقارير
└── main.py          # نقطة الدخول
```

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.
