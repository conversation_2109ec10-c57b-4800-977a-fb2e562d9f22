#!/usr/bin/env python3
"""
سكريبت لفتح Qt Designer مع ملف الواجهة الرئيسية
Script to open Qt Designer with main window UI file
"""

import subprocess
import sys
import os

def open_qt_designer():
    """فتح Qt Designer مع ملف الواجهة الرئيسية"""
    ui_file = "ui_files/main_window.ui"
    
    print("🎨 فتح Qt Designer...")
    print("=" * 40)
    
    # التحقق من وجود الملف
    if not os.path.exists(ui_file):
        print(f"❌ الملف غير موجود: {ui_file}")
        return False
    
    try:
        # محاولة فتح Qt Designer مع الملف
        print(f"📂 فتح الملف: {ui_file}")
        
        # تشغيل Qt Designer
        subprocess.Popen([
            'pyside6-designer', 
            ui_file
        ])
        
        print("✅ تم فتح Qt Designer بنجاح!")
        print("\n📝 نصائح للتعديل:")
        print("- يمكنك تعديل الألوان والخطوط")
        print("- إضافة أو حذف عناصر الواجهة")
        print("- تغيير ترتيب القوائم")
        print("- تعديل أحجام العناصر")
        print("\n💾 لا تنس حفظ التغييرات بـ Ctrl+S")
        print("🔄 بعد الحفظ، شغّل: python convert_ui.py")
        
        return True
        
    except FileNotFoundError:
        print("❌ Qt Designer غير مثبت أو غير موجود في PATH")
        print("\n🔧 حلول مقترحة:")
        print("1. تثبيت PySide6: pip install PySide6")
        print("2. أو تشغيل من Terminal: pyside6-designer")
        print("3. أو من VS Code Terminal: pyside6-designer ui_files/main_window.ui")
        return False
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def show_file_info():
    """عرض معلومات الملف"""
    ui_file = "ui_files/main_window.ui"
    
    if os.path.exists(ui_file):
        file_size = os.path.getsize(ui_file)
        print(f"📄 معلومات الملف:")
        print(f"   📁 المسار: {ui_file}")
        print(f"   📏 الحجم: {file_size} بايت")
        print(f"   ✅ الحالة: موجود وجاهز للتعديل")
    else:
        print(f"❌ الملف غير موجود: {ui_file}")

def main():
    """الدالة الرئيسية"""
    print("🚀 مشغل Qt Designer للواجهة الرئيسية")
    print("=" * 50)
    
    # عرض معلومات الملف
    show_file_info()
    print()
    
    # فتح Qt Designer
    success = open_qt_designer()
    
    if success:
        print("\n🎯 الخطوات التالية:")
        print("1. عدّل الواجهة في Qt Designer")
        print("2. احفظ التغييرات (Ctrl+S)")
        print("3. شغّل: python convert_ui.py")
        print("4. ادمج الكود الجديد مع النظام")
    else:
        print("\n💡 بدائل أخرى:")
        print("- افتح Terminal واكتب: pyside6-designer")
        print("- أو من VS Code: Ctrl+` ثم pyside6-designer")

if __name__ == "__main__":
    main()
