#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إعادة تعيين النظام للوضع النهاري
Reset System to Light Mode
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))


def reset_theme_settings():
    """إعادة تعيين إعدادات الثيم بالكامل"""
    print("🔄 إعادة تعيين إعدادات الثيم...")
    
    try:
        # إعادة تعيين theme_manager
        from src.utils.theme_manager import ThemeManager
        theme_manager = ThemeManager()
        
        print("☀️ إعادة تعيين theme_manager للوضع النهاري...")
        success1 = theme_manager.reset_to_default()
        
        # إعادة تعيين appearance_manager
        try:
            from src.utils.appearance_manager import get_appearance_manager
            appearance_manager = get_appearance_manager()
            
            print("☀️ إعادة تعيين appearance_manager للوضع النهاري...")
            appearance_manager.current_settings['theme_mode'] = 'light'
            appearance_manager.save_settings()
            print("✅ تم إعادة تعيين appearance_manager")
            success2 = True
            
        except Exception as e:
            print(f"⚠️ تحذير في appearance_manager: {e}")
            success2 = True  # لا نعتبرها خطأ حرج
        
        # إعادة تعيين إعدادات Qt
        try:
            settings = QSettings("HR_System", "Theme")
            settings.setValue("current_theme", "light")
            settings.sync()
            print("✅ تم إعادة تعيين إعدادات Qt")
            success3 = True
            
        except Exception as e:
            print(f"⚠️ تحذير في إعدادات Qt: {e}")
            success3 = True
        
        if success1 and success2 and success3:
            print("✅ تم إعادة تعيين جميع إعدادات الثيم للوضع النهاري")
            return True
        else:
            print("⚠️ تم إعادة التعيين مع بعض التحذيرات")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إعادة تعيين الثيم: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_reset_tool():
    """إنشاء أداة إعادة التعيين"""
    
    class ResetTool(QWidget):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("☀️ إعادة تعيين للوضع النهاري")
            self.setGeometry(200, 200, 600, 500)
            self.setWindowFlags(Qt.WindowStaysOnTopHint)
            
            self.setup_ui()
            
        def setup_ui(self):
            """إعداد واجهة الأداة"""
            layout = QVBoxLayout(self)
            layout.setContentsMargins(25, 25, 25, 25)
            layout.setSpacing(20)
            
            # العنوان
            title = QLabel("☀️ إعادة تعيين النظام للوضع النهاري")
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet("""
                font-size: 22px; 
                font-weight: bold; 
                color: #2c3e50; 
                margin: 20px;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 12px;
                border: 2px solid #3498db;
            """)
            layout.addWidget(title)
            
            # معلومات المشكلة
            problem_info = QLabel("""
🔍 المشكلة:
• النظام يفتح بالوضع الليلي رغم اختيار الوضع النهاري
• عدم حفظ إعداد الوضع النهاري بشكل صحيح
• تضارب بين مديري الثيمات المختلفة

💡 الحل:
• إعادة تعيين جميع إعدادات الثيم
• مزامنة جميع مديري الثيمات
• فرض الوضع النهاري كافتراضي
            """)
            problem_info.setStyleSheet("""
                font-size: 14px; 
                color: #34495e; 
                padding: 20px;
                background-color: #fff3cd;
                border-radius: 10px;
                border-left: 5px solid #ffc107;
                line-height: 1.8;
            """)
            layout.addWidget(problem_info)
            
            # حالة النظام
            self.status_label = QLabel("🔍 فحص حالة النظام...")
            self.status_label.setAlignment(Qt.AlignCenter)
            self.status_label.setStyleSheet("""
                font-size: 16px; 
                color: #6c757d; 
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            """)
            layout.addWidget(self.status_label)
            
            # أزرار الإجراءات
            buttons_layout = QVBoxLayout()
            
            # زر فحص الحالة
            check_btn = QPushButton("🔍 فحص حالة الثيم")
            check_btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px; font-weight: bold; color: white; 
                    background-color: #17a2b8; border: none; border-radius: 10px; 
                    padding: 15px 25px; margin: 8px;
                }
                QPushButton:hover { background-color: #138496; }
                QPushButton:pressed { background-color: #117a8b; }
            """)
            check_btn.clicked.connect(self.check_theme_status)
            buttons_layout.addWidget(check_btn)
            
            # زر إعادة التعيين
            reset_btn = QPushButton("☀️ إعادة تعيين للوضع النهاري")
            reset_btn.setStyleSheet("""
                QPushButton {
                    font-size: 18px; font-weight: bold; color: white; 
                    background-color: #ffc107; border: none; border-radius: 12px; 
                    padding: 18px 30px; margin: 10px;
                }
                QPushButton:hover { background-color: #e0a800; }
                QPushButton:pressed { background-color: #d39e00; }
            """)
            reset_btn.clicked.connect(self.reset_to_light)
            buttons_layout.addWidget(reset_btn)
            
            # زر إعادة تعيين شامل
            full_reset_btn = QPushButton("🔧 إعادة تعيين شامل")
            full_reset_btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px; font-weight: bold; color: white; 
                    background-color: #dc3545; border: none; border-radius: 10px; 
                    padding: 15px 25px; margin: 8px;
                }
                QPushButton:hover { background-color: #c82333; }
                QPushButton:pressed { background-color: #bd2130; }
            """)
            full_reset_btn.clicked.connect(self.full_reset)
            buttons_layout.addWidget(full_reset_btn)
            
            # زر تطبيق فوري
            apply_btn = QPushButton("⚡ تطبيق فوري")
            apply_btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px; font-weight: bold; color: white; 
                    background-color: #28a745; border: none; border-radius: 10px; 
                    padding: 15px 25px; margin: 8px;
                }
                QPushButton:hover { background-color: #218838; }
                QPushButton:pressed { background-color: #1e7e34; }
            """)
            apply_btn.clicked.connect(self.apply_immediately)
            buttons_layout.addWidget(apply_btn)
            
            layout.addLayout(buttons_layout)
            
            # سجل الأحداث
            self.log_text = QTextEdit()
            self.log_text.setMaximumHeight(150)
            self.log_text.setStyleSheet("""
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                background-color: #f8f9fa;
                color: #495057;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
            """)
            self.log_text.setPlainText("📝 سجل الأحداث:\n")
            layout.addWidget(self.log_text)
            
            # فحص أولي
            self.check_theme_status()
            
        def log_message(self, message):
            """إضافة رسالة للسجل"""
            self.log_text.append(f"[{QTime.currentTime().toString()}] {message}")
            self.log_text.verticalScrollBar().setValue(
                self.log_text.verticalScrollBar().maximum()
            )
            
        def check_theme_status(self):
            """فحص حالة الثيم"""
            try:
                from src.utils.theme_manager import ThemeManager
                theme_manager = ThemeManager()
                
                current_theme = theme_manager.get_current_theme()
                
                # فحص appearance_manager
                try:
                    from src.utils.appearance_manager import get_appearance_manager
                    appearance_manager = get_appearance_manager()
                    appearance_theme = appearance_manager.current_settings.get('theme_mode', 'unknown')
                except:
                    appearance_theme = 'غير متوفر'
                
                # فحص إعدادات Qt
                settings = QSettings("HR_System", "Theme")
                qt_theme = settings.value("current_theme", "غير محدد")
                
                status = f"""
الثيم الحالي:
• theme_manager: {current_theme}
• appearance_manager: {appearance_theme}  
• إعدادات Qt: {qt_theme}
                """
                
                self.status_label.setText(status.strip())
                
                if current_theme == "light" and appearance_theme == "light":
                    self.status_label.setStyleSheet("""
                        font-size: 14px; color: #155724; padding: 15px;
                        background-color: #d4edda; border-radius: 8px;
                        border: 1px solid #c3e6cb;
                    """)
                    self.log_message("✅ جميع الإعدادات صحيحة - الوضع النهاري")
                else:
                    self.status_label.setStyleSheet("""
                        font-size: 14px; color: #721c24; padding: 15px;
                        background-color: #f8d7da; border-radius: 8px;
                        border: 1px solid #f5c6cb;
                    """)
                    self.log_message("⚠️ يوجد تضارب في إعدادات الثيم")
                    
            except Exception as e:
                self.log_message(f"❌ خطأ في فحص الحالة: {e}")
        
        def reset_to_light(self):
            """إعادة تعيين للوضع النهاري"""
            self.log_message("☀️ بدء إعادة التعيين للوضع النهاري...")
            
            try:
                from src.utils.theme_manager import ThemeManager
                theme_manager = ThemeManager()
                
                success = theme_manager.reset_to_default()
                
                if success:
                    self.log_message("✅ تم إعادة التعيين للوضع النهاري")
                    self.status_label.setText("✅ تم إعادة التعيين للوضع النهاري")
                    self.status_label.setStyleSheet("""
                        font-size: 16px; color: #155724; padding: 15px;
                        background-color: #d4edda; border-radius: 8px;
                    """)
                else:
                    self.log_message("❌ فشل في إعادة التعيين")
                    
            except Exception as e:
                self.log_message(f"❌ خطأ في إعادة التعيين: {e}")
        
        def full_reset(self):
            """إعادة تعيين شامل"""
            self.log_message("🔧 بدء الإعادة التعيين الشامل...")
            
            success = reset_theme_settings()
            
            if success:
                self.log_message("✅ تم الإعادة التعيين الشامل بنجاح")
                self.status_label.setText("✅ تم الإعادة التعيين الشامل")
                QTimer.singleShot(1000, self.check_theme_status)
            else:
                self.log_message("❌ فشل في الإعادة التعيين الشامل")
        
        def apply_immediately(self):
            """تطبيق فوري للوضع النهاري"""
            self.log_message("⚡ تطبيق فوري للوضع النهاري...")
            
            try:
                app = QApplication.instance()
                if app:
                    # تطبيق الوضع النهاري مباشرة
                    from src.utils.theme_manager import ThemeManager
                    theme_manager = ThemeManager()
                    
                    success = theme_manager.set_theme("light")
                    
                    if success:
                        self.log_message("✅ تم التطبيق الفوري للوضع النهاري")
                        self.status_label.setText("✅ تم تطبيق الوضع النهاري فورياً")
                    else:
                        self.log_message("❌ فشل في التطبيق الفوري")
                else:
                    self.log_message("❌ لا يوجد تطبيق للتطبيق عليه")
                    
            except Exception as e:
                self.log_message(f"❌ خطأ في التطبيق الفوري: {e}")
    
    return ResetTool()


def main():
    """الدالة الرئيسية"""
    print("☀️ أداة إعادة تعيين النظام للوضع النهاري")
    print("=" * 50)
    
    # إعادة تعيين فورية
    if len(sys.argv) > 1 and sys.argv[1] == "--reset":
        print("🔄 إعادة تعيين فورية...")
        success = reset_theme_settings()
        return 0 if success else 1
    
    try:
        # التحقق من وجود تطبيق Qt جاري
        app = QApplication.instance()
        
        if not app:
            # إنشاء تطبيق جديد إذا لم يكن موجود
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
            print("📱 تم إنشاء تطبيق Qt جديد")
        else:
            print("📱 تم العثور على تطبيق Qt جاري")
        
        # إنشاء أداة الإعادة التعيين
        tool = create_reset_tool()
        tool.show()
        
        print("✅ تم فتح أداة إعادة التعيين!")
        print("\n🎯 استخدم الأزرار لإعادة تعيين النظام للوضع النهاري")
        
        # تشغيل التطبيق إذا لم يكن جارياً
        if not QApplication.instance():
            return app.exec()
        else:
            return 0
        
    except Exception as e:
        print(f"💥 خطأ في تشغيل أداة الإعادة التعيين: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
