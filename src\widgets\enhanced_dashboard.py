"""
لوحة التحكم المحسنة مع البيانات التوضيحية الشاملة
Enhanced Dashboard with Comprehensive Data Visualization
"""

from typing import Dict, List, Tuple
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QGridLayout, QScrollArea
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QPalette

from ..utils import apply_rtl_layout, set_font_size, make_bold, format_currency
from ..database import get_db_session_context
from ..models import Employee, FinancialTransaction, Department, JobTitle


class DataCard(QFrame):
    """بطاقة بيانات محسنة"""
    
    def __init__(self, title: str, value: str, subtitle: str, color: str, icon: str = "", parent=None):
        super().__init__(parent)
        self.setObjectName("data_card")
        self.color = color
        self.setup_ui(title, value, subtitle, icon)
        
    def setup_ui(self, title: str, value: str, subtitle: str, icon: str):
        """إعداد واجهة البطاقة"""
        self.setFixedSize(300, 200)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(8)
        
        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        
        if icon:
            icon_label = QLabel(icon)
            icon_label.setStyleSheet("font-size: 24px;")
            header_layout.addWidget(icon_label)
        
        self.title_label = QLabel(title)
        self.title_label.setObjectName("card_title")
        self.title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(self.title_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # القيمة الرئيسية
        self.value_label = QLabel(value)
        self.value_label.setObjectName("card_value")
        self.value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.value_label)
        
        # العنوان الفرعي
        self.subtitle_label = QLabel(subtitle)
        self.subtitle_label.setObjectName("card_subtitle")
        self.subtitle_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.subtitle_label)
        
        # تطبيق الألوان
        self.setStyleSheet(f"""
            QFrame#data_card {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 {self.color}, stop: 1 {self._darken_color(self.color)});
                border-radius: 12px;
                border: 2px solid {self._darken_color(self.color, 20)};
            }}
            QLabel#card_title {{
                color: rgba(255, 255, 255, 0.95);
                font-size: 14px;
                font-weight: bold;
                letter-spacing: 0.5px;
            }}
            QLabel#card_value {{
                color: white;
                font-size: 28px;
                font-weight: 900;
                letter-spacing: 1px;
                text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }}
            QLabel#card_subtitle {{
                color: rgba(255, 255, 255, 0.85);
                font-size: 12px;
                font-weight: 500;
                letter-spacing: 0.3px;
            }}
        """)
        
    def update_value(self, new_value: str):
        """تحديث القيمة"""
        self.value_label.setText(new_value)
        
    def _darken_color(self, color: str, amount: int = 15) -> str:
        """تغميق اللون"""
        color = color.lstrip('#')
        r = max(0, int(color[0:2], 16) - amount)
        g = max(0, int(color[2:4], 16) - amount)
        b = max(0, int(color[4:6], 16) - amount)
        return f"#{r:02x}{g:02x}{b:02x}"


class DepartmentDistributionChart(QWidget):
    """رسم بياني محسن لتوزيع الموظفين على الأقسام"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(600, 400)  # حجم أكبر للوضوح
        self.departments_data = []
        self.setStyleSheet("""
            QWidget {
                background-color: #fafbfc;
                border: 3px solid #dae1e7;
                border-radius: 15px;
                margin: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }
        """)

    def update_data(self, departments_data: List[Tuple[str, int]]):
        """تحديث بيانات الأقسام"""
        self.departments_data = departments_data
        print(f"📊 تحديث رسم توزيع الأقسام: {departments_data}")
        self.update()

    def paintEvent(self, event):
        """رسم المخطط المحسن"""
        from PySide6.QtGui import QPainter, QPen, QBrush, QColor, QLinearGradient
        from PySide6.QtCore import QRect

        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        rect = self.rect().adjusted(10, 10, -10, -10)

        # إزالة العنوان المكرر - العنوان موجود في القسم أعلاه

        if not self.departments_data:
            # رسم رسالة "لا توجد بيانات"
            painter.setPen(QPen(QColor(149, 165, 166)))
            no_data_font = QFont("Arial", 16, QFont.Normal)
            painter.setFont(no_data_font)
            no_data_rect = QRect(rect.x(), rect.y() + 20, rect.width(), rect.height() - 40)
            painter.drawText(no_data_rect, Qt.AlignCenter, "لا توجد بيانات لعرضها")
            return

        # منطقة الرسم المحسنة - بدء من الأعلى مباشرة
        chart_rect = rect.adjusted(50, 30, -50, -60)  # تقليل الهامش العلوي

        if len(self.departments_data) == 0:
            return

        # حساب أبعاد الأعمدة المحسنة
        available_width = chart_rect.width() - 40  # مساحة للهوامش الجانبية
        bar_spacing = 15  # مسافة بين الأعمدة
        total_spacing = (len(self.departments_data) - 1) * bar_spacing
        bar_width = max(50, (available_width - total_spacing) // len(self.departments_data))

        # توسيط الأعمدة
        total_bars_width = len(self.departments_data) * bar_width + total_spacing
        start_x = chart_rect.x() + (chart_rect.width() - total_bars_width) // 2

        max_value = max(count for _, count in self.departments_data) if self.departments_data else 1

        # ألوان متدرجة ومتناسقة
        colors = [
            "#3498db",  # أزرق
            "#27ae60",  # أخضر
            "#f39c12",  # برتقالي
            "#e74c3c",  # أحمر
            "#9b59b6",  # بنفسجي
            "#1abc9c",  # تركوازي
            "#34495e",  # رمادي داكن
            "#f1c40f"   # أصفر
        ]

        for i, (dept_name, count) in enumerate(self.departments_data):
            # حساب موقع وحجم العمود المحسن
            bar_height = max(15, int((count / max_value) * chart_rect.height())) if max_value > 0 else 15
            bar_x = start_x + i * (bar_width + bar_spacing)
            bar_y = chart_rect.bottom() - bar_height

            # لون العمود مع تدرج
            base_color = QColor(colors[i % len(colors)])

            # إنشاء تدرج لوني
            gradient = QLinearGradient(0, bar_y, 0, bar_y + bar_height)
            gradient.setColorAt(0, base_color.lighter(120))
            gradient.setColorAt(1, base_color)

            # رسم العمود
            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(base_color.darker(130), 2))
            painter.drawRoundedRect(bar_x, bar_y, bar_width, bar_height, 6, 6)

            # رسم القيمة فوق العمود مع تحسين التنسيق
            painter.setPen(QPen(QColor(44, 62, 80)))
            value_font = QFont("Arial", 14, QFont.Bold)  # خط أكبر للوضوح
            painter.setFont(value_font)
            value_rect = QRect(bar_x - 15, bar_y - 30, bar_width + 30, 25)
            painter.drawText(value_rect, Qt.AlignCenter, str(count))

            # رسم اسم القسم مع تحسين التنسيق
            painter.setPen(QPen(QColor(52, 73, 94)))
            label_font = QFont("Arial", 11, QFont.Bold)  # خط أكبر وعريض
            painter.setFont(label_font)

            # تقصير النص إذا كان طويلاً مع تحسين
            display_name = dept_name
            if len(dept_name) > 12:
                display_name = dept_name[:10] + ".."

            label_rect = QRect(bar_x - 20, chart_rect.bottom() + 8, bar_width + 40, 30)
            painter.drawText(label_rect, Qt.AlignCenter, display_name)

            # رسم نسبة مئوية تحت اسم القسم مع تحسين
            total_employees = sum(c for _, c in self.departments_data)
            percentage = (count / total_employees * 100) if total_employees > 0 else 0
            painter.setPen(QPen(QColor(149, 165, 166)))
            percentage_font = QFont("Arial", 10, QFont.Normal)
            painter.setFont(percentage_font)
            percentage_rect = QRect(bar_x - 20, chart_rect.bottom() + 35, bar_width + 40, 25)
            painter.drawText(percentage_rect, Qt.AlignCenter, f"({percentage:.1f}%)")

        # رسم خط الأساس
        painter.setPen(QPen(QColor(189, 195, 199), 2))
        painter.drawLine(chart_rect.bottomLeft(), chart_rect.bottomRight())

        # رسم مقياس على الجانب الأيسر
        painter.setPen(QPen(QColor(149, 165, 166)))
        scale_font = QFont("Arial", 9, QFont.Normal)
        painter.setFont(scale_font)

        # رسم خطوط المقياس
        for i in range(0, max_value + 1, max(1, max_value // 5)):
            y_pos = chart_rect.bottom() - int((i / max_value) * chart_rect.height()) if max_value > 0 else chart_rect.bottom()
            painter.drawLine(chart_rect.left() - 5, y_pos, chart_rect.left(), y_pos)
            scale_rect = QRect(chart_rect.left() - 25, y_pos - 10, 20, 20)
            painter.drawText(scale_rect, Qt.AlignRight | Qt.AlignVCenter, str(i))


class EnhancedDashboard(QWidget):
    """لوحة التحكم المحسنة"""
    
    # إشارات للإجراءات السريعة
    add_employee_requested = Signal()
    add_transaction_requested = Signal()
    view_reports_requested = Signal()
    backup_requested = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("enhanced_dashboard")
        self.data_cards = {}
        self.setup_ui()
        self.setup_update_timer()
        
        # تحديث فوري
        QTimer.singleShot(1000, self.load_statistics)

        # إضافة بيانات تجريبية للاختبار إذا لم توجد بيانات
        QTimer.singleShot(2000, self.load_sample_data_if_empty)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(25, 25, 25, 25)
        main_layout.setSpacing(25)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # المحتوى الرئيسي
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(30)
        
        # العنوان الرئيسي
        self.create_main_header(content_layout)
        
        # البيانات التوضيحية
        self.create_data_cards_section(content_layout)
        
        # الرسوم البيانية
        self.create_charts_section(content_layout)
        
        # الإجراءات السريعة
        self.create_quick_actions_section(content_layout)
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        
    def create_main_header(self, layout: QVBoxLayout):
        """إنشاء العنوان الرئيسي"""
        header_frame = QFrame()
        header_frame.setObjectName("main_header_frame")
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(30, 25, 30, 25)
        header_layout.setSpacing(10)
        
        # العنوان الرئيسي
        main_title = QLabel("📊 لوحة التحكم الرئيسية")
        main_title.setObjectName("main_dashboard_title")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel#main_dashboard_title {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                letter-spacing: 2px;
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
                padding: 15px;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                          stop: 0 rgba(52, 152, 219, 0.1), 
                                          stop: 1 rgba(155, 89, 182, 0.1));
                border-radius: 12px;
                border: 2px solid rgba(52, 152, 219, 0.2);
            }
        """)
        header_layout.addWidget(main_title)
        
        # العنوان الفرعي
        subtitle = QLabel("نظام إدارة الموارد البشرية المتقدم")
        subtitle.setObjectName("dashboard_subtitle")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel#dashboard_subtitle {
                font-size: 16px;
                font-weight: 500;
                color: #7f8c8d;
                letter-spacing: 1px;
                margin-top: 5px;
            }
        """)
        header_layout.addWidget(subtitle)
        
        layout.addWidget(header_frame)

    def create_data_cards_section(self, layout: QVBoxLayout):
        """إنشاء قسم البطاقات التوضيحية"""
        # عنوان القسم
        section_title = QLabel("📈 البيانات التوضيحية الشاملة")
        section_title.setObjectName("section_title")
        section_title.setAlignment(Qt.AlignCenter)
        section_title.setStyleSheet("""
            QLabel#section_title {
                font-size: 20px;
                font-weight: bold;
                color: #34495e;
                margin: 15px 0;
                padding: 12px;
                letter-spacing: 1px;
            }
        """)
        layout.addWidget(section_title)

        # شبكة البطاقات
        cards_frame = QFrame()
        cards_layout = QGridLayout(cards_frame)
        cards_layout.setSpacing(20)
        cards_layout.setContentsMargins(20, 20, 20, 20)

        # إنشاء البطاقات
        self.data_cards["employees"] = DataCard(
            "عدد الموظفين", "0", "موظف نشط", "#3498db", "👥"
        )

        self.data_cards["total_salaries"] = DataCard(
            "إجمالي الرواتب المستحقة", "0 د.ع", "للشهر الحالي", "#27ae60", "💰"
        )

        self.data_cards["advances"] = DataCard(
            "إجمالي السلف", "0 د.ع", "غير مدفوعة", "#f39c12", "💳"
        )

        self.data_cards["debts"] = DataCard(
            "إجمالي الديون", "0 د.ع", "مستحقة", "#e74c3c", "📋"
        )

        self.data_cards["bonuses"] = DataCard(
            "إجمالي المكافآت", "0 د.ع", "هذا الشهر", "#9b59b6", "🎁"
        )

        self.data_cards["departments"] = DataCard(
            "الأقسام النشطة", "0", "قسم", "#1abc9c", "🏢"
        )

        # ترتيب البطاقات في الشبكة (3 أعمدة)
        cards = list(self.data_cards.values())
        for i, card in enumerate(cards):
            row = i // 3
            col = i % 3
            cards_layout.addWidget(card, row, col)

        layout.addWidget(cards_frame)

    def create_charts_section(self, layout: QVBoxLayout):
        """إنشاء قسم الرسوم البيانية المتوسط"""
        # عنوان القسم المحسن
        charts_title = QLabel("👥 توزيع الموظفين على الأقسام")
        charts_title.setObjectName("section_title")
        charts_title.setAlignment(Qt.AlignCenter)
        charts_title.setStyleSheet("""
            QLabel#section_title {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin: 15px 0;
                padding: 15px;
                letter-spacing: 1.5px;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                          stop: 0 rgba(52, 152, 219, 0.1),
                                          stop: 1 rgba(155, 89, 182, 0.1));
                border-radius: 10px;
                border: 2px solid rgba(52, 152, 219, 0.3);
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
        """)
        layout.addWidget(charts_title)

        # إطار الرسوم البيانية المتوسط
        charts_frame = QFrame()
        charts_frame.setObjectName("charts_frame")
        charts_frame.setStyleSheet("""
            QFrame#charts_frame {
                background-color: white;
                border: 2px solid #e9ecef;
                border-radius: 15px;
                margin: 10px;
                padding: 30px;
            }
        """)

        # تخطيط متوسط للرسم البياني
        charts_layout = QVBoxLayout(charts_frame)
        charts_layout.setSpacing(20)
        charts_layout.setContentsMargins(40, 30, 40, 30)

        # حاوية توسيط الرسم البياني
        chart_container = QHBoxLayout()
        chart_container.addStretch(1)  # مساحة مرنة يسار

        # رسم توزيع الموظفين على الأقسام
        self.dept_chart = DepartmentDistributionChart()
        chart_container.addWidget(self.dept_chart)

        chart_container.addStretch(1)  # مساحة مرنة يمين

        # إضافة الحاوية إلى التخطيط الرئيسي
        charts_layout.addLayout(chart_container)

        # إضافة وصف تحت الرسم البياني
        chart_description = QLabel("📈 عرض تفصيلي لتوزيع الموظفين النشطين على جميع الأقسام مع النسب المئوية والإحصائيات")
        chart_description.setAlignment(Qt.AlignCenter)
        chart_description.setStyleSheet("""
            QLabel {
                font-size: 13px;
                color: #5d6d7e;
                font-style: italic;
                font-weight: 500;
                margin-top: 12px;
                padding: 10px;
                background-color: rgba(248, 249, 250, 0.8);
                border-radius: 6px;
                border: 1px solid rgba(189, 195, 199, 0.3);
            }
        """)
        charts_layout.addWidget(chart_description)

        layout.addWidget(charts_frame)

    def create_quick_actions_section(self, layout: QVBoxLayout):
        """إنشاء قسم الإجراءات السريعة"""
        from PySide6.QtWidgets import QPushButton

        # عنوان القسم
        actions_title = QLabel("⚡ الإجراءات السريعة")
        actions_title.setObjectName("section_title")
        actions_title.setAlignment(Qt.AlignCenter)
        actions_title.setStyleSheet("""
            QLabel#section_title {
                font-size: 20px;
                font-weight: bold;
                color: #34495e;
                margin: 15px 0;
                padding: 12px;
                letter-spacing: 1px;
            }
        """)
        layout.addWidget(actions_title)

        # إطار الأزرار
        actions_frame = QFrame()
        actions_layout = QHBoxLayout(actions_frame)
        actions_layout.setSpacing(20)
        actions_layout.setContentsMargins(20, 20, 20, 20)

        # أزرار الإجراءات
        buttons_data = [
            ("👤 إضافة موظف", "#3498db", self.add_employee_requested.emit),
            ("💰 إضافة معاملة", "#27ae60", self.add_transaction_requested.emit),
            ("📊 عرض التقارير", "#f39c12", self.view_reports_requested.emit),
            ("💾 نسخ احتياطي", "#e74c3c", self.backup_requested.emit)
        ]

        for text, color, callback in buttons_data:
            btn = QPushButton(text)
            btn.setFixedSize(180, 50)
            btn.clicked.connect(callback)
            btn.setStyleSheet(f"""
                QPushButton {{
                    font-size: 14px;
                    font-weight: bold;
                    color: white;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 {color}, stop: 1 {self._darken_color(color)});
                    border: none;
                    border-radius: 8px;
                    padding: 12px;
                    letter-spacing: 0.5px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 {self._lighten_color(color)}, stop: 1 {color});
                }}
                QPushButton:pressed {{
                    background: {self._darken_color(color, 20)};
                }}
            """)
            actions_layout.addWidget(btn)

        actions_layout.addStretch()
        layout.addWidget(actions_frame)

    def setup_update_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.load_statistics)
        self.update_timer.start(30000)  # تحديث كل 30 ثانية

    def load_statistics(self):
        """تحميل الإحصائيات من قاعدة البيانات"""
        try:
            with get_db_session_context() as session:
                print("🔄 تحديث بيانات لوحة التحكم المحسنة...")

                # عدد الموظفين النشطين
                employees_count = session.query(Employee).filter_by(is_active=True).count()
                self.data_cards["employees"].update_value(str(employees_count))

                # إجمالي الرواتب المستحقة
                total_salaries = session.query(Employee).filter_by(is_active=True).with_entities(
                    Employee.basic_salary
                ).all()
                monthly_total = sum(float(salary[0]) for salary in total_salaries if salary[0])
                self.data_cards["total_salaries"].update_value(format_currency(monthly_total))

                # إجمالي السلف
                advances = session.query(FinancialTransaction).filter(
                    FinancialTransaction.transaction_type == "ADVANCE",
                    FinancialTransaction.payment_status.in_(["PENDING", "PARTIALLY_PAID"]),
                    FinancialTransaction.is_active == True
                ).all()
                total_advances = sum(float(t.amount) - float(t.paid_amount) for t in advances)
                self.data_cards["advances"].update_value(format_currency(total_advances))

                # إجمالي الديون
                debts = session.query(FinancialTransaction).filter(
                    FinancialTransaction.transaction_type == "MARKET_DEBT",
                    FinancialTransaction.payment_status.in_(["PENDING", "PARTIALLY_PAID"]),
                    FinancialTransaction.is_active == True
                ).all()
                total_debts = sum(float(t.amount) - float(t.paid_amount) for t in debts)
                self.data_cards["debts"].update_value(format_currency(total_debts))

                # إجمالي المكافآت
                bonuses = session.query(FinancialTransaction).filter(
                    FinancialTransaction.transaction_type == "BONUS",
                    FinancialTransaction.is_active == True
                ).all()
                total_bonuses = sum(float(t.amount) for t in bonuses)
                self.data_cards["bonuses"].update_value(format_currency(total_bonuses))

                # عدد الأقسام النشطة
                departments_count = session.query(Department).filter_by(is_active=True).count()
                self.data_cards["departments"].update_value(str(departments_count))

                # توزيع الموظفين على الأقسام
                departments_data = []
                departments = session.query(Department).filter_by(is_active=True).all()

                print(f"🔍 عدد الأقسام النشطة: {len(departments)}")

                for dept in departments:
                    emp_count = session.query(Employee).filter(
                        Employee.department_id == dept.id,
                        Employee.is_active == True
                    ).count()

                    print(f"📋 القسم: {dept.name} - عدد الموظفين: {emp_count}")

                    # إضافة جميع الأقسام حتى لو كان عدد الموظفين صفر
                    departments_data.append((dept.name, emp_count))

                # ترتيب الأقسام حسب عدد الموظفين (تنازلي)
                departments_data.sort(key=lambda x: x[1], reverse=True)

                print(f"📊 بيانات الأقسام النهائية: {departments_data}")

                # تحديث الرسم البياني
                if hasattr(self, 'dept_chart'):
                    self.dept_chart.update_data(departments_data)
                else:
                    print("⚠️ مكون الرسم البياني غير موجود")

                print(f"✅ تم تحديث البيانات - موظفين: {employees_count}, أقسام: {departments_count}")

        except Exception as e:
            print(f"❌ خطأ في تحميل الإحصائيات: {e}")
            import traceback
            traceback.print_exc()

    def refresh_statistics(self):
        """تحديث الإحصائيات يدوياً"""
        self.load_statistics()

    def load_sample_data_if_empty(self):
        """تحميل بيانات تجريبية إذا لم توجد بيانات"""
        try:
            # التحقق من وجود بيانات
            if not hasattr(self, 'dept_chart') or not self.dept_chart.departments_data:
                print("📊 تحميل بيانات تجريبية لتوزيع الأقسام...")

                # بيانات تجريبية للأقسام
                sample_departments = [
                    ("الإدارة العامة", 8),
                    ("المحاسبة", 5),
                    ("الموارد البشرية", 3),
                    ("التسويق", 6),
                    ("المبيعات", 12),
                    ("تقنية المعلومات", 4)
                ]

                if hasattr(self, 'dept_chart'):
                    self.dept_chart.update_data(sample_departments)
                    print("✅ تم تحميل البيانات التجريبية")

        except Exception as e:
            print(f"⚠️ خطأ في تحميل البيانات التجريبية: {e}")

    def _lighten_color(self, color: str, amount: int = 20) -> str:
        """تفتيح لون"""
        color = color.lstrip('#')
        r = min(255, int(color[0:2], 16) + amount)
        g = min(255, int(color[2:4], 16) + amount)
        b = min(255, int(color[4:6], 16) + amount)
        return f"#{r:02x}{g:02x}{b:02x}"

    def _darken_color(self, color: str, amount: int = 15) -> str:
        """تغميق لون"""
        color = color.lstrip('#')
        r = max(0, int(color[0:2], 16) - amount)
        g = max(0, int(color[2:4], 16) - amount)
        b = max(0, int(color[4:6], 16) - amount)
        return f"#{r:02x}{g:02x}{b:02x}"
