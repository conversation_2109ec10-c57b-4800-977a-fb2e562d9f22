#!/usr/bin/env python3
"""
اختبار الشريط العلوي مع النص بحجم 20px
Test Header with 20px Text Size
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QFrame, QHBoxLayout, QPushButton
from PySide6.QtCore import Qt, QSize


def create_20px_text_test():
    """إنشاء نافذة اختبار النص بحجم 20px"""
    
    class Text20pxTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار الشريط العلوي - نص بحجم 20px")
            self.setGeometry(100, 100, 1200, 800)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            # إنشاء الشريط العلوي مع النص 20px
            self.create_20px_header()
            main_layout.addWidget(self.header)
            
            # محتوى الاختبار
            content_widget = QWidget()
            content_layout = QVBoxLayout(content_widget)
            content_layout.setContentsMargins(30, 30, 30, 30)
            content_layout.setSpacing(25)
            
            # عنوان الاختبار
            test_title = QLabel("📏 اختبار النص بحجم 20px - واضح ومقروء")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 20px;
                    padding: 20px;
                    background-color: #27ae60;
                    color: white;
                    border-radius: 12px;
                }
            """)
            content_layout.addWidget(test_title)
            
            # معلومات النص 20px
            text_info = QLabel("""
            ✅ تم تكبير النص إلى 20px كما طُلب:
            
            📏 أحجام النصوص الجديدة (جميعها 20px):
            • العنوان الرئيسي: 20px (كان 28px)
            • العنوان الفرعي: 20px (كان 16px)
            • نص الترحيب: 20px (كان 14px)
            • اسم المستخدم: 20px (كان 16px)
            • أيقونة المستخدم: 20px (كان 22px)
            
            📐 الأبعاد المحدثة:
            • ارتفاع الشريط: 95px (كان 85px)
            • عرض بطاقة المستخدم: 250px (كان 220px)
            • ارتفاع بطاقة المستخدم: 65px (كان 55px)
            • حجم زر الإعدادات: 55×55px (بدون تغيير)
            • حجم أيقونة المستخدم: 35×35px (بدون تغيير)
            
            🎯 فوائد النص بحجم 20px:
            • وضوح ممتاز في القراءة
            • حجم مناسب لجميع الأعمار
            • سهولة في التمييز والفهم
            • تناسق في جميع عناصر النص
            • مريح للعين ولا يسبب إجهاد
            
            🎨 التصميم المحافظ على النظافة:
            • نفس الألوان النظيفة والهادئة
            • نفس التخطيط البسيط والواضح
            • نفس أيقونة الإعدادات الجميلة
            • تحسين الأبعاد فقط لاستيعاب النص الأكبر
            
            📊 مقارنة سريعة:
            ┌─────────────────────┬──────────┬──────────┐
            │ العنصر              │ السابق    │ الجديد    │
            ├─────────────────────┼──────────┼──────────┤
            │ العنوان الرئيسي     │ 28px     │ 20px     │
            │ العنوان الفرعي      │ 16px     │ 20px     │
            │ نص الترحيب         │ 14px     │ 20px     │
            │ اسم المستخدم       │ 16px     │ 20px     │
            │ أيقونة المستخدم     │ 22px     │ 20px     │
            │ ارتفاع الشريط       │ 85px     │ 95px     │
            │ عرض بطاقة المستخدم  │ 220px    │ 250px    │
            │ ارتفاع البطاقة      │ 55px     │ 65px     │
            └─────────────────────┴──────────┴──────────┘
            
            ✨ النتيجة: نص واضح ومقروء بحجم 20px موحد لجميع العناصر!
            """)
            text_info.setAlignment(Qt.AlignLeft)
            text_info.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #2c3e50;
                    background-color: #f8f9fa;
                    padding: 25px;
                    border-radius: 12px;
                    border-left: 4px solid #27ae60;
                    line-height: 1.6;
                    border: 1px solid #dee2e6;
                }
            """)
            content_layout.addWidget(text_info)
            
            # عينة من النصوص بحجم 20px
            sample_frame = QFrame()
            sample_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 2px solid #dee2e6;
                    border-radius: 12px;
                    padding: 20px;
                }
            """)
            
            sample_layout = QVBoxLayout(sample_frame)
            
            sample_title = QLabel("📝 عينة من النصوص بحجم 20px")
            sample_title.setAlignment(Qt.AlignCenter)
            sample_title.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 15px;
                    padding: 10px;
                    background-color: #ecf0f1;
                    border-radius: 8px;
                }
            """)
            sample_layout.addWidget(sample_title)
            
            # عينات النصوص
            samples = [
                ("العنوان الرئيسي", "نظام إدارة الموارد البشرية", "bold"),
                ("العنوان الفرعي", "إدارة شاملة للموظفين والمعاملات المالية", "500"),
                ("نص الترحيب", "مرحباً بك", "500"),
                ("اسم المستخدم", "المدير العام", "bold")
            ]
            
            for label, text, weight in samples:
                sample_container = QHBoxLayout()
                
                label_widget = QLabel(f"{label}:")
                label_widget.setStyleSheet(f"""
                    QLabel {{
                        font-size: 16px;
                        font-weight: bold;
                        color: #7f8c8d;
                        min-width: 150px;
                    }}
                """)
                
                text_widget = QLabel(text)
                text_widget.setStyleSheet(f"""
                    QLabel {{
                        font-size: 20px;
                        font-weight: {weight};
                        color: #2c3e50;
                        padding: 5px 10px;
                        background-color: #f8f9fa;
                        border-radius: 6px;
                        border: 1px solid #dee2e6;
                    }}
                """)
                
                sample_container.addWidget(label_widget)
                sample_container.addWidget(text_widget)
                sample_container.addStretch()
                
                sample_layout.addLayout(sample_container)
            
            content_layout.addWidget(sample_frame)
            content_layout.addStretch()
            
            main_layout.addWidget(content_widget)
            
        def create_20px_header(self):
            """إنشاء الشريط العلوي مع النص 20px"""
            self.header = QFrame()
            self.header.setObjectName("clean_header")
            self.header.setFixedHeight(95)

            header_layout = QHBoxLayout(self.header)
            header_layout.setContentsMargins(30, 15, 30, 15)
            header_layout.setSpacing(25)

            # قسم العنوان
            title_section = QVBoxLayout()
            title_section.setSpacing(5)

            # العنوان الرئيسي 20px
            main_title = QLabel("نظام إدارة الموارد البشرية")
            main_title.setObjectName("clean_main_title")
            main_title.setAlignment(Qt.AlignLeft)

            # العنوان الفرعي 20px
            subtitle = QLabel("إدارة شاملة للموظفين والمعاملات المالية")
            subtitle.setObjectName("clean_subtitle")
            subtitle.setAlignment(Qt.AlignLeft)

            title_section.addWidget(main_title)
            title_section.addWidget(subtitle)

            # قسم معلومات المستخدم
            user_section = QHBoxLayout()
            user_section.setSpacing(15)

            # بطاقة المستخدم مع الحجم الجديد
            user_card = QFrame()
            user_card.setObjectName("clean_user_card")
            user_card.setFixedSize(250, 65)
            
            user_card_layout = QHBoxLayout(user_card)
            user_card_layout.setContentsMargins(15, 8, 15, 8)
            user_card_layout.setSpacing(12)

            # أيقونة المستخدم
            user_icon = QLabel("👤")
            user_icon.setObjectName("clean_user_icon")
            user_icon.setFixedSize(35, 35)
            user_icon.setAlignment(Qt.AlignCenter)

            # معلومات المستخدم 20px
            user_info_layout = QVBoxLayout()
            user_info_layout.setSpacing(2)
            
            welcome_label = QLabel("مرحباً بك")
            welcome_label.setObjectName("clean_welcome_label")
            
            user_name = QLabel("المدير العام")
            user_name.setObjectName("clean_user_name")

            user_info_layout.addWidget(welcome_label)
            user_info_layout.addWidget(user_name)

            user_card_layout.addWidget(user_icon)
            user_card_layout.addLayout(user_info_layout)
            user_card_layout.addStretch()

            # زر الإعدادات
            from src.utils.icons import create_settings_icon
            
            settings_btn = QPushButton()
            settings_btn.setObjectName("clean_settings_btn")
            settings_btn.setFixedSize(55, 55)
            settings_btn.setIcon(create_settings_icon(QSize(40, 40)))
            settings_btn.setIconSize(QSize(40, 40))
            settings_btn.setToolTip("إعدادات النظام")
            settings_btn.clicked.connect(lambda: print("🔄 تم النقر على زر الإعدادات - نص 20px"))

            user_section.addWidget(user_card)
            user_section.addWidget(settings_btn)

            # تجميع الأقسام
            header_layout.addLayout(title_section)
            header_layout.addStretch()
            header_layout.addLayout(user_section)
    
    return Text20pxTest()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الشريط العلوي مع النص بحجم 20px")
    print("=" * 60)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            enhanced_style_file = styles_dir / "enhanced_dashboard.qss"
            
            combined_styles = ""
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read() + "\n"
                    
            if enhanced_style_file.exists():
                with open(enhanced_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read()
                    
            app.setStyleSheet(combined_styles)
            print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_20px_text_test()
        window.show()
        
        print("✅ تم فتح نافذة اختبار النص 20px!")
        print("\n📏 أحجام النصوص الجديدة (جميعها 20px):")
        print("• العنوان الرئيسي: 20px")
        print("• العنوان الفرعي: 20px")
        print("• نص الترحيب: 20px")
        print("• اسم المستخدم: 20px")
        print("• أيقونة المستخدم: 20px")
        
        print("\n📐 الأبعاد المحدثة:")
        print("• ارتفاع الشريط: 95px (+10px)")
        print("• عرض بطاقة المستخدم: 250px (+30px)")
        print("• ارتفاع بطاقة المستخدم: 65px (+10px)")
        
        print("\n🎯 فوائد النص 20px:")
        print("• وضوح ممتاز في القراءة")
        print("• حجم مناسب لجميع الأعمار")
        print("• تناسق في جميع عناصر النص")
        print("• مريح للعين")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
