"""
نظام البحث والتصفية المحسن
Enhanced Search and Filter System
"""

import re
from datetime import datetime, date
from typing import Any, List, Dict, Optional, Callable, Union
from enum import Enum


class FilterOperator(Enum):
    """عوامل التصفية"""
    EQUALS = "equals"
    CONTAINS = "contains"
    STARTS_WITH = "starts_with"
    ENDS_WITH = "ends_with"
    GREATER_THAN = "greater_than"
    LESS_THAN = "less_than"
    GREATER_EQUAL = "greater_equal"
    LESS_EQUAL = "less_equal"
    BETWEEN = "between"
    IN = "in"
    NOT_IN = "not_in"
    IS_NULL = "is_null"
    IS_NOT_NULL = "is_not_null"


class SearchFilter:
    """فئة التصفية"""
    
    def __init__(self, field: str, operator: FilterOperator, value: Any = None, value2: Any = None):
        self.field = field
        self.operator = operator
        self.value = value
        self.value2 = value2  # للعمليات مثل BETWEEN
    
    def apply(self, item: Dict[str, Any]) -> bool:
        """تطبيق التصفية على عنصر"""
        field_value = item.get(self.field)
        
        if self.operator == FilterOperator.EQUALS:
            return field_value == self.value
        
        elif self.operator == FilterOperator.CONTAINS:
            if field_value is None:
                return False
            return str(self.value).lower() in str(field_value).lower()
        
        elif self.operator == FilterOperator.STARTS_WITH:
            if field_value is None:
                return False
            return str(field_value).lower().startswith(str(self.value).lower())
        
        elif self.operator == FilterOperator.ENDS_WITH:
            if field_value is None:
                return False
            return str(field_value).lower().endswith(str(self.value).lower())
        
        elif self.operator == FilterOperator.GREATER_THAN:
            if field_value is None:
                return False
            try:
                return float(field_value) > float(self.value)
            except (ValueError, TypeError):
                return str(field_value) > str(self.value)
        
        elif self.operator == FilterOperator.LESS_THAN:
            if field_value is None:
                return False
            try:
                return float(field_value) < float(self.value)
            except (ValueError, TypeError):
                return str(field_value) < str(self.value)
        
        elif self.operator == FilterOperator.GREATER_EQUAL:
            if field_value is None:
                return False
            try:
                return float(field_value) >= float(self.value)
            except (ValueError, TypeError):
                return str(field_value) >= str(self.value)
        
        elif self.operator == FilterOperator.LESS_EQUAL:
            if field_value is None:
                return False
            try:
                return float(field_value) <= float(self.value)
            except (ValueError, TypeError):
                return str(field_value) <= str(self.value)
        
        elif self.operator == FilterOperator.BETWEEN:
            if field_value is None:
                return False
            try:
                val = float(field_value)
                return float(self.value) <= val <= float(self.value2)
            except (ValueError, TypeError):
                return str(self.value) <= str(field_value) <= str(self.value2)
        
        elif self.operator == FilterOperator.IN:
            return field_value in self.value if isinstance(self.value, (list, tuple)) else field_value == self.value
        
        elif self.operator == FilterOperator.NOT_IN:
            return field_value not in self.value if isinstance(self.value, (list, tuple)) else field_value != self.value
        
        elif self.operator == FilterOperator.IS_NULL:
            return field_value is None or field_value == ""
        
        elif self.operator == FilterOperator.IS_NOT_NULL:
            return field_value is not None and field_value != ""
        
        return True


class SearchEngine:
    """محرك البحث المحسن"""
    
    def __init__(self):
        self.filters: List[SearchFilter] = []
        self.search_fields: List[str] = []
        self.search_text: str = ""
        self.case_sensitive: bool = False
        self.use_regex: bool = False
    
    def set_search_text(self, text: str, fields: List[str] = None, case_sensitive: bool = False, use_regex: bool = False):
        """تعيين نص البحث"""
        self.search_text = text
        self.search_fields = fields or []
        self.case_sensitive = case_sensitive
        self.use_regex = use_regex
    
    def add_filter(self, field: str, operator: FilterOperator, value: Any = None, value2: Any = None):
        """إضافة تصفية"""
        filter_obj = SearchFilter(field, operator, value, value2)
        self.filters.append(filter_obj)
    
    def clear_filters(self):
        """مسح جميع التصفيات"""
        self.filters.clear()
        self.search_text = ""
        self.search_fields.clear()
    
    def remove_filter(self, field: str):
        """إزالة تصفية حقل معين"""
        self.filters = [f for f in self.filters if f.field != field]
    
    def search(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """تطبيق البحث والتصفية"""
        results = data.copy()
        
        # تطبيق البحث النصي
        if self.search_text and self.search_fields:
            results = self._apply_text_search(results)
        
        # تطبيق التصفيات
        for filter_obj in self.filters:
            results = [item for item in results if filter_obj.apply(item)]
        
        return results
    
    def _apply_text_search(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """تطبيق البحث النصي"""
        if not self.search_text:
            return data
        
        results = []
        search_text = self.search_text if self.case_sensitive else self.search_text.lower()
        
        for item in data:
            match_found = False
            
            for field in self.search_fields:
                field_value = item.get(field, "")
                if field_value is None:
                    continue
                
                field_str = str(field_value) if self.case_sensitive else str(field_value).lower()
                
                if self.use_regex:
                    try:
                        if re.search(search_text, field_str):
                            match_found = True
                            break
                    except re.error:
                        # في حالة خطأ في التعبير النمطي، استخدم البحث العادي
                        if search_text in field_str:
                            match_found = True
                            break
                else:
                    if search_text in field_str:
                        match_found = True
                        break
            
            if match_found:
                results.append(item)
        
        return results


class AdvancedSearchBuilder:
    """بناء البحث المتقدم"""
    
    def __init__(self):
        self.engine = SearchEngine()
    
    def text_search(self, text: str, fields: List[str], case_sensitive: bool = False, use_regex: bool = False):
        """بحث نصي"""
        self.engine.set_search_text(text, fields, case_sensitive, use_regex)
        return self
    
    def equals(self, field: str, value: Any):
        """تساوي"""
        self.engine.add_filter(field, FilterOperator.EQUALS, value)
        return self
    
    def contains(self, field: str, value: str):
        """يحتوي على"""
        self.engine.add_filter(field, FilterOperator.CONTAINS, value)
        return self
    
    def starts_with(self, field: str, value: str):
        """يبدأ بـ"""
        self.engine.add_filter(field, FilterOperator.STARTS_WITH, value)
        return self
    
    def ends_with(self, field: str, value: str):
        """ينتهي بـ"""
        self.engine.add_filter(field, FilterOperator.ENDS_WITH, value)
        return self
    
    def greater_than(self, field: str, value: Union[int, float]):
        """أكبر من"""
        self.engine.add_filter(field, FilterOperator.GREATER_THAN, value)
        return self
    
    def less_than(self, field: str, value: Union[int, float]):
        """أقل من"""
        self.engine.add_filter(field, FilterOperator.LESS_THAN, value)
        return self
    
    def between(self, field: str, min_value: Union[int, float], max_value: Union[int, float]):
        """بين قيمتين"""
        self.engine.add_filter(field, FilterOperator.BETWEEN, min_value, max_value)
        return self
    
    def in_list(self, field: str, values: List[Any]):
        """في القائمة"""
        self.engine.add_filter(field, FilterOperator.IN, values)
        return self
    
    def not_in_list(self, field: str, values: List[Any]):
        """ليس في القائمة"""
        self.engine.add_filter(field, FilterOperator.NOT_IN, values)
        return self
    
    def is_null(self, field: str):
        """فارغ"""
        self.engine.add_filter(field, FilterOperator.IS_NULL)
        return self
    
    def is_not_null(self, field: str):
        """غير فارغ"""
        self.engine.add_filter(field, FilterOperator.IS_NOT_NULL)
        return self
    
    def date_range(self, field: str, start_date: date, end_date: date):
        """نطاق تاريخ"""
        self.engine.add_filter(field, FilterOperator.BETWEEN, start_date, end_date)
        return self
    
    def execute(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """تنفيذ البحث"""
        return self.engine.search(data)
    
    def clear(self):
        """مسح البحث"""
        self.engine.clear_filters()
        return self


def create_search_builder() -> AdvancedSearchBuilder:
    """إنشاء بناء البحث"""
    return AdvancedSearchBuilder()


def quick_search(data: List[Dict[str, Any]], search_text: str, fields: List[str]) -> List[Dict[str, Any]]:
    """بحث سريع"""
    return (create_search_builder()
            .text_search(search_text, fields)
            .execute(data))


def filter_by_field(data: List[Dict[str, Any]], field: str, value: Any) -> List[Dict[str, Any]]:
    """تصفية بحقل واحد"""
    return (create_search_builder()
            .equals(field, value)
            .execute(data))


def filter_by_date_range(data: List[Dict[str, Any]], field: str, start_date: date, end_date: date) -> List[Dict[str, Any]]:
    """تصفية بنطاق تاريخ"""
    return (create_search_builder()
            .date_range(field, start_date, end_date)
            .execute(data))


def filter_by_numeric_range(data: List[Dict[str, Any]], field: str, min_value: float, max_value: float) -> List[Dict[str, Any]]:
    """تصفية بنطاق رقمي"""
    return (create_search_builder()
            .between(field, min_value, max_value)
            .execute(data))
