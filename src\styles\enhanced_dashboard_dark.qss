/*
🌙 أنماط لوحة التحكم المحسنة للوضع الليلي
Enhanced Dashboard Dark Mode Styles
تصميم متطور ومتوازن للوحة التحكم في الوضع الليلي
*/

/* متغيرات الألوان المتخصصة للوحة التحكم */
:root {
    /* ألوان خاصة بلوحة التحكم */
    --dashboard-bg-primary: #0f0f23;
    --dashboard-bg-secondary: #1a1a2e;
    --dashboard-bg-card: #252545;
    --dashboard-bg-elevated: #2d2d52;
    
    /* ألوان البطاقات الإحصائية */
    --card-employees: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4299e1, stop:1 #2b77cb);
    --card-salaries: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #48bb78, stop:1 #2f855a);
    --card-advances: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ed8936, stop:1 #c05621);
    --card-debts: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #f56565, stop:1 #c53030);
    --card-bonuses: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #9f7aea, stop:1 #6b46c1);
    --card-departments: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4fd1c7, stop:1 #319795);
}

/* الإطار الرئيسي للوحة التحكم */
QWidget#enhanced_dashboard {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 var(--dashboard-bg-primary), 
        stop:0.5 var(--dashboard-bg-secondary), 
        stop:1 var(--dashboard-bg-primary));
    border: none;
    border-radius: 16px;
    padding: 20px;
}

/* إطار العنوان الرئيسي المتطور */
QFrame#main_header_frame {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #667eea, 
        stop:0.5 #764ba2, 
        stop:1 #667eea);
    border: 2px solid rgba(102, 126, 234, 0.3);
    border-radius: 20px;
    margin: 12px;
    padding: 24px;
    box-shadow: 0px 12px 40px rgba(102, 126, 234, 0.2);
}

/* العنوان الرئيسي المحسن */
QLabel#main_dashboard_title {
    font-size: 28px;
    font-weight: 800;
    color: #ffffff;
    letter-spacing: 1.5px;
    text-shadow: 0px 4px 12px rgba(0, 0, 0, 0.4);
    padding: 16px;
    background: transparent;
    border: none;
    text-align: center;
}

/* العنوان الفرعي المحسن */
QLabel#dashboard_subtitle {
    font-size: 18px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    letter-spacing: 0.8px;
    margin-top: 8px;
    text-shadow: 0px 2px 6px rgba(0, 0, 0, 0.3);
}

/* عناوين الأقسام المتطورة */
QLabel#section_title {
    font-size: 22px;
    font-weight: 700;
    color: #e2e8f0;
    margin: 20px 0px 16px 0px;
    padding: 16px 20px;
    letter-spacing: 0.8px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(102, 126, 234, 0.2), 
        stop:1 rgba(118, 75, 162, 0.2));
    border-radius: 12px;
    border-left: 5px solid #667eea;
    box-shadow: 0px 4px 16px rgba(102, 126, 234, 0.15);
}

/* بطاقات البيانات المحسنة */
QFrame#data_card {
    border-radius: 18px;
    margin: 8px;
    padding: 24px;
    box-shadow: 0px 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

QFrame#data_card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0px 16px 48px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* عناوين البطاقات المحسنة */
QLabel#card_title {
    color: rgba(255, 255, 255, 0.95);
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 0.5px;
    text-shadow: 0px 2px 6px rgba(0, 0, 0, 0.4);
    margin-bottom: 12px;
}

/* قيم البطاقات المتطورة */
QLabel#card_value {
    color: #ffffff;
    font-size: 36px;
    font-weight: 900;
    letter-spacing: 1px;
    text-shadow: 0px 4px 12px rgba(0, 0, 0, 0.5);
    margin: 16px 0px;
    text-align: center;
}

/* عناوين فرعية للبطاقات */
QLabel#card_subtitle {
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.3px;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3);
    margin-top: 8px;
}

/* إطار الرسوم البيانية المحسن */
QFrame#charts_frame {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 var(--dashboard-bg-card), 
        stop:1 var(--dashboard-bg-elevated));
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 20px;
    margin: 16px;
    padding: 28px;
    box-shadow: 0px 12px 40px rgba(0, 0, 0, 0.2);
}

/* أزرار الإجراءات السريعة المحسنة */
QPushButton#dashboard_action_button {
    font-size: 16px;
    font-weight: 700;
    color: #ffffff;
    border: none;
    border-radius: 12px;
    padding: 16px 24px;
    letter-spacing: 0.5px;
    text-shadow: 0px 2px 6px rgba(0, 0, 0, 0.3);
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #667eea, 
        stop:1 #764ba2);
    box-shadow: 0px 6px 20px rgba(102, 126, 234, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 140px;
}

QPushButton#dashboard_action_button:hover {
    transform: translateY(-3px);
    box-shadow: 0px 12px 32px rgba(102, 126, 234, 0.4);
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(102, 126, 234, 0.9), 
        stop:1 rgba(118, 75, 162, 0.9));
}

QPushButton#dashboard_action_button:pressed {
    transform: translateY(-1px) scale(0.98);
    box-shadow: 0px 6px 16px rgba(102, 126, 234, 0.3);
    transition: all 0.1s ease;
}

/* منطقة التمرير المحسنة */
QScrollArea {
    border: none;
    background-color: transparent;
    border-radius: 16px;
}

QScrollBar:vertical {
    background: var(--dashboard-bg-card);
    width: 16px;
    border-radius: 8px;
    margin: 0;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #667eea, 
        stop:1 #764ba2);
    border-radius: 7px;
    min-height: 30px;
    margin: 2px;
    box-shadow: 0px 2px 8px rgba(102, 126, 234, 0.3);
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(102, 126, 234, 0.9), 
        stop:1 rgba(118, 75, 162, 0.9));
    box-shadow: 0px 4px 12px rgba(102, 126, 234, 0.4);
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* ألوان مخصصة للبطاقات المحسنة */
QFrame#employees_card {
    background: var(--card-employees);
    border: 2px solid rgba(66, 153, 225, 0.4);
    box-shadow: 0px 8px 32px rgba(66, 153, 225, 0.25);
}

QFrame#salaries_card {
    background: var(--card-salaries);
    border: 2px solid rgba(72, 187, 120, 0.4);
    box-shadow: 0px 8px 32px rgba(72, 187, 120, 0.25);
}

QFrame#advances_card {
    background: var(--card-advances);
    border: 2px solid rgba(237, 137, 54, 0.4);
    box-shadow: 0px 8px 32px rgba(237, 137, 54, 0.25);
}

QFrame#debts_card {
    background: var(--card-debts);
    border: 2px solid rgba(245, 101, 101, 0.4);
    box-shadow: 0px 8px 32px rgba(245, 101, 101, 0.25);
}

QFrame#bonuses_card {
    background: var(--card-bonuses);
    border: 2px solid rgba(159, 122, 234, 0.4);
    box-shadow: 0px 8px 32px rgba(159, 122, 234, 0.25);
}

QFrame#departments_card {
    background: var(--card-departments);
    border: 2px solid rgba(79, 209, 199, 0.4);
    box-shadow: 0px 8px 32px rgba(79, 209, 199, 0.25);
}

/* تأثيرات خاصة للبطاقات عند التمرير */
QFrame#employees_card:hover {
    box-shadow: 0px 16px 48px rgba(66, 153, 225, 0.35);
}

QFrame#salaries_card:hover {
    box-shadow: 0px 16px 48px rgba(72, 187, 120, 0.35);
}

QFrame#advances_card:hover {
    box-shadow: 0px 16px 48px rgba(237, 137, 54, 0.35);
}

QFrame#debts_card:hover {
    box-shadow: 0px 16px 48px rgba(245, 101, 101, 0.35);
}

QFrame#bonuses_card:hover {
    box-shadow: 0px 16px 48px rgba(159, 122, 234, 0.35);
}

QFrame#departments_card:hover {
    box-shadow: 0px 16px 48px rgba(79, 209, 199, 0.35);
}

/* تحسينات إضافية للرسوم البيانية */
QFrame#chart_container {
    background: var(--dashboard-bg-elevated);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 16px;
    padding: 20px;
    margin: 8px;
    box-shadow: 0px 6px 24px rgba(0, 0, 0, 0.15);
}

QLabel#chart_title {
    color: #e2e8f0;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 0.3px;
    margin-bottom: 16px;
    text-align: center;
}
