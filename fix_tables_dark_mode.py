#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح تطبيق الوضع الليلي على الجداول
Fix Dark Mode Application on Tables
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))


def apply_dark_mode_to_tables():
    """تطبيق الوضع الليلي على جميع الجداول"""
    print("📊 تطبيق الوضع الليلي على الجداول...")
    
    try:
        # البحث عن التطبيق الجاري
        app = QApplication.instance()
        
        if not app:
            print("❌ لا يوجد تطبيق Qt جاري")
            return False
        
        # تطبيق الوضع الليلي أولاً
        from src.utils.theme_manager import ThemeManager
        theme_manager = ThemeManager()
        
        print("🌙 تطبيق الوضع الليلي...")
        success = theme_manager.set_theme("dark")
        
        if not success:
            print("❌ فشل في تطبيق الوضع الليلي")
            return False
        
        print("✅ تم تطبيق الوضع الليلي")
        
        # تطبيق الثيم على الجداول
        print("📊 تطبيق الثيم على الجداول...")
        tables_success = theme_manager.apply_theme_to_tables()
        
        if tables_success:
            print("✅ تم تطبيق الوضع الليلي على الجداول بنجاح")
        else:
            print("⚠️ لم يتم العثور على جداول أو فشل في التطبيق")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تطبيق الوضع الليلي على الجداول: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_tables_fixer():
    """إنشاء أداة إصلاح الجداول"""
    
    class TablesFixer(QWidget):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("📊 إصلاح الوضع الليلي للجداول")
            self.setGeometry(150, 150, 500, 400)
            self.setWindowFlags(Qt.WindowStaysOnTopHint)
            
            # إنشاء مدير الثيمات
            from src.utils.theme_manager import ThemeManager
            self.theme_manager = ThemeManager()
            
            self.setup_ui()
            
        def setup_ui(self):
            """إعداد واجهة الإصلاح"""
            layout = QVBoxLayout(self)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)
            
            # العنوان
            title = QLabel("📊 إصلاح الوضع الليلي للجداول")
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet("""
                font-size: 20px; 
                font-weight: bold; 
                color: #2c3e50; 
                margin: 15px;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
            """)
            layout.addWidget(title)
            
            # معلومات
            info_label = QLabel("""
🎯 هذه الأداة تقوم بـ:
• تطبيق الوضع الليلي على النظام
• إصلاح مشكلة عدم تطبيق الثيم على الجداول
• تحديث جميع الجداول الموجودة في النظام
• تطبيق الألوان المحسنة للجداول
            """)
            info_label.setStyleSheet("""
                font-size: 14px; 
                color: #34495e; 
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #3498db;
                line-height: 1.6;
            """)
            layout.addWidget(info_label)
            
            # حالة النظام
            self.status_label = QLabel("🔍 فحص حالة النظام...")
            self.status_label.setAlignment(Qt.AlignCenter)
            self.status_label.setStyleSheet("""
                font-size: 14px; 
                color: #7f8c8d; 
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 6px;
                border: 1px solid #dee2e6;
            """)
            layout.addWidget(self.status_label)
            
            # أزرار الإجراءات
            buttons_layout = QVBoxLayout()
            
            # زر تطبيق الوضع الليلي
            apply_dark_btn = QPushButton("🌙 تطبيق الوضع الليلي")
            apply_dark_btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px; font-weight: bold; color: white; 
                    background-color: #2c3e50; border: none; border-radius: 10px; 
                    padding: 15px 25px; margin: 8px;
                }
                QPushButton:hover { background-color: #34495e; }
                QPushButton:pressed { background-color: #1a252f; }
            """)
            apply_dark_btn.clicked.connect(self.apply_dark_mode)
            buttons_layout.addWidget(apply_dark_btn)
            
            # زر إصلاح الجداول
            fix_tables_btn = QPushButton("📊 إصلاح الجداول")
            fix_tables_btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px; font-weight: bold; color: white; 
                    background-color: #e74c3c; border: none; border-radius: 10px; 
                    padding: 15px 25px; margin: 8px;
                }
                QPushButton:hover { background-color: #c0392b; }
                QPushButton:pressed { background-color: #a93226; }
            """)
            fix_tables_btn.clicked.connect(self.fix_tables)
            buttons_layout.addWidget(fix_tables_btn)
            
            # زر إصلاح شامل
            full_fix_btn = QPushButton("🔧 إصلاح شامل")
            full_fix_btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px; font-weight: bold; color: white; 
                    background-color: #27ae60; border: none; border-radius: 10px; 
                    padding: 15px 25px; margin: 8px;
                }
                QPushButton:hover { background-color: #2ecc71; }
                QPushButton:pressed { background-color: #1e8449; }
            """)
            full_fix_btn.clicked.connect(self.full_fix)
            buttons_layout.addWidget(full_fix_btn)
            
            # زر فحص الجداول
            check_tables_btn = QPushButton("🔍 فحص الجداول")
            check_tables_btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px; font-weight: bold; color: white; 
                    background-color: #f39c12; border: none; border-radius: 10px; 
                    padding: 15px 25px; margin: 8px;
                }
                QPushButton:hover { background-color: #e67e22; }
                QPushButton:pressed { background-color: #d35400; }
            """)
            check_tables_btn.clicked.connect(self.check_tables)
            buttons_layout.addWidget(check_tables_btn)
            
            layout.addLayout(buttons_layout)
            
            # سجل الأحداث
            self.log_text = QTextEdit()
            self.log_text.setMaximumHeight(150)
            self.log_text.setStyleSheet("""
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 6px;
                padding: 8px;
            """)
            self.log_text.setPlainText("📝 سجل الأحداث:\n")
            layout.addWidget(self.log_text)
            
            # فحص أولي
            self.check_system_status()
            
        def log_message(self, message):
            """إضافة رسالة للسجل"""
            self.log_text.append(f"[{QTime.currentTime().toString()}] {message}")
            self.log_text.verticalScrollBar().setValue(
                self.log_text.verticalScrollBar().maximum()
            )
            
        def check_system_status(self):
            """فحص حالة النظام"""
            try:
                app = QApplication.instance()
                if app:
                    tables = app.findChildren(QTableWidget)
                    current_theme = self.theme_manager.get_current_theme()
                    
                    status = f"✅ النظام متصل | الثيم: {current_theme} | الجداول: {len(tables)}"
                    self.status_label.setText(status)
                    self.status_label.setStyleSheet("""
                        font-size: 14px; color: #27ae60; padding: 10px;
                        background-color: #d5f4e6; border-radius: 6px;
                    """)
                    
                    self.log_message(f"✅ تم فحص النظام: {len(tables)} جدول موجود")
                else:
                    self.status_label.setText("❌ النظام غير متصل")
                    self.status_label.setStyleSheet("""
                        font-size: 14px; color: #e74c3c; padding: 10px;
                        background-color: #fadbd8; border-radius: 6px;
                    """)
                    self.log_message("❌ لا يوجد تطبيق Qt جاري")
                    
            except Exception as e:
                self.log_message(f"❌ خطأ في فحص النظام: {e}")
        
        def apply_dark_mode(self):
            """تطبيق الوضع الليلي"""
            try:
                self.log_message("🌙 بدء تطبيق الوضع الليلي...")
                success = self.theme_manager.set_theme("dark")
                
                if success:
                    self.log_message("✅ تم تطبيق الوضع الليلي بنجاح")
                    self.status_label.setText("✅ تم تطبيق الوضع الليلي")
                else:
                    self.log_message("❌ فشل في تطبيق الوضع الليلي")
                    self.status_label.setText("❌ فشل في تطبيق الوضع الليلي")
                    
            except Exception as e:
                self.log_message(f"❌ خطأ في تطبيق الوضع الليلي: {e}")
        
        def fix_tables(self):
            """إصلاح الجداول"""
            try:
                self.log_message("📊 بدء إصلاح الجداول...")
                success = self.theme_manager.apply_theme_to_tables()
                
                if success:
                    self.log_message("✅ تم إصلاح الجداول بنجاح")
                    self.status_label.setText("✅ تم إصلاح الجداول")
                else:
                    self.log_message("⚠️ لم يتم العثور على جداول")
                    self.status_label.setText("⚠️ لا توجد جداول للإصلاح")
                    
            except Exception as e:
                self.log_message(f"❌ خطأ في إصلاح الجداول: {e}")
        
        def full_fix(self):
            """إصلاح شامل"""
            self.log_message("🔧 بدء الإصلاح الشامل...")
            self.apply_dark_mode()
            QTimer.singleShot(500, self.fix_tables)
            QTimer.singleShot(1000, self.check_system_status)
        
        def check_tables(self):
            """فحص الجداول"""
            try:
                app = QApplication.instance()
                if app:
                    tables = app.findChildren(QTableWidget)
                    self.log_message(f"🔍 تم العثور على {len(tables)} جدول:")
                    
                    for i, table in enumerate(tables):
                        object_name = table.objectName() or f"جدول_{i+1}"
                        self.log_message(f"  - {object_name}")
                        
                    self.status_label.setText(f"🔍 تم فحص {len(tables)} جدول")
                else:
                    self.log_message("❌ لا يوجد تطبيق للفحص")
                    
            except Exception as e:
                self.log_message(f"❌ خطأ في فحص الجداول: {e}")
    
    return TablesFixer()


def main():
    """الدالة الرئيسية"""
    print("📊 أداة إصلاح الوضع الليلي للجداول")
    print("=" * 50)
    
    try:
        # التحقق من وجود تطبيق Qt جاري
        app = QApplication.instance()
        
        if not app:
            # إنشاء تطبيق جديد إذا لم يكن موجود
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
            print("📱 تم إنشاء تطبيق Qt جديد")
        else:
            print("📱 تم العثور على تطبيق Qt جاري")
        
        # إنشاء أداة الإصلاح
        fixer = create_tables_fixer()
        fixer.show()
        
        print("✅ تم فتح أداة إصلاح الجداول!")
        print("\n🎯 استخدم الأزرار لإصلاح مشكلة الجداول")
        
        # تشغيل التطبيق إذا لم يكن جارياً
        if not QApplication.instance():
            return app.exec()
        else:
            return 0
        
    except Exception as e:
        print(f"💥 خطأ في تشغيل أداة الإصلاح: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
