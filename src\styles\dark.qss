/*
🌙 ملف الأنماط للوضع الليلي المحسن والمتطور
Enhanced & Modern Dark Mode Stylesheet
تصميم متطور مع نظام ألوان متوازن وتأثيرات بصرية حديثة
*/

/* الألوان المتطورة للوضع الليلي */
/* الألوان الأساسية */
/* bg-primary: #0f0f23 */
/* bg-secondary: #1a1a2e */
/* bg-tertiary: #16213e */
/* bg-surface: #1e2139 */
/* bg-elevated: #252545 */

/* ألوان النص */
/* text-primary: #e2e8f0 */
/* text-secondary: #a0aec0 */
/* text-muted: #718096 */
/* text-accent: #90cdf4 */

/* ألوان التمييز */
/* accent-primary: #667eea */
/* accent-secondary: #764ba2 */
/* accent-success: #48bb78 */
/* accent-warning: #ed8936 */
/* accent-danger: #f56565 */
/* accent-info: #4299e1 */

/* ألوان الحدود */
/* border-primary: #2d3748 */
/* border-secondary: #4a5568 */
/* border-accent: #667eea */

/* الإعدادات العامة المحسنة */
* {
    color: #e2e8f0;
    font-family: 'Segoe UI', 'SF Pro Display', 'Inter', 'Roboto', 'Arial', sans-serif;
    background-color: #0f0f23;
    selection-background-color: #667eea;
    selection-color: #ffffff;
    outline: none;
}

/* النافذة الرئيسية المحسنة */
QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #0f0f23,
        stop:0.3 #1a1a2e,
        stop:0.7 #16213e,
        stop:1 #0f0f23);
    border: 1px solid #2d3748;
    border-radius: 12px;
}

/* شريط العنوان المتطور */
QFrame#header {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #667eea,
        stop:0.5 #764ba2,
        stop:1 #667eea);
    border: none;
    border-radius: 12px 12px 0px 0px;
    padding: 12px 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

/* العناوين والنصوص المحسنة */
QLabel#app_title {
    color: #ffffff;
    font-size: 22px;
    font-weight: 700;
    text-shadow: 0px 2px 8px rgba(102, 126, 234, 0.4);
    padding: 8px 16px;
    letter-spacing: 0.5px;
}

QLabel#app_icon {
    color: #ffffff;
    font-size: 26px;
    margin-right: 12px;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3);
}

QLabel#user_info {
    color: #a0aec0;
    font-size: 14px;
    font-weight: 500;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(102, 126, 234, 0.15),
        stop:1 rgba(118, 75, 162, 0.15));
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 20px;
    padding: 8px 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

QLabel#window_title {
    color: #e2e8f0;
    font-size: 20px;
    font-weight: 600;
    margin: 16px 0;
    text-align: center;
    letter-spacing: 0.3px;
}

/* الشريط الجانبي المتطور */
QWidget#sidebar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 var(--bg-secondary),
        stop:0.3 var(--bg-tertiary),
        stop:0.7 var(--bg-surface),
        stop:1 var(--bg-secondary));
    border-right: 2px solid var(--border-primary);
    border-radius: 0px 16px 16px 0px;
    box-shadow: var(--shadow-lg);
}

QLabel#sidebar_section_title {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 16px;
    padding: 16px 12px 10px 12px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(102, 126, 234, 0.2),
        stop:1 rgba(118, 75, 162, 0.2));
    border-radius: 10px;
    margin: 8px;
    border-left: 4px solid var(--accent-primary);
    box-shadow: var(--shadow-sm);
    letter-spacing: 0.3px;
}

QFrame#sidebar_separator {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 transparent,
        stop:0.5 var(--border-secondary),
        stop:1 transparent);
    height: 2px;
    margin: 12px 20px;
    border-radius: 1px;
}

/* أزرار الشريط الجانبي المتطورة */
QPushButton#sidebar_button {
    background-color: transparent;
    color: var(--text-secondary);
    border: none;
    text-align: right;
    padding: 16px 20px;
    margin: 4px 10px;
    border-radius: 12px;
    font-size: 15px;
    font-weight: 500;
    letter-spacing: 0.2px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

QPushButton#sidebar_button:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(102, 126, 234, 0.15),
        stop:1 rgba(118, 75, 162, 0.15));
    color: var(--text-primary);
    border: 1px solid rgba(102, 126, 234, 0.4);
    transform: translateX(4px);
    box-shadow: var(--shadow-sm);
}

QPushButton#sidebar_button:checked {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    color: #ffffff;
    font-weight: 600;
    border: 1px solid rgba(102, 126, 234, 0.6);
    box-shadow: 0px 6px 20px rgba(102, 126, 234, 0.4);
    transform: translateX(2px);
}

QPushButton#sidebar_button:checked:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(102, 126, 234, 0.9),
        stop:1 rgba(118, 75, 162, 0.9));
    box-shadow: 0px 8px 25px rgba(102, 126, 234, 0.5);
}

QPushButton#sidebar_button:pressed {
    transform: translateX(1px) scale(0.98);
    transition: all 0.1s ease;
}

/* منطقة المحتوى المحسنة */
QWidget#content_area {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 var(--bg-surface),
        stop:1 var(--bg-elevated));
    border: 1px solid var(--border-primary);
    border-radius: 16px;
    margin: 8px;
    box-shadow: var(--shadow-md);
}

/* لوحة التحكم المتطورة */
QWidget#dashboard {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 var(--bg-surface),
        stop:1 var(--bg-elevated));
    border-radius: 16px;
    padding: 20px;
}

QLabel#dashboard_header {
    color: var(--text-primary);
    font-size: 24px;
    font-weight: 700;
    padding: 16px 0px;
    letter-spacing: 0.5px;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3);
}

QFrame#dashboard_header_frame {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    border: none;
    border-radius: 16px;
    margin: 12px;
    box-shadow: var(--shadow-lg);
    padding: 20px;
}

QLabel#dashboard_main_title {
    color: #ffffff;
    font-size: 22px;
    font-weight: 700;
    padding: 12px;
    letter-spacing: 0.8px;
    text-shadow: 0px 2px 6px rgba(0, 0, 0, 0.4);
}

QFrame#dashboard_separator {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 transparent,
        stop:0.5 var(--border-secondary),
        stop:1 transparent);
    height: 2px;
    margin: 16px 0px;
    border-radius: 1px;
}

QLabel#section_title {
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    padding: 18px 0px 12px 0px;
    letter-spacing: 0.3px;
}

/* البطاقات الإحصائية المتطورة */
QFrame#stat_card_blue, QFrame#employees_card {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #4299e1, stop:0.5 #3182ce, stop:1 #2b77cb);
    border: 1px solid rgba(66, 153, 225, 0.3);
    border-radius: 16px;
    color: #ffffff;
    box-shadow: 0px 8px 32px rgba(66, 153, 225, 0.2);
    padding: 20px;
}

QFrame#stat_card_green, QFrame#salaries_card {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #48bb78, stop:0.5 #38a169, stop:1 #2f855a);
    border: 1px solid rgba(72, 187, 120, 0.3);
    border-radius: 16px;
    color: #ffffff;
    box-shadow: 0px 8px 32px rgba(72, 187, 120, 0.2);
    padding: 20px;
}

QFrame#stat_card_orange, QFrame#advances_card {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #ed8936, stop:0.5 #dd6b20, stop:1 #c05621);
    border: 1px solid rgba(237, 137, 54, 0.3);
    border-radius: 16px;
    color: #ffffff;
    box-shadow: 0px 8px 32px rgba(237, 137, 54, 0.2);
    padding: 20px;
}

QFrame#stat_card_red, QFrame#debts_card {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #f56565, stop:0.5 #e53e3e, stop:1 #c53030);
    border: 1px solid rgba(245, 101, 101, 0.3);
    border-radius: 16px;
    color: #ffffff;
    box-shadow: 0px 8px 32px rgba(245, 101, 101, 0.2);
    padding: 20px;
}

QFrame#stat_card_purple {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #9f7aea, stop:0.5 #805ad5, stop:1 #6b46c1);
    border: 1px solid rgba(159, 122, 234, 0.3);
    border-radius: 16px;
    color: #ffffff;
    box-shadow: 0px 8px 32px rgba(159, 122, 234, 0.2);
    padding: 20px;
}

QFrame#stat_card_teal {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #4fd1c7, stop:0.5 #38b2ac, stop:1 #319795);
    border: 1px solid rgba(79, 209, 199, 0.3);
    border-radius: 16px;
    color: #ffffff;
    box-shadow: 0px 8px 32px rgba(79, 209, 199, 0.2);
    padding: 20px;
}

/* نصوص البطاقات المحسنة */
QLabel#stat_card_title, QLabel#card_title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.3px;
    margin-bottom: 8px;
}

QLabel#stat_card_value, QLabel#card_value {
    color: #ffffff;
    font-size: 32px;
    font-weight: 800;
    letter-spacing: 0.5px;
    text-shadow: 0px 2px 8px rgba(0, 0, 0, 0.3);
    margin: 12px 0px;
}

QLabel#stat_card_subtitle, QLabel#card_subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0.2px;
    margin-top: 4px;
}

/* أزرار الإجراءات السريعة المحسنة */
QPushButton#quick_action_button {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    color: #ffffff;
    border: none;
    border-radius: 12px;
    padding: 14px 24px;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.3px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

QPushButton#quick_action_button:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(102, 126, 234, 0.9),
        stop:1 rgba(118, 75, 162, 0.9));
    transform: translateY(-2px);
    box-shadow: 0px 12px 24px rgba(102, 126, 234, 0.3);
}

QPushButton#quick_action_button:pressed {
    transform: translateY(0px) scale(0.98);
    box-shadow: var(--shadow-sm);
    transition: all 0.1s ease;
}

/* شريط التمرير المحسن */
QScrollBar:vertical {
    background: var(--bg-surface);
    width: 14px;
    border-radius: 7px;
    margin: 0px;
    border: 1px solid var(--border-primary);
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    border-radius: 6px;
    min-height: 30px;
    margin: 2px;
    box-shadow: var(--shadow-sm);
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(102, 126, 234, 0.8),
        stop:1 rgba(118, 75, 162, 0.8));
    box-shadow: var(--shadow-md);
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
    height: 0px;
}

QScrollBar:horizontal {
    background: var(--bg-surface);
    height: 14px;
    border-radius: 7px;
    margin: 0px;
    border: 1px solid var(--border-primary);
}

QScrollBar::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    border-radius: 6px;
    min-width: 30px;
    margin: 2px;
    box-shadow: var(--shadow-sm);
}

/* الجداول المحسنة */
QTableWidget {
    background: var(--bg-surface);
    alternate-background-color: var(--bg-elevated);
    selection-background-color: var(--accent-primary);
    selection-color: #ffffff;
    border: 2px solid var(--border-primary);
    gridline-color: var(--border-secondary);
    color: var(--text-primary);
    border-radius: 12px;
    font-size: 13px;
    outline: none;
}

QTableWidget::item {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-primary);
    color: var(--text-primary);
    border-radius: 4px;
    margin: 1px;
}

QTableWidget::item:hover {
    background-color: rgba(102, 126, 234, 0.1);
    color: var(--text-accent);
}

QTableWidget::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    color: #ffffff;
    border-radius: 6px;
    box-shadow: var(--shadow-sm);
}

QHeaderView::section {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 var(--bg-tertiary),
        stop:1 var(--bg-surface));
    color: var(--text-primary);
    padding: 14px 16px;
    border: none;
    font-weight: 600;
    font-size: 14px;
    border-bottom: 2px solid var(--accent-primary);
    border-radius: 8px 8px 0px 0px;
    letter-spacing: 0.2px;
}

QHeaderView::section:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(102, 126, 234, 0.2),
        stop:1 var(--bg-surface));
}

/* حقول الإدخال المحسنة */
QLineEdit {
    background: var(--bg-elevated);
    border: 2px solid var(--border-primary);
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

QLineEdit:focus {
    border-color: var(--accent-primary);
    background: var(--bg-surface);
    box-shadow: 0px 0px 0px 3px rgba(102, 126, 234, 0.2);
    outline: none;
}

QLineEdit:hover {
    border-color: var(--border-accent);
    background: var(--bg-surface);
}

/* القوائم المنسدلة المحسنة */
QComboBox {
    background: var(--bg-elevated);
    border: 2px solid var(--border-primary);
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
    min-width: 120px;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

QComboBox:focus {
    border-color: var(--accent-primary);
    background: var(--bg-surface);
    box-shadow: 0px 0px 0px 3px rgba(102, 126, 234, 0.2);
    outline: none;
}

QComboBox:hover {
    border-color: var(--border-accent);
    background: var(--bg-surface);
}

QComboBox::drop-down {
    border: none;
    width: 30px;
    background: transparent;
    border-radius: 0px 8px 8px 0px;
}

QComboBox::down-arrow {
    width: 16px;
    height: 16px;
    background: var(--accent-primary);
    border-radius: 3px;
}

QComboBox QAbstractItemView {
    background: var(--bg-elevated);
    color: var(--text-primary);
    selection-background-color: var(--accent-primary);
    selection-color: #ffffff;
    border: 2px solid var(--border-primary);
    border-radius: 10px;
    padding: 8px;
    box-shadow: var(--shadow-lg);
    outline: none;
}

QComboBox QAbstractItemView::item {
    padding: 10px 16px;
    border-radius: 6px;
    margin: 2px;
}

QComboBox QAbstractItemView::item:hover {
    background: rgba(102, 126, 234, 0.2);
    color: var(--text-accent);
}

/* حقول التاريخ المحسنة */
QDateEdit {
    background: var(--bg-elevated);
    border: 2px solid var(--border-primary);
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
    min-width: 140px;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

QDateEdit:focus {
    border-color: var(--accent-primary);
    background: var(--bg-surface);
    box-shadow: 0px 0px 0px 3px rgba(102, 126, 234, 0.2);
    outline: none;
}

QDateEdit:hover {
    border-color: var(--border-accent);
    background: var(--bg-surface);
}

QDateEdit::drop-down {
    border: none;
    width: 30px;
    background: transparent;
    border-radius: 0px 8px 8px 0px;
}

QDateEdit::down-arrow {
    width: 16px;
    height: 16px;
    background: var(--accent-primary);
    border-radius: 3px;
}

/* مربعات النص المحسنة */
QTextEdit {
    background: var(--bg-elevated);
    border: 2px solid var(--border-primary);
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
    line-height: 1.5;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

QTextEdit:focus {
    border-color: var(--accent-primary);
    background: var(--bg-surface);
    box-shadow: 0px 0px 0px 3px rgba(102, 126, 234, 0.2);
    outline: none;
}

QTextEdit:hover {
    border-color: var(--border-accent);
    background: var(--bg-surface);
}

/* حقول الأرقام */
QSpinBox, QDoubleSpinBox {
    background: var(--bg-elevated);
    border: 2px solid var(--border-primary);
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
    min-width: 100px;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: var(--accent-primary);
    background: var(--bg-surface);
    box-shadow: 0px 0px 0px 3px rgba(102, 126, 234, 0.2);
    outline: none;
}

/* الأزرار العامة المحسنة */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    color: #ffffff;
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.3px;
    min-width: 100px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(102, 126, 234, 0.9),
        stop:1 rgba(118, 75, 162, 0.9));
    transform: translateY(-2px);
    box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.3);
}

QPushButton:pressed {
    transform: translateY(0px) scale(0.98);
    box-shadow: var(--shadow-sm);
    transition: all 0.1s ease;
}

QPushButton:disabled {
    background: var(--bg-surface);
    color: var(--text-muted);
    box-shadow: none;
    transform: none;
}

/* أزرار الخطر المحسنة */
QPushButton#danger_button {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-danger),
        stop:1 #c53030);
    box-shadow: 0px 4px 12px rgba(245, 101, 101, 0.3);
}

QPushButton#danger_button:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(245, 101, 101, 0.9),
        stop:1 rgba(197, 48, 48, 0.9));
    box-shadow: 0px 8px 20px rgba(245, 101, 101, 0.4);
}

/* أزرار النجاح المحسنة */
QPushButton#success_button {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-success),
        stop:1 #2f855a);
    box-shadow: 0px 4px 12px rgba(72, 187, 120, 0.3);
}

QPushButton#success_button:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(72, 187, 120, 0.9),
        stop:1 rgba(47, 133, 90, 0.9));
    box-shadow: 0px 8px 20px rgba(72, 187, 120, 0.4);
}

/* أزرار التحذير */
QPushButton#warning_button {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-warning),
        stop:1 #c05621);
    box-shadow: 0px 4px 12px rgba(237, 137, 54, 0.3);
}

QPushButton#warning_button:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(237, 137, 54, 0.9),
        stop:1 rgba(192, 86, 33, 0.9));
    box-shadow: 0px 8px 20px rgba(237, 137, 54, 0.4);
}

/* أزرار المعلومات */
QPushButton#info_button {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-info),
        stop:1 #2b77cb);
    box-shadow: 0px 4px 12px rgba(66, 153, 225, 0.3);
}

QPushButton#info_button:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(66, 153, 225, 0.9),
        stop:1 rgba(43, 119, 203, 0.9));
    box-shadow: 0px 8px 20px rgba(66, 153, 225, 0.4);
}

/* مجموعات العناصر المحسنة */
QGroupBox {
    font-weight: 600;
    border: 2px solid var(--border-primary);
    border-radius: 12px;
    margin-top: 16px;
    color: var(--text-primary);
    background: var(--bg-elevated);
    padding: 16px;
    box-shadow: var(--shadow-sm);
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 16px;
    padding: 4px 12px;
    color: var(--text-accent);
    background: var(--bg-surface);
    border: 1px solid var(--border-accent);
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.2px;
}

/* التسميات المحسنة */
QLabel {
    color: var(--text-primary);
    font-weight: 500;
}

QLabel#section_header {
    color: var(--text-accent);
    font-size: 18px;
    font-weight: 700;
    letter-spacing: 0.3px;
    margin: 16px 0px 8px 0px;
}

QLabel#info_label {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 400;
    line-height: 1.4;
}

/* شريط التقدم المحسن */
QProgressBar {
    border: 2px solid var(--border-primary);
    border-radius: 12px;
    text-align: center;
    color: var(--text-primary);
    background: var(--bg-elevated);
    font-weight: 600;
    font-size: 13px;
    padding: 2px;
    box-shadow: var(--shadow-sm);
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    border-radius: 10px;
    box-shadow: 0px 2px 8px rgba(102, 126, 234, 0.3);
}

/* خانات الاختيار المحسنة */
QCheckBox {
    color: var(--text-primary);
    font-weight: 500;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 20px;
    height: 20px;
}

QCheckBox::indicator:unchecked {
    border: 2px solid var(--border-primary);
    background: var(--bg-elevated);
    border-radius: 6px;
    box-shadow: var(--shadow-sm);
}

QCheckBox::indicator:unchecked:hover {
    border-color: var(--accent-primary);
    background: var(--bg-surface);
}

QCheckBox::indicator:checked {
    border: 2px solid var(--accent-primary);
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    border-radius: 6px;
    box-shadow: 0px 2px 8px rgba(102, 126, 234, 0.3);
}

QCheckBox::indicator:checked:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 rgba(102, 126, 234, 0.9),
        stop:1 rgba(118, 75, 162, 0.9));
    box-shadow: 0px 4px 12px rgba(102, 126, 234, 0.4);
}

/* أزرار الراديو المحسنة */
QRadioButton {
    color: var(--text-primary);
    font-weight: 500;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 20px;
    height: 20px;
}

QRadioButton::indicator:unchecked {
    border: 2px solid var(--border-primary);
    background: var(--bg-elevated);
    border-radius: 10px;
    box-shadow: var(--shadow-sm);
}

QRadioButton::indicator:unchecked:hover {
    border-color: var(--accent-primary);
    background: var(--bg-surface);
}

QRadioButton::indicator:checked {
    border: 2px solid var(--accent-primary);
    background: qradial-gradient(circle,
        var(--accent-primary) 0%,
        var(--accent-primary) 40%,
        var(--bg-elevated) 50%);
    border-radius: 10px;
    box-shadow: 0px 2px 8px rgba(102, 126, 234, 0.3);
}

QRadioButton::indicator:checked:hover {
    background: qradial-gradient(circle,
        rgba(102, 126, 234, 0.9) 0%,
        rgba(102, 126, 234, 0.9) 40%,
        var(--bg-surface) 50%);
    box-shadow: 0px 4px 12px rgba(102, 126, 234, 0.4);
}

/* التبويبات المحسنة */
QTabWidget::pane {
    border: 2px solid var(--border-primary);
    background: var(--bg-surface);
    border-radius: 12px;
    padding: 16px;
    box-shadow: var(--shadow-md);
}

QTabBar::tab {
    background: var(--bg-elevated);
    color: var(--text-secondary);
    padding: 12px 20px;
    margin-right: 4px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    font-weight: 500;
    font-size: 14px;
    letter-spacing: 0.2px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

QTabBar::tab:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    color: #ffffff;
    font-weight: 600;
    box-shadow: 0px 4px 12px rgba(102, 126, 234, 0.3);
}

QTabBar::tab:hover:!selected {
    background: var(--bg-surface);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* شريط الحالة المحسن */
QStatusBar {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--bg-secondary),
        stop:1 var(--bg-tertiary));
    color: var(--text-secondary);
    border-top: 2px solid var(--border-primary);
    padding: 8px 16px;
    font-weight: 500;
    font-size: 13px;
}

QStatusBar::item {
    border: none;
    padding: 4px 8px;
    border-radius: 6px;
    background: rgba(102, 126, 234, 0.1);
    margin: 2px;
}

/* القوائم المحسنة */
QMenuBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 var(--bg-secondary),
        stop:1 var(--bg-tertiary));
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-primary);
    padding: 4px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

QMenuBar::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    color: #ffffff;
    box-shadow: var(--shadow-sm);
}

QMenu {
    background: var(--bg-elevated);
    color: var(--text-primary);
    border: 2px solid var(--border-primary);
    border-radius: 12px;
    padding: 8px;
    box-shadow: var(--shadow-lg);
}

QMenu::item {
    padding: 10px 20px;
    border-radius: 8px;
    margin: 2px;
    font-weight: 500;
    transition: all 0.2s ease;
}

QMenu::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    color: #ffffff;
    box-shadow: var(--shadow-sm);
}

QMenu::separator {
    height: 2px;
    background: var(--border-primary);
    margin: 8px 16px;
    border-radius: 1px;
}

/* النوافذ المنبثقة والحوارات */
QDialog {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 var(--bg-primary),
        stop:0.5 var(--bg-secondary),
        stop:1 var(--bg-tertiary));
    border: 2px solid var(--border-primary);
    border-radius: 16px;
    box-shadow: var(--shadow-xl);
}

QDialog QLabel {
    color: var(--text-primary);
    font-weight: 500;
}

/* أشرطة الأدوات */
QToolBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 var(--bg-secondary),
        stop:1 var(--bg-tertiary));
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 4px;
    spacing: 4px;
}

QToolButton {
    background: transparent;
    color: var(--text-secondary);
    border: none;
    border-radius: 8px;
    padding: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

QToolButton:hover {
    background: rgba(102, 126, 234, 0.2);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
}

QToolButton:pressed {
    background: var(--accent-primary);
    color: #ffffff;
}

/* عناصر القائمة */
QListWidget {
    background: var(--bg-surface);
    border: 2px solid var(--border-primary);
    border-radius: 12px;
    color: var(--text-primary);
    font-weight: 500;
    padding: 8px;
    box-shadow: var(--shadow-md);
    outline: none;
}

QListWidget::item {
    padding: 12px 16px;
    border-radius: 8px;
    margin: 2px;
    transition: all 0.2s ease;
}

QListWidget::item:hover {
    background: rgba(102, 126, 234, 0.15);
    color: var(--text-accent);
}

QListWidget::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    color: #ffffff;
    font-weight: 600;
    box-shadow: var(--shadow-sm);
}

/* عرض الشجرة */
QTreeWidget {
    background: var(--bg-surface);
    border: 2px solid var(--border-primary);
    border-radius: 12px;
    color: var(--text-primary);
    font-weight: 500;
    padding: 8px;
    box-shadow: var(--shadow-md);
    outline: none;
}

QTreeWidget::item {
    padding: 8px 12px;
    border-radius: 6px;
    margin: 1px;
}

QTreeWidget::item:hover {
    background: rgba(102, 126, 234, 0.15);
    color: var(--text-accent);
}

QTreeWidget::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    color: #ffffff;
    font-weight: 600;
}

/* شرائح التمرير */
QSlider::groove:horizontal {
    background: var(--bg-elevated);
    height: 8px;
    border-radius: 4px;
    border: 1px solid var(--border-primary);
}

QSlider::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    width: 20px;
    height: 20px;
    border-radius: 10px;
    margin: -6px 0;
    box-shadow: var(--shadow-md);
}

QSlider::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(102, 126, 234, 0.9),
        stop:1 rgba(118, 75, 162, 0.9));
    box-shadow: 0px 4px 12px rgba(102, 126, 234, 0.4);
}

/* تحسينات إضافية للتفاعل */
QWidget:focus {
    outline: none;
}

/* تأثيرات الانتقال العامة */
* {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسينات خاصة للنوافذ الفرعية */
QMdiSubWindow {
    background: var(--bg-surface);
    border: 2px solid var(--border-primary);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
}

QMdiSubWindow::title {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 var(--accent-primary),
        stop:1 var(--accent-secondary));
    color: #ffffff;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 10px 10px 0px 0px;
}

/* تحسينات للرسائل والتنبيهات */
QMessageBox {
    background: var(--bg-surface);
    border: 2px solid var(--border-primary);
    border-radius: 16px;
    box-shadow: var(--shadow-xl);
}

QMessageBox QLabel {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
    line-height: 1.5;
}

QMessageBox QPushButton {
    min-width: 80px;
    margin: 4px;
}

/* تأثيرات بصرية متقدمة وانتقالات سلسة */

/* تأثيرات الإضاءة والتوهج */
@keyframes glow {
    0% { box-shadow: 0 0 5px var(--accent-primary); }
    50% { box-shadow: 0 0 20px var(--accent-primary), 0 0 30px var(--accent-primary); }
    100% { box-shadow: 0 0 5px var(--accent-primary); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes slideIn {
    0% { transform: translateX(-100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

/* تأثيرات خاصة للعناصر التفاعلية */
QPushButton:focus {
    animation: glow 2s infinite;
    outline: none;
}

QFrame#stat_card_blue:hover,
QFrame#employees_card:hover {
    animation: pulse 0.6s ease-in-out;
}

/* تأثيرات الزجاج المصقول (Glassmorphism) */
QFrame#glass_effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* تأثيرات النيومورفيزم للوضع الليلي */
QFrame#neomorphism_card {
    background: var(--bg-surface);
    border-radius: 20px;
    box-shadow:
        8px 8px 16px rgba(0, 0, 0, 0.4),
        -8px -8px 16px rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

QFrame#neomorphism_card:hover {
    box-shadow:
        12px 12px 24px rgba(0, 0, 0, 0.5),
        -12px -12px 24px rgba(255, 255, 255, 0.08);
}

/* تأثيرات الحدود المتوهجة */
.glowing-border {
    border: 2px solid transparent;
    background: linear-gradient(var(--bg-surface), var(--bg-surface)) padding-box,
                linear-gradient(45deg, var(--accent-primary), var(--accent-secondary)) border-box;
    border-radius: 12px;
}

/* تأثيرات التدرج المتحرك */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

QFrame#animated_gradient {
    background: linear-gradient(-45deg,
        var(--accent-primary),
        var(--accent-secondary),
        var(--accent-info),
        var(--accent-success));
    background-size: 400% 400%;
    animation: gradientShift 4s ease infinite;
    border-radius: 16px;
}

/* تأثيرات الظلال المتقدمة */
.shadow-soft {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.shadow-medium {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.shadow-hard {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.shadow-colored {
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

/* تأثيرات الانعكاس */
QFrame#reflection_effect {
    position: relative;
}

QFrame#reflection_effect::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom,
        rgba(var(--bg-surface), 0.3),
        transparent);
    transform: scaleY(-1);
    opacity: 0.3;
}

/* تأثيرات التمرير السلس */
QScrollArea {
    scroll-behavior: smooth;
}

/* تأثيرات الانتقال المتقدمة */
.transition-all {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-fast {
    transition: all 0.15s ease-out;
}

.transition-slow {
    transition: all 0.5s ease-in-out;
}

/* تأثيرات التحويل ثلاثي الأبعاد */
.transform-3d {
    transform-style: preserve-3d;
    perspective: 1000px;
}

QFrame#card_3d:hover {
    transform: rotateY(5deg) rotateX(5deg);
    transition: transform 0.3s ease;
}

/* تأثيرات الخلفية المتحركة */
@keyframes floatingParticles {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(120deg); }
    66% { transform: translateY(5px) rotate(240deg); }
}

.floating-animation {
    animation: floatingParticles 6s ease-in-out infinite;
}

/* تأثيرات الإضاءة الخلفية */
QFrame#backlight_effect {
    position: relative;
    overflow: hidden;
}

QFrame#backlight_effect::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle,
        rgba(102, 126, 234, 0.1) 0%,
        transparent 70%);
    animation: floatingParticles 8s linear infinite;
}

/* تأثيرات الحدود المتموجة */
@keyframes borderWave {
    0% { border-radius: 20px 40px 20px 40px; }
    25% { border-radius: 40px 20px 40px 20px; }
    50% { border-radius: 20px 40px 20px 40px; }
    75% { border-radius: 40px 20px 40px 20px; }
    100% { border-radius: 20px 40px 20px 40px; }
}

QFrame#wave_border {
    animation: borderWave 4s ease-in-out infinite;
}

/* تحسينات الأداء للتأثيرات */
* {
    will-change: transform, opacity, box-shadow;
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تحسينات إمكانية الوصول والقراءة */

/* تحسين التباين للنصوص */
QLabel#high_contrast_text {
    color: #ffffff;
    background-color: var(--bg-primary);
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

QLabel#medium_contrast_text {
    color: #e2e8f0;
    background-color: var(--bg-secondary);
    font-weight: 500;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
}

/* تحسين التباين للأزرار */
QPushButton#accessible_button {
    background: var(--accent-primary);
    color: #ffffff;
    border: 2px solid #ffffff;
    font-weight: 700;
    font-size: 16px;
    min-height: 44px; /* الحد الأدنى للمس */
    min-width: 120px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

QPushButton#accessible_button:focus {
    border: 3px solid #ffff00; /* حدود صفراء للتركيز */
    outline: 2px solid #000000;
    outline-offset: 2px;
}

/* تحسين التباين للحقول */
QLineEdit#accessible_input {
    background: #ffffff;
    color: #000000;
    border: 3px solid var(--accent-primary);
    font-size: 16px;
    font-weight: 600;
    min-height: 40px;
    padding: 12px 16px;
}

QLineEdit#accessible_input:focus {
    border: 3px solid #ffff00;
    outline: 2px solid #000000;
    outline-offset: 2px;
    background: #fffacd; /* خلفية فاتحة للتركيز */
}

/* تحسين التباين للجداول */
QTableWidget#accessible_table {
    background: var(--bg-surface);
    alternate-background-color: var(--bg-elevated);
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
    gridline-color: #ffffff;
    selection-background-color: #ffff00;
    selection-color: #000000;
}

QTableWidget#accessible_table::item {
    padding: 16px;
    border: 1px solid #ffffff;
}

QTableWidget#accessible_table::item:selected {
    background: #ffff00;
    color: #000000;
    font-weight: 700;
    border: 2px solid #000000;
}

/* تحسين التباين لخانات الاختيار */
QCheckBox#accessible_checkbox {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    spacing: 12px;
}

QCheckBox#accessible_checkbox::indicator {
    width: 24px;
    height: 24px;
    border: 3px solid #ffffff;
    background: var(--bg-primary);
}

QCheckBox#accessible_checkbox::indicator:checked {
    background: #ffff00;
    border: 3px solid #000000;
}

QCheckBox#accessible_checkbox::indicator:focus {
    outline: 2px solid #ffff00;
    outline-offset: 2px;
}

/* تحسين التباين للقوائم المنسدلة */
QComboBox#accessible_combo {
    background: #ffffff;
    color: #000000;
    border: 3px solid var(--accent-primary);
    font-size: 16px;
    font-weight: 600;
    min-height: 40px;
    padding: 12px 16px;
}

QComboBox#accessible_combo:focus {
    border: 3px solid #ffff00;
    outline: 2px solid #000000;
    outline-offset: 2px;
}

QComboBox#accessible_combo QAbstractItemView {
    background: #ffffff;
    color: #000000;
    selection-background-color: #ffff00;
    selection-color: #000000;
    border: 2px solid #000000;
    font-weight: 600;
}

/* تحسينات للمستخدمين ذوي الإعاقة البصرية */
.screen_reader_only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسينات للحركة المحدودة */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
    * {
        border-width: 2px !important;
        outline-width: 2px !important;
    }

    QLabel {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 1) !important;
    }

    QPushButton {
        border: 3px solid #ffffff !important;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 1) !important;
    }
}

/* تحسينات لحجم الخط الكبير */
.large_text {
    font-size: 18px !important;
    line-height: 1.6 !important;
    letter-spacing: 0.5px !important;
}

.extra_large_text {
    font-size: 24px !important;
    line-height: 1.8 !important;
    letter-spacing: 0.8px !important;
}

/* تحسينات للتنقل بلوحة المفاتيح */
*:focus {
    outline: 3px solid #ffff00 !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 5px rgba(255, 255, 0, 0.3) !important;
}

/* تحسينات للألوان الآمنة للعمى اللوني */
.colorblind_safe_red {
    background: #d73027 !important; /* أحمر آمن */
}

.colorblind_safe_green {
    background: #1a9850 !important; /* أخضر آمن */
}

.colorblind_safe_blue {
    background: #313695 !important; /* أزرق آمن */
}

.colorblind_safe_orange {
    background: #f46d43 !important; /* برتقالي آمن */
}

/* تحسينات للنصوص الطويلة */
.readable_text {
    line-height: 1.6;
    word-spacing: 0.2em;
    letter-spacing: 0.05em;
    max-width: 70ch; /* عرض مثالي للقراءة */
    margin: 0 auto;
}

/* تحسينات للروابط والعناصر التفاعلية */
QLabel#link_style {
    color: #87ceeb; /* أزرق فاتح للروابط */
    text-decoration: underline;
    font-weight: 600;
}

QLabel#link_style:hover {
    color: #ffffff;
    background: var(--accent-primary);
    padding: 4px 8px;
    border-radius: 4px;
}

/* تحسينات للرسائل والتنبيهات */
QLabel#error_message {
    background: #d73027;
    color: #ffffff;
    font-weight: 700;
    padding: 12px 16px;
    border-radius: 8px;
    border: 2px solid #ffffff;
    font-size: 16px;
}

QLabel#success_message {
    background: #1a9850;
    color: #ffffff;
    font-weight: 700;
    padding: 12px 16px;
    border-radius: 8px;
    border: 2px solid #ffffff;
    font-size: 16px;
}

QLabel#warning_message {
    background: #f46d43;
    color: #000000;
    font-weight: 700;
    padding: 12px 16px;
    border-radius: 8px;
    border: 2px solid #000000;
    font-size: 16px;
}

QLabel#info_message {
    background: #313695;
    color: #ffffff;
    font-weight: 700;
    padding: 12px 16px;
    border-radius: 8px;
    border: 2px solid #ffffff;
    font-size: 16px;
}
