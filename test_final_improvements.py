#!/usr/bin/env python3
"""
اختبار التحسينات النهائية
Test Final Improvements
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.database import db_manager
from src.config import get_config


def test_font_size_config():
    """اختبار إعداد حجم الخط"""
    print("🔤 اختبار إعداد حجم الخط...")
    
    try:
        # اختبار تحميل الإعداد
        ui_config = get_config("ui")
        font_size = ui_config.get("font_size", 10)
        print(f"   حجم الخط المحفوظ: {font_size}")
        
        # اختبار تحديث الإعداد
        from src.config import update_config
        update_config("ui", "font_size", 14)
        
        # التحقق من التحديث
        updated_config = get_config("ui")
        updated_font_size = updated_config.get("font_size", 10)
        print(f"   حجم الخط بعد التحديث: {updated_font_size}")
        
        if updated_font_size == 14:
            print("   ✅ إعداد حجم الخط يعمل بشكل صحيح")
            return True
        else:
            print("   ❌ مشكلة في حفظ إعداد حجم الخط")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار حجم الخط: {e}")
        return False


def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n💾 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        print("   ✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # اختبار الاستعلام
        from src.database import get_db_session_context
        from src.models import Employee
        
        with get_db_session_context() as session:
            employee_count = session.query(Employee).count()
            print(f"   عدد الموظفين في النظام: {employee_count}")
            
        print("   ✅ استعلامات قاعدة البيانات تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")
        return False


def test_salary_system():
    """اختبار نظام الرواتب"""
    print("\n💰 اختبار نظام الرواتب...")
    
    try:
        from src.database import get_db_session_context
        from src.models import SalaryRecord
        
        with get_db_session_context() as session:
            salary_records = session.query(SalaryRecord).all()
            print(f"   عدد سجلات الرواتب: {len(salary_records)}")
            
            if salary_records:
                record = salary_records[0]
                print(f"   مثال على راتب: {record.employee.full_name}")
                print(f"   صافي الراتب: {record.net_salary:,.0f} د.ع")
                
                # اختبار إعادة الحساب
                old_net = record.net_salary
                record.calculate_salary()
                new_net = record.net_salary
                
                if abs(float(old_net) - float(new_net)) < 0.01:
                    print("   ✅ حساب الرواتب يعمل بشكل صحيح")
                    return True
                else:
                    print("   ⚠️ هناك اختلاف في حساب الرواتب")
                    return False
            else:
                print("   ⚠️ لا توجد سجلات رواتب للاختبار")
                return True
                
    except Exception as e:
        print(f"   ❌ خطأ في نظام الرواتب: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_financial_transactions():
    """اختبار المعاملات المالية"""
    print("\n💸 اختبار المعاملات المالية...")
    
    try:
        from src.database import get_db_session_context
        from src.models import FinancialTransaction
        
        with get_db_session_context() as session:
            transactions = session.query(FinancialTransaction).all()
            print(f"   عدد المعاملات المالية: {len(transactions)}")
            
            if transactions:
                # تجميع المعاملات حسب النوع
                transaction_types = {}
                for transaction in transactions:
                    t_type = transaction.transaction_type.value
                    if t_type not in transaction_types:
                        transaction_types[t_type] = 0
                    transaction_types[t_type] += 1
                
                print("   أنواع المعاملات:")
                for t_type, count in transaction_types.items():
                    print(f"     {t_type}: {count}")
                
                print("   ✅ المعاملات المالية تعمل بشكل صحيح")
                return True
            else:
                print("   ⚠️ لا توجد معاملات مالية للاختبار")
                return True
                
    except Exception as e:
        print(f"   ❌ خطأ في المعاملات المالية: {e}")
        return False


def test_config_system():
    """اختبار نظام الإعدادات"""
    print("\n⚙️ اختبار نظام الإعدادات...")
    
    try:
        # اختبار تحميل الإعدادات
        app_config = get_config("app")
        ui_config = get_config("ui")
        
        print(f"   اسم التطبيق: {app_config.get('name', 'غير محدد')}")
        print(f"   ارتفاع الهيدر: {ui_config.get('header_height', 'غير محدد')}")
        print(f"   عرض الشريط الجانبي: {ui_config.get('sidebar_width', 'غير محدد')}")
        
        print("   ✅ نظام الإعدادات يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في نظام الإعدادات: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار التحسينات النهائية للنظام")
    print("=" * 60)
    
    tests = [
        ("إعداد حجم الخط", test_font_size_config),
        ("قاعدة البيانات", test_database_connection),
        ("نظام الرواتب", test_salary_system),
        ("المعاملات المالية", test_financial_transactions),
        ("نظام الإعدادات", test_config_system)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبارات:")
    print(f"   الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        print("\n✅ التحسينات المطبقة:")
        print("• تم إصلاح مشكلة حجم الخط في الإعدادات")
        print("• تم تكبير الأيقونات وتحسين العرض")
        print("• تم تحسين شريط العنوان وجعله أكثر تميزاً")
        print("• تم تحسين أزرار الشريط الجانبي")
        print("• تم إصلاح نظام الديون والمعاملات المالية")
        print("• تم تحسين حساب الرواتب")
        
        print("\n🚀 يمكنك الآن تشغيل النظام:")
        print("python run_hr_system.py")
        return 0
    else:
        print(f"\n⚠️ فشل {total_tests - passed_tests} اختبار من أصل {total_tests}")
        print("يرجى مراجعة الأخطاء أعلاه")
        return 1


if __name__ == "__main__":
    sys.exit(main())
