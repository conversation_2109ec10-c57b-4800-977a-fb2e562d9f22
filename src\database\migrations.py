"""
نظام ترحيلات قاعدة البيانات
Database Migrations System
"""

import os
import logging
from datetime import datetime
from pathlib import Path
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from .connection import get_database_url
from ..models import Base
from ..config import get_config


class MigrationManager:
    """مدير ترحيلات قاعدة البيانات"""
    
    def __init__(self):
        self.database_url = get_database_url()
        self.engine = None
        self.migrations_dir = Path(__file__).parent / "migration_files"
        self.migrations_dir.mkdir(exist_ok=True)
        
    def create_engine(self):
        """إنشاء محرك قاعدة البيانات"""
        try:
            self.engine = create_engine(self.database_url, echo=False)
            logging.info("تم إنشاء محرك قاعدة البيانات بنجاح")
            return True
        except Exception as e:
            logging.error(f"فشل في إنشاء محرك قاعدة البيانات: {e}")
            return False
            
    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            if not self.engine:
                if not self.create_engine():
                    return False
                    
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logging.info("تم اختبار الاتصال بقاعدة البيانات بنجاح")
            return True
        except Exception as e:
            logging.error(f"فشل في الاتصال بقاعدة البيانات: {e}")
            return False
            
    def create_database_if_not_exists(self):
        """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
        try:
            db_config = get_config("database")
            
            # إنشاء URL للاتصال بخادم PostgreSQL بدون تحديد قاعدة البيانات
            server_url = f"postgresql://{db_config['username']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/postgres"
            
            # الاتصال بخادم PostgreSQL
            server_engine = create_engine(server_url)
            
            with server_engine.connect() as conn:
                # التحقق من وجود قاعدة البيانات
                result = conn.execute(text(
                    "SELECT 1 FROM pg_database WHERE datname = :db_name"
                ), {"db_name": db_config['database']})
                
                if not result.fetchone():
                    # إنشاء قاعدة البيانات
                    conn.execute(text("COMMIT"))  # إنهاء المعاملة الحالية
                    conn.execute(text(f'CREATE DATABASE "{db_config["database"]}"'))
                    logging.info(f"تم إنشاء قاعدة البيانات: {db_config['database']}")
                else:
                    logging.info(f"قاعدة البيانات موجودة بالفعل: {db_config['database']}")
                    
            server_engine.dispose()
            return True
            
        except Exception as e:
            logging.error(f"فشل في إنشاء قاعدة البيانات: {e}")
            return False
            
    def create_migrations_table(self):
        """إنشاء جدول تتبع الترحيلات"""
        try:
            if not self.engine:
                if not self.create_engine():
                    return False
                    
            with self.engine.connect() as conn:
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS schema_migrations (
                        id SERIAL PRIMARY KEY,
                        version VARCHAR(255) NOT NULL UNIQUE,
                        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """))
                conn.commit()
                
            logging.info("تم إنشاء جدول تتبع الترحيلات")
            return True
            
        except Exception as e:
            logging.error(f"فشل في إنشاء جدول الترحيلات: {e}")
            return False
            
    def create_all_tables(self):
        """إنشاء جميع الجداول"""
        try:
            if not self.engine:
                if not self.create_engine():
                    return False
                    
            # إنشاء جميع الجداول من النماذج
            Base.metadata.create_all(self.engine)
            logging.info("تم إنشاء جميع الجداول بنجاح")
            
            # تسجيل الترحيل
            self.record_migration("initial_schema")
            
            return True
            
        except Exception as e:
            logging.error(f"فشل في إنشاء الجداول: {e}")
            return False
            
    def record_migration(self, version: str):
        """تسجيل ترحيل في جدول التتبع"""
        try:
            with self.engine.connect() as conn:
                # التحقق من عدم وجود الترحيل مسبقاً
                result = conn.execute(text(
                    "SELECT 1 FROM schema_migrations WHERE version = :version"
                ), {"version": version})
                
                if not result.fetchone():
                    conn.execute(text(
                        "INSERT INTO schema_migrations (version) VALUES (:version)"
                    ), {"version": version})
                    conn.commit()
                    logging.info(f"تم تسجيل الترحيل: {version}")
                    
        except Exception as e:
            logging.error(f"فشل في تسجيل الترحيل: {e}")
            
    def get_applied_migrations(self):
        """الحصول على قائمة الترحيلات المطبقة"""
        try:
            if not self.engine:
                if not self.create_engine():
                    return []
                    
            with self.engine.connect() as conn:
                result = conn.execute(text(
                    "SELECT version FROM schema_migrations ORDER BY applied_at"
                ))
                return [row[0] for row in result.fetchall()]
                
        except Exception as e:
            logging.error(f"فشل في الحصول على الترحيلات المطبقة: {e}")
            return []
            
    def insert_sample_data(self):
        """إدراج بيانات نموذجية"""
        try:
            from ..models import Department, JobTitle, Employee, EmploymentStatus
            from sqlalchemy.orm import sessionmaker
            from decimal import Decimal
            from datetime import date
            
            Session = sessionmaker(bind=self.engine)
            session = Session()
            
            try:
                # التحقق من وجود بيانات
                if session.query(Department).count() > 0:
                    logging.info("البيانات النموذجية موجودة بالفعل")
                    return True
                
                # إنشاء أقسام نموذجية
                departments = [
                    Department(
                        name="قسم تقنية المعلومات",
                        description="قسم مسؤول عن تقنية المعلومات والحاسوب",
                        manager_name="أحمد محمد السعيد"
                    ),
                    Department(
                        name="قسم المحاسبة",
                        description="قسم مسؤول عن المحاسبة والشؤون المالية",
                        manager_name="فاطمة علي أحمد"
                    ),
                    Department(
                        name="قسم الموارد البشرية",
                        description="قسم مسؤول عن شؤون الموظفين والتوظيف",
                        manager_name="محمد عبدالله الزهراني"
                    ),
                    Department(
                        name="قسم المبيعات",
                        description="قسم مسؤول عن المبيعات والتسويق",
                        manager_name="سارة خالد المطيري"
                    )
                ]
                
                for dept in departments:
                    session.add(dept)
                
                session.commit()
                
                # إنشاء عناوين وظيفية نموذجية
                job_titles = [
                    JobTitle(
                        title="مطور برمجيات",
                        description="تطوير وصيانة التطبيقات والأنظمة البرمجية",
                        base_salary=Decimal("6000.00")
                    ),
                    JobTitle(
                        title="محاسب",
                        description="إدارة الحسابات والمعاملات المالية",
                        base_salary=Decimal("4500.00")
                    ),
                    JobTitle(
                        title="أخصائي موارد بشرية",
                        description="إدارة شؤون الموظفين والتوظيف",
                        base_salary=Decimal("5000.00")
                    ),
                    JobTitle(
                        title="مندوب مبيعات",
                        description="تسويق وبيع المنتجات والخدمات",
                        base_salary=Decimal("3500.00")
                    ),
                    JobTitle(
                        title="مدير قسم",
                        description="إدارة وتنسيق أعمال القسم",
                        base_salary=Decimal("8000.00")
                    )
                ]
                
                for job_title in job_titles:
                    session.add(job_title)
                
                session.commit()
                
                # إنشاء موظفين نموذجيين
                employees = [
                    Employee(
                        employee_number="EMP001",
                        full_name="أحمد محمد السعيد",
                        national_id="1234567890",
                        phone="0501234567",
                        email="<EMAIL>",
                        hire_date=date(2023, 1, 15),
                        basic_salary=Decimal("8000.00"),
                        employment_status=EmploymentStatus.ACTIVE,
                        department_id=1,  # تقنية المعلومات
                        job_title_id=5    # مدير قسم
                    ),
                    Employee(
                        employee_number="EMP002",
                        full_name="فاطمة علي أحمد",
                        national_id="2345678901",
                        phone="0509876543",
                        email="<EMAIL>",
                        hire_date=date(2023, 2, 1),
                        basic_salary=Decimal("8000.00"),
                        employment_status=EmploymentStatus.ACTIVE,
                        department_id=2,  # المحاسبة
                        job_title_id=5    # مدير قسم
                    ),
                    Employee(
                        employee_number="EMP003",
                        full_name="محمد عبدالله الزهراني",
                        national_id="3456789012",
                        phone="0551234567",
                        email="<EMAIL>",
                        hire_date=date(2023, 1, 10),
                        basic_salary=Decimal("6000.00"),
                        employment_status=EmploymentStatus.ACTIVE,
                        department_id=1,  # تقنية المعلومات
                        job_title_id=1    # مطور برمجيات
                    ),
                    Employee(
                        employee_number="EMP004",
                        full_name="سارة خالد المطيري",
                        national_id="4567890123",
                        phone="0559876543",
                        email="<EMAIL>",
                        hire_date=date(2023, 3, 1),
                        basic_salary=Decimal("4500.00"),
                        employment_status=EmploymentStatus.ACTIVE,
                        department_id=2,  # المحاسبة
                        job_title_id=2    # محاسب
                    ),
                    Employee(
                        employee_number="EMP005",
                        full_name="عبدالرحمن أحمد القحطاني",
                        national_id="5678901234",
                        phone="0561234567",
                        email="<EMAIL>",
                        hire_date=date(2023, 2, 15),
                        basic_salary=Decimal("5000.00"),
                        employment_status=EmploymentStatus.ACTIVE,
                        department_id=3,  # الموارد البشرية
                        job_title_id=3    # أخصائي موارد بشرية
                    )
                ]
                
                for employee in employees:
                    session.add(employee)
                
                session.commit()
                
                logging.info("تم إدراج البيانات النموذجية بنجاح")
                return True
                
            except Exception as e:
                session.rollback()
                raise e
            finally:
                session.close()
                
        except Exception as e:
            logging.error(f"فشل في إدراج البيانات النموذجية: {e}")
            return False
            
    def setup_database(self):
        """إعداد قاعدة البيانات الكامل"""
        logging.info("بدء إعداد قاعدة البيانات...")
        
        try:
            # 1. إنشاء قاعدة البيانات إذا لم تكن موجودة
            if not self.create_database_if_not_exists():
                return False
                
            # 2. إنشاء محرك قاعدة البيانات
            if not self.create_engine():
                return False
                
            # 3. اختبار الاتصال
            if not self.test_connection():
                return False
                
            # 4. إنشاء جدول تتبع الترحيلات
            if not self.create_migrations_table():
                return False
                
            # 5. إنشاء جميع الجداول
            if not self.create_all_tables():
                return False
                
            # 6. إدراج البيانات النموذجية
            if not self.insert_sample_data():
                logging.warning("فشل في إدراج البيانات النموذجية، ولكن قاعدة البيانات جاهزة")
                
            logging.info("تم إعداد قاعدة البيانات بنجاح!")
            return True
            
        except Exception as e:
            logging.error(f"فشل في إعداد قاعدة البيانات: {e}")
            return False
            
    def reset_database(self):
        """إعادة تعيين قاعدة البيانات (حذف جميع الجداول وإعادة إنشائها)"""
        logging.warning("بدء إعادة تعيين قاعدة البيانات...")
        
        try:
            if not self.engine:
                if not self.create_engine():
                    return False
                    
            # حذف جميع الجداول
            Base.metadata.drop_all(self.engine)
            logging.info("تم حذف جميع الجداول")
            
            # إعادة إنشاء قاعدة البيانات
            return self.setup_database()
            
        except Exception as e:
            logging.error(f"فشل في إعادة تعيين قاعدة البيانات: {e}")
            return False


def setup_database():
    """دالة مساعدة لإعداد قاعدة البيانات"""
    migration_manager = MigrationManager()
    return migration_manager.setup_database()


def reset_database():
    """دالة مساعدة لإعادة تعيين قاعدة البيانات"""
    migration_manager = MigrationManager()
    return migration_manager.reset_database()


if __name__ == "__main__":
    # إعداد التسجيل
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # تشغيل إعداد قاعدة البيانات
    if setup_database():
        print("✅ تم إعداد قاعدة البيانات بنجاح!")
    else:
        print("❌ فشل في إعداد قاعدة البيانات!")
        exit(1)
