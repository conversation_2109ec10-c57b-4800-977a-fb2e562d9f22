"""
تحسينات الواجهة الشاملة
Comprehensive UI Improvements
"""

from PySide6.QtWidgets import (
    QWidget, QApplication, QPushButton, QLabel, QLineEdit, QComboBox,
    QTableWidget, QTextEdit, QPlainTextEdit, QGroupBox, QFrame,
    QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout, QSizePolicy
)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QFont, QPalette, QColor


class UIStyleManager:
    """مدير أنماط الواجهة"""
    
    # ألوان النظام
    COLORS = {
        'primary': '#2E86AB',
        'secondary': '#A23B72', 
        'success': '#28A745',
        'danger': '#DC3545',
        'warning': '#FFC107',
        'info': '#17A2B8',
        'light': '#F8F9FA',
        'dark': '#343A40',
        'white': '#FFFFFF',
        'gray_100': '#F8F9FA',
        'gray_200': '#E9ECEF',
        'gray_300': '#DEE2E6',
        'gray_400': '#CED4DA',
        'gray_500': '#ADB5BD',
        'gray_600': '#6C757D',
        'gray_700': '#495057',
        'gray_800': '#343A40',
        'gray_900': '#212529'
    }
    
    # أحجام الخطوط
    FONT_SIZES = {
        'xs': 8,
        'sm': 9,
        'base': 10,
        'lg': 12,
        'xl': 14,
        '2xl': 16,
        '3xl': 18,
        '4xl': 20,
        '5xl': 24
    }
    
    @classmethod
    def get_button_style(cls, button_type: str = 'default') -> str:
        """الحصول على نمط الزر"""
        base_style = """
            QPushButton {
                border: 1px solid #CED4DA;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 10px;
                min-height: 20px;
                text-align: center;
            }
            QPushButton:hover {
                border-color: #ADB5BD;
                background-color: #F8F9FA;
            }
            QPushButton:pressed {
                background-color: #E9ECEF;
            }
            QPushButton:disabled {
                color: #6C757D;
                background-color: #E9ECEF;
                border-color: #DEE2E6;
            }
        """
        
        if button_type == 'primary':
            return base_style + f"""
                QPushButton {{
                    background-color: {cls.COLORS['primary']};
                    color: white;
                    border-color: {cls.COLORS['primary']};
                }}
                QPushButton:hover {{
                    background-color: #1F5F7A;
                    border-color: #1F5F7A;
                }}
                QPushButton:pressed {{
                    background-color: #1A5269;
                }}
            """
        elif button_type == 'success':
            return base_style + f"""
                QPushButton {{
                    background-color: {cls.COLORS['success']};
                    color: white;
                    border-color: {cls.COLORS['success']};
                }}
                QPushButton:hover {{
                    background-color: #218838;
                    border-color: #218838;
                }}
                QPushButton:pressed {{
                    background-color: #1E7E34;
                }}
            """
        elif button_type == 'danger':
            return base_style + f"""
                QPushButton {{
                    background-color: {cls.COLORS['danger']};
                    color: white;
                    border-color: {cls.COLORS['danger']};
                }}
                QPushButton:hover {{
                    background-color: #C82333;
                    border-color: #C82333;
                }}
                QPushButton:pressed {{
                    background-color: #BD2130;
                }}
            """
        elif button_type == 'warning':
            return base_style + f"""
                QPushButton {{
                    background-color: {cls.COLORS['warning']};
                    color: #212529;
                    border-color: {cls.COLORS['warning']};
                }}
                QPushButton:hover {{
                    background-color: #E0A800;
                    border-color: #E0A800;
                }}
                QPushButton:pressed {{
                    background-color: #D39E00;
                }}
            """
        
        return base_style
    
    @classmethod
    def get_input_style(cls) -> str:
        """الحصول على نمط حقول الإدخال"""
        return f"""
            QLineEdit, QTextEdit, QPlainTextEdit {{
                border: 1px solid {cls.COLORS['gray_300']};
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 10px;
                background-color: white;
            }}
            QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
                border-color: {cls.COLORS['primary']};
                outline: none;
            }}
            QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {{
                background-color: {cls.COLORS['gray_100']};
                color: {cls.COLORS['gray_600']};
            }}
        """
    
    @classmethod
    def get_combobox_style(cls) -> str:
        """الحصول على نمط القوائم المنسدلة"""
        return f"""
            QComboBox {{
                border: 1px solid {cls.COLORS['gray_300']};
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 10px;
                background-color: white;
                min-width: 100px;
            }}
            QComboBox:focus {{
                border-color: {cls.COLORS['primary']};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {cls.COLORS['gray_600']};
                margin-right: 5px;
            }}
            QComboBox QAbstractItemView {{
                border: 1px solid {cls.COLORS['gray_300']};
                background-color: white;
                selection-background-color: {cls.COLORS['primary']};
            }}
        """
    
    @classmethod
    def get_table_style(cls) -> str:
        """الحصول على نمط الجداول"""
        return f"""
            QTableWidget {{
                gridline-color: {cls.COLORS['gray_200']};
                background-color: white;
                alternate-background-color: {cls.COLORS['gray_100']};
                selection-background-color: {cls.COLORS['primary']};
                selection-color: white;
                font-size: 9px;
            }}
            QTableWidget::item {{
                padding: 8px;
                border: none;
            }}
            QTableWidget::item:selected {{
                background-color: {cls.COLORS['primary']};
                color: white;
            }}
            QHeaderView::section {{
                background-color: {cls.COLORS['gray_200']};
                padding: 8px;
                border: 1px solid {cls.COLORS['gray_300']};
                font-weight: bold;
                font-size: 9px;
            }}
        """
    
    @classmethod
    def get_groupbox_style(cls) -> str:
        """الحصول على نمط مجموعات العناصر"""
        return f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 11px;
                border: 2px solid {cls.COLORS['gray_300']};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: white;
            }}
        """
    
    @classmethod
    def get_label_style(cls) -> str:
        """الحصول على نمط التسميات"""
        return f"""
            QLabel {{
                font-size: 10px;
                color: {cls.COLORS['gray_800']};
            }}
            QLabel[class="title"] {{
                font-size: 16px;
                font-weight: bold;
                color: {cls.COLORS['primary']};
            }}
            QLabel[class="subtitle"] {{
                font-size: 12px;
                font-weight: bold;
                color: {cls.COLORS['gray_700']};
            }}
        """


def apply_modern_style(widget: QWidget) -> None:
    """تطبيق النمط الحديث على العنصر"""
    style_manager = UIStyleManager()
    
    # تطبيق الأنماط المختلفة
    combined_style = (
        style_manager.get_button_style() +
        style_manager.get_input_style() +
        style_manager.get_combobox_style() +
        style_manager.get_table_style() +
        style_manager.get_groupbox_style() +
        style_manager.get_label_style()
    )
    
    widget.setStyleSheet(combined_style)


def setup_button_styles(widget: QWidget) -> None:
    """إعداد أنماط الأزرار حسب الفئة"""
    style_manager = UIStyleManager()
    
    buttons = widget.findChildren(QPushButton)
    for button in buttons:
        object_name = button.objectName()
        
        if 'success' in object_name or 'add' in object_name or 'save' in object_name:
            button.setStyleSheet(style_manager.get_button_style('success'))
        elif 'danger' in object_name or 'delete' in object_name or 'remove' in object_name:
            button.setStyleSheet(style_manager.get_button_style('danger'))
        elif 'warning' in object_name or 'edit' in object_name:
            button.setStyleSheet(style_manager.get_button_style('warning'))
        elif 'primary' in object_name or 'main' in object_name:
            button.setStyleSheet(style_manager.get_button_style('primary'))
        else:
            button.setStyleSheet(style_manager.get_button_style())


def improve_layout_spacing(widget: QWidget) -> None:
    """تحسين المسافات في التخطيط"""
    # البحث عن جميع التخطيطات
    layouts = widget.findChildren(QVBoxLayout) + widget.findChildren(QHBoxLayout) + widget.findChildren(QGridLayout)
    
    for layout in layouts:
        # تحسين المسافات
        layout.setSpacing(12)
        layout.setContentsMargins(16, 16, 16, 16)


def setup_responsive_sizing(widget: QWidget) -> None:
    """إعداد الأحجام المتجاوبة"""
    # تحسين سياسة الحجم للعناصر المختلفة
    buttons = widget.findChildren(QPushButton)
    for button in buttons:
        button.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        button.setMinimumHeight(36)
    
    line_edits = widget.findChildren(QLineEdit)
    for line_edit in line_edits:
        line_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        line_edit.setMinimumHeight(32)
    
    combo_boxes = widget.findChildren(QComboBox)
    for combo in combo_boxes:
        combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        combo.setMinimumHeight(32)
    
    tables = widget.findChildren(QTableWidget)
    for table in tables:
        table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)


def fix_common_ui_issues(widget: QWidget) -> None:
    """إصلاح المشاكل الشائعة في الواجهة"""
    # إصلاح مشكلة عدم ظهور النصوص
    labels = widget.findChildren(QLabel)
    for label in labels:
        if not label.text():
            continue
        label.setWordWrap(True)
        label.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Minimum)
    
    # إصلاح مشكلة الأزرار المعطلة
    buttons = widget.findChildren(QPushButton)
    for button in buttons:
        if not button.isEnabled():
            button.setToolTip("هذا الزر معطل حالياً")
    
    # إصلاح مشكلة الجداول الفارغة
    tables = widget.findChildren(QTableWidget)
    for table in tables:
        if table.rowCount() == 0:
            table.setMinimumHeight(200)


def apply_comprehensive_ui_improvements(widget: QWidget) -> None:
    """تطبيق جميع تحسينات الواجهة"""
    # تطبيق النمط الحديث
    apply_modern_style(widget)
    
    # إعداد أنماط الأزرار
    setup_button_styles(widget)
    
    # تحسين المسافات
    improve_layout_spacing(widget)
    
    # إعداد الأحجام المتجاوبة
    setup_responsive_sizing(widget)
    
    # إصلاح المشاكل الشائعة
    fix_common_ui_issues(widget)
    
    # تطبيق تحسينات العربية
    from .ui_helpers import improve_arabic_display
    improve_arabic_display(widget)
