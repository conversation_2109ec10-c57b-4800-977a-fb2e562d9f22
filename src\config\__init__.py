"""
إعدادات التطبيق
Application Configuration
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional

# مسارات المشروع
PROJECT_ROOT = Path(__file__).parent.parent.parent
CONFIG_DIR = PROJECT_ROOT / "config"
STYLES_DIR = PROJECT_ROOT / "src" / "styles"
ASSETS_DIR = PROJECT_ROOT / "src" / "assets"
LOGS_DIR = PROJECT_ROOT / "logs"
BACKUP_DIR = PROJECT_ROOT / "backups"

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [CONFIG_DIR, LOGS_DIR, BACKUP_DIR]:
    directory.mkdir(exist_ok=True)

# إعدادات قاعدة البيانات الافتراضية
DEFAULT_DATABASE_CONFIG = {
    "type": "postgresql",
    "host": os.getenv("DB_HOST", "localhost"),
    "port": int(os.getenv("DB_PORT", "5432")),
    "database": os.getenv("DB_NAME", "hr_system"),
    "username": os.getenv("DB_USER", "postgres"),
    "password": os.getenv("DB_PASSWORD", "19952010"),
    "pool_size": 10,
    "max_overflow": 20,
    "pool_timeout": 30,
    "pool_recycle": 3600,
    "echo": False,
}

# إعدادات التطبيق الافتراضية
DEFAULT_APP_CONFIG = {
    "name": "نظام إدارة شؤون الموظفين",
    "version": "1.0.0",
    "debug": False,
    "language": "ar",
    "theme": "light",
    "font_size": 10,
    "window_size": [1200, 800],
    "window_position": "center",
}

# إعدادات النسخ الاحتياطي الافتراضية
DEFAULT_BACKUP_CONFIG = {
    "auto_backup": True,
    "backup_interval": "daily",
    "backup_time": "02:00",
    "max_backups": 30,
    "backup_path": str(BACKUP_DIR),
    "compress": True,
}

# إعدادات التقارير الافتراضية
DEFAULT_REPORTS_CONFIG = {
    "default_format": "pdf",
    "page_size": "A4",
    "orientation": "portrait",
    "font_name": "Arial",
    "font_size": 10,
    "margin": {
        "top": 2.5,
        "bottom": 2.5,
        "left": 2.5,
        "right": 2.5
    },
    "company_name": "اسم الشركة",
    "company_address": "عنوان الشركة",
}

# إعدادات واجهة المستخدم الافتراضية
DEFAULT_UI_CONFIG = {
    "theme": "light",
    "language": "ar",
    "font_family": "Arial",
    "font_size": 10,
    "rtl_layout": True,
    "show_tooltips": True,
    "animation_enabled": True,
    "header_height": 60,
    "sidebar_width": 250,
}

# جميع الإعدادات الافتراضية
DEFAULT_CONFIGS = {
    "database": DEFAULT_DATABASE_CONFIG,
    "app": DEFAULT_APP_CONFIG,
    "backup": DEFAULT_BACKUP_CONFIG,
    "reports": DEFAULT_REPORTS_CONFIG,
    "ui": DEFAULT_UI_CONFIG,
}

# متغير لحفظ الإعدادات المحملة
_loaded_configs: Optional[Dict[str, Any]] = None


def load_config_file() -> Dict[str, Any]:
    """تحميل ملف الإعدادات"""
    config_file = CONFIG_DIR / "config.json"
    
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            print(f"خطأ في تحميل ملف الإعدادات: {e}")
            return {}
    
    return {}


def save_config_file(configs: Dict[str, Any]) -> bool:
    """حفظ ملف الإعدادات"""
    config_file = CONFIG_DIR / "config.json"
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(configs, f, ensure_ascii=False, indent=2)
        return True
    except IOError as e:
        print(f"خطأ في حفظ ملف الإعدادات: {e}")
        return False


def get_config(section: str = None) -> Dict[str, Any]:
    """
    الحصول على إعدادات قسم محدد أو جميع الإعدادات
    
    Args:
        section: اسم القسم (database, app, backup, reports, ui)
                إذا كان None، سيتم إرجاع جميع الإعدادات
    
    Returns:
        dict: إعدادات القسم المطلوب أو جميع الإعدادات
    """
    global _loaded_configs
    
    # تحميل الإعدادات إذا لم تكن محملة
    if _loaded_configs is None:
        file_configs = load_config_file()
        _loaded_configs = {}
        
        # دمج الإعدادات الافتراضية مع إعدادات الملف
        for section_name, default_config in DEFAULT_CONFIGS.items():
            _loaded_configs[section_name] = {
                **default_config,
                **file_configs.get(section_name, {})
            }
    
    if section is None:
        return _loaded_configs
    
    return _loaded_configs.get(section, {})


def update_config(section: str, key: str, value: Any) -> bool:
    """
    تحديث إعداد محدد
    
    Args:
        section: اسم القسم
        key: مفتاح الإعداد
        value: القيمة الجديدة
    
    Returns:
        bool: True إذا تم التحديث بنجاح
    """
    global _loaded_configs
    
    # تحميل الإعدادات إذا لم تكن محملة
    if _loaded_configs is None:
        get_config()
    
    if section not in _loaded_configs:
        _loaded_configs[section] = {}
    
    _loaded_configs[section][key] = value
    
    # حفظ التغييرات في الملف
    return save_config_file(_loaded_configs)


def get_database_url() -> str:
    """
    إنشاء URL الاتصال بقاعدة البيانات
    
    Returns:
        str: URL الاتصال
    """
    db_config = get_config("database")
    
    if db_config["type"] == "postgresql":
        return (
            f"postgresql://{db_config['username']}:{db_config['password']}"
            f"@{db_config['host']}:{db_config['port']}/{db_config['database']}"
        )
    elif db_config["type"] == "sqlite":
        return f"sqlite:///{db_config['database']}"
    else:
        raise ValueError(f"نوع قاعدة بيانات غير مدعوم: {db_config['type']}")


def reset_config() -> bool:
    """
    إعادة تعيين الإعدادات إلى القيم الافتراضية
    
    Returns:
        bool: True إذا تم إعادة التعيين بنجاح
    """
    global _loaded_configs
    _loaded_configs = None
    
    config_file = CONFIG_DIR / "config.json"
    if config_file.exists():
        try:
            config_file.unlink()
        except OSError:
            pass
    
    return save_config_file(DEFAULT_CONFIGS)


def create_default_config_file() -> bool:
    """
    إنشاء ملف إعدادات افتراضي

    Returns:
        bool: True إذا تم الإنشاء بنجاح
    """
    config_file = CONFIG_DIR / "config.json"

    if not config_file.exists():
        return save_config_file(DEFAULT_CONFIGS)

    return True


def ensure_directories() -> None:
    """التأكد من وجود جميع المجلدات المطلوبة"""
    directories = [CONFIG_DIR, LOGS_DIR, BACKUP_DIR]

    for directory in directories:
        directory.mkdir(exist_ok=True, parents=True)


# إنشاء ملف الإعدادات الافتراضي عند الاستيراد
create_default_config_file()


# تصدير الدوال والثوابت المهمة
__all__ = [
    "get_config",
    "update_config",
    "get_database_url",
    "reset_config",
    "ensure_directories",
    "PROJECT_ROOT",
    "CONFIG_DIR",
    "STYLES_DIR",
    "ASSETS_DIR",
    "LOGS_DIR",
    "BACKUP_DIR"
]
