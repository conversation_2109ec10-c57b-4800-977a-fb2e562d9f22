#!/usr/bin/env python3
"""
اختبار سريع للنظام
Quick System Test
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """اختبار الاستيرادات"""
    print("🔍 اختبار الاستيرادات...")
    
    try:
        from src.config import get_config
        print("   ✅ تم استيراد get_config")
        
        from src.database import db_manager
        print("   ✅ تم استيراد db_manager")
        
        from src.views import MainWindow
        print("   ✅ تم استيراد MainWindow")
        
        return True
    except Exception as e:
        print(f"   ❌ خطأ في الاستيراد: {e}")
        return False

def test_config():
    """اختبار الإعدادات"""
    print("\n⚙️ اختبار الإعدادات...")
    
    try:
        from src.config import get_config
        
        app_config = get_config("app")
        ui_config = get_config("ui")
        
        print(f"   اسم التطبيق: {app_config['name']}")
        print(f"   حجم الخط: {ui_config.get('font_size', 10)}")
        print("   ✅ الإعدادات تعمل بشكل صحيح")
        
        return True
    except Exception as e:
        print(f"   ❌ خطأ في الإعدادات: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n💾 اختبار قاعدة البيانات...")
    
    try:
        from src.database import db_manager
        
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        print("   ✅ تم تهيئة قاعدة البيانات")
        
        # اختبار الاتصال
        if db_manager.test_connection():
            print("   ✅ الاتصال بقاعدة البيانات يعمل")
        else:
            print("   ⚠️ مشكلة في الاتصال بقاعدة البيانات")
        
        return True
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار سريع للنظام")
    print("=" * 40)
    
    tests = [
        ("الاستيرادات", test_imports),
        ("الإعدادات", test_config),
        ("قاعدة البيانات", test_database)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للتشغيل")
        print("\n🚀 لتشغيل النظام:")
        print("python run_hr_system.py")
        return 0
    else:
        print("⚠️ بعض الاختبارات فشلت")
        return 1

if __name__ == "__main__":
    sys.exit(main())
