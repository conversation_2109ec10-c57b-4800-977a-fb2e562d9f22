[tool.poetry]
name = "hr-management-system"
version = "1.0.0"
description = "نظام إدارة شؤون الموظفين - HR Management System"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.9"
PySide6 = "^6.6.0"
SQLAlchemy = "^2.0.0"
psycopg2-binary = "^2.9.0"
alembic = "^1.12.0"
openpyxl = "^3.1.0"
reportlab = "^4.0.0"
python-dateutil = "^2.8.0"
schedule = "^1.2.0"
cryptography = "^41.0.0"
Pillow = "^10.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-qt = "^4.2.0"
black = "^23.0.0"
flake8 = "^6.0.0"
mypy = "^1.5.0"
pytest-cov = "^4.1.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
