<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1200</width>
    <height>800</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>🏢 نظام إدارة الموارد البشرية المتقدم</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QMainWindow {
    background-color: #f8f9fa;
}

QMenuBar {
    background-color: #2c3e50;
    color: white;
    border: none;
    padding: 5px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 12px;
    margin: 2px;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background-color: #34495e;
}

QStatusBar {
    background-color: #34495e;
    color: white;
    border-top: 2px solid #3498db;
    padding: 5px;
}</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="main_layout">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QFrame" name="sidebar_frame">
      <property name="minimumSize">
       <size>
        <width>250</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>250</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QFrame {
    background-color: #2c3e50;
    border: none;
}</string>
      </property>
      <layout class="QVBoxLayout" name="sidebar_layout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QFrame" name="logo_frame">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>80</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QFrame {
    background-color: #34495e;
    border-bottom: 2px solid #3498db;
}</string>
         </property>
         <layout class="QHBoxLayout" name="logo_layout">
          <item>
           <widget class="QLabel" name="logo_label">
            <property name="text">
             <string>🏢 نظام الموارد البشرية</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: white;
    font-size: 16px;
    font-weight: bold;
    padding: 10px;
}</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QScrollArea" name="menu_scroll_area">
         <property name="styleSheet">
          <string notr="true">QScrollArea {
    border: none;
    background-color: transparent;
}</string>
         </property>
         <property name="widgetResizable">
          <bool>true</bool>
         </property>
         <widget class="QWidget" name="menu_content">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>248</width>
            <height>718</height>
           </rect>
          </property>
          <layout class="QVBoxLayout" name="menu_layout">
           <property name="spacing">
            <number>5</number>
           </property>
           <property name="leftMargin">
            <number>10</number>
           </property>
           <property name="topMargin">
            <number>20</number>
           </property>
           <property name="rightMargin">
            <number>10</number>
           </property>
           <property name="bottomMargin">
            <number>20</number>
           </property>
           <item>
            <widget class="QPushButton" name="dashboard_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string>📊 لوحة التحكم</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #2980b9;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="employees_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string>👥 إدارة الموظفين</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    border-radius: 8px;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #34495e;
    color: #3498db;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="departments_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string>🏢 إدارة الأقسام</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    border-radius: 8px;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #34495e;
    color: #3498db;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="salaries_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string>💰 إدارة الرواتب</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    border-radius: 8px;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #34495e;
    color: #3498db;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="reports_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string>📊 التقارير</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    border-radius: 8px;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #34495e;
    color: #3498db;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="menu_spacer">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="content_frame">
      <property name="styleSheet">
       <string notr="true">QFrame {
    background-color: #ecf0f1;
    border: none;
}</string>
      </property>
      <layout class="QVBoxLayout" name="content_layout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QStackedWidget" name="content_stack">
         <widget class="QWidget" name="dashboard_page">
          <layout class="QVBoxLayout" name="dashboard_layout">
           <item>
            <widget class="QLabel" name="dashboard_label">
             <property name="text">
              <string>📊 لوحة التحكم الرئيسية</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
             <property name="styleSheet">
              <string notr="true">QLabel {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    padding: 20px;
}</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1400</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
