"""
إدارة الاتصال بقاعدة البيانات
Database Connection Management
"""

import logging
from contextlib import contextmanager
from typing import Generator, Optional
from sqlalchemy import create_engine, event, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from ..config import get_database_url
from ..models import Base

logger = logging.getLogger(__name__)


class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._initialized = False
    
    def initialize(self, database_url: str = None) -> None:
        """تهيئة قاعدة البيانات"""
        if self._initialized:
            return
        
        try:
            # إنشاء محرك قاعدة البيانات
            url = database_url or get_database_url()
            self.engine = create_engine(
                url,
                poolclass=QueuePool,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                echo=False  # تغيير إلى True لرؤية استعلامات SQL
            )
            
            # إعداد مستمع للأحداث
            event.listen(self.engine, "connect", self._on_connect)
            
            # إنشاء مصنع الجلسات
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            self._initialized = True
            logger.info("تم تهيئة قاعدة البيانات بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
            raise
    
    def _on_connect(self, dbapi_connection, connection_record):
        """مستمع أحداث الاتصال"""
        # تعيين إعدادات PostgreSQL (فقط لـ PostgreSQL)
        if hasattr(dbapi_connection, 'server_version'):  # PostgreSQL
            cursor = dbapi_connection.cursor()
            try:
                cursor.execute("SET timezone TO 'UTC'")
            finally:
                cursor.close()
    
    def create_tables(self) -> None:
        """إنشاء جميع الجداول"""
        if not self._initialized:
            raise RuntimeError("يجب تهيئة قاعدة البيانات أولاً")
        
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("تم إنشاء جميع الجداول بنجاح")
        except Exception as e:
            logger.error(f"خطأ في إنشاء الجداول: {e}")
            raise
    
    def drop_tables(self) -> None:
        """حذف جميع الجداول"""
        if not self._initialized:
            raise RuntimeError("يجب تهيئة قاعدة البيانات أولاً")
        
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.info("تم حذف جميع الجداول بنجاح")
        except Exception as e:
            logger.error(f"خطأ في حذف الجداول: {e}")
            raise
    
    def get_session(self) -> Session:
        """الحصول على جلسة قاعدة بيانات جديدة"""
        if not self._initialized:
            raise RuntimeError("يجب تهيئة قاعدة البيانات أولاً")
        
        return self.SessionLocal()
    
    @contextmanager
    def session_scope(self) -> Generator[Session, None, None]:
        """مدير سياق للجلسات مع إدارة تلقائية للمعاملات"""
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def test_connection(self) -> bool:
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            session = self.SessionLocal()
            try:
                session.execute(text("SELECT 1"))
                session.commit()
                return True
            finally:
                session.close()
        except Exception as e:
            logger.error(f"فشل اختبار الاتصال: {e}")
            return False
    
    def close(self) -> None:
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.engine:
            self.engine.dispose()
            logger.info("تم إغلاق الاتصال بقاعدة البيانات")


# إنشاء مثيل مدير قاعدة البيانات
db_manager = DatabaseManager()


def get_db_session() -> Session:
    """دالة مساعدة للحصول على جلسة قاعدة البيانات"""
    return db_manager.get_session()


@contextmanager
def get_db_session_context() -> Generator[Session, None, None]:
    """دالة مساعدة للحصول على جلسة قاعدة البيانات مع إدارة السياق"""
    with db_manager.session_scope() as session:
        yield session
