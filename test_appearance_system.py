#!/usr/bin/env python3
"""
اختبار نظام المظهر المتقدم
Test Advanced Appearance System
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget
from PySide6.QtWidgets import QPushButton, QLabel, QLineEdit, QComboBox, QTableWidget
from PySide6.QtWidgets import QGroupBox, QCheckBox, QTabWidget, QTextEdit, QSlider
from PySide6.QtWidgets import QTableWidgetItem, QHeaderView, QListWidget, QTreeWidget
from PySide6.QtWidgets import QTreeWidgetItem, QSpinBox, QProgressBar, Q<PERSON>enuBar, QMenu
from PySide6.QtWidgets import QStatusBar, QToolBar
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QAction


def create_comprehensive_test_window():
    """إنشاء نافذة اختبار شاملة لجميع العناصر"""
    
    class ComprehensiveTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار نظام المظهر المتقدم - جميع العناصر")
            self.setGeometry(100, 100, 1200, 800)
            
            # إنشاء شريط القوائم
            self.create_menu_bar()
            
            # إنشاء شريط الأدوات
            self.create_toolbar()
            
            # إنشاء شريط الحالة
            self.create_status_bar()
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            
            # شريط التحكم في المظهر
            control_panel = self.create_control_panel()
            main_layout.addWidget(control_panel)
            
            # التبويبات للعناصر المختلفة
            tabs = QTabWidget()
            
            # تبويب الأزرار والحقول
            buttons_tab = self.create_buttons_tab()
            tabs.addTab(buttons_tab, "الأزرار والحقول")
            
            # تبويب الجداول والقوائم
            tables_tab = self.create_tables_tab()
            tabs.addTab(tables_tab, "الجداول والقوائم")
            
            # تبويب العناصر المتقدمة
            advanced_tab = self.create_advanced_tab()
            tabs.addTab(advanced_tab, "عناصر متقدمة")
            
            main_layout.addWidget(tabs)
            
        def create_menu_bar(self):
            """إنشاء شريط القوائم"""
            menubar = self.menuBar()
            
            # قائمة الملف
            file_menu = menubar.addMenu("ملف")
            file_menu.addAction("جديد")
            file_menu.addAction("فتح")
            file_menu.addAction("حفظ")
            file_menu.addSeparator()
            file_menu.addAction("خروج")
            
            # قائمة التحرير
            edit_menu = menubar.addMenu("تحرير")
            edit_menu.addAction("نسخ")
            edit_menu.addAction("لصق")
            edit_menu.addAction("قص")
            
            # قائمة المظهر
            appearance_menu = menubar.addMenu("المظهر")
            appearance_menu.addAction("فاتح")
            appearance_menu.addAction("داكن")
            appearance_menu.addAction("إعدادات متقدمة")
        
        def create_toolbar(self):
            """إنشاء شريط الأدوات"""
            toolbar = self.addToolBar("الأدوات الرئيسية")
            
            toolbar.addAction("جديد")
            toolbar.addAction("فتح")
            toolbar.addAction("حفظ")
            toolbar.addSeparator()
            toolbar.addAction("نسخ")
            toolbar.addAction("لصق")
        
        def create_status_bar(self):
            """إنشاء شريط الحالة"""
            status_bar = self.statusBar()
            status_bar.showMessage("جاهز - اختبار نظام المظهر المتقدم")
        
        def create_control_panel(self) -> QWidget:
            """إنشاء لوحة التحكم في المظهر"""
            panel = QGroupBox("التحكم في المظهر")
            layout = QHBoxLayout(panel)
            
            # أزرار تغيير المظهر
            light_btn = QPushButton("مظهر فاتح")
            dark_btn = QPushButton("مظهر داكن")
            settings_btn = QPushButton("إعدادات متقدمة")
            
            light_btn.clicked.connect(lambda: self.change_theme('light'))
            dark_btn.clicked.connect(lambda: self.change_theme('dark'))
            settings_btn.clicked.connect(self.open_appearance_settings)
            
            layout.addWidget(light_btn)
            layout.addWidget(dark_btn)
            layout.addWidget(settings_btn)
            
            # أزرار تغيير مخطط الألوان
            layout.addWidget(QLabel("مخطط الألوان:"))
            
            colors = [("أزرق", "blue"), ("أخضر", "green"), ("بنفسجي", "purple"), 
                     ("برتقالي", "orange"), ("أحمر", "red")]
            
            for name, scheme in colors:
                btn = QPushButton(name)
                btn.clicked.connect(lambda checked, s=scheme: self.change_color_scheme(s))
                layout.addWidget(btn)
            
            layout.addStretch()
            return panel
        
        def create_buttons_tab(self) -> QWidget:
            """إنشاء تبويب الأزرار والحقول"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # مجموعة الأزرار
            buttons_group = QGroupBox("أنواع الأزرار")
            buttons_layout = QHBoxLayout(buttons_group)
            
            primary_btn = QPushButton("زر أساسي")
            secondary_btn = QPushButton("زر ثانوي")
            secondary_btn.setObjectName("sidebar_button")
            success_btn = QPushButton("زر نجاح")
            success_btn.setObjectName("success_button")
            danger_btn = QPushButton("زر خطر")
            danger_btn.setObjectName("danger_button")
            disabled_btn = QPushButton("زر معطل")
            disabled_btn.setEnabled(False)
            
            buttons_layout.addWidget(primary_btn)
            buttons_layout.addWidget(secondary_btn)
            buttons_layout.addWidget(success_btn)
            buttons_layout.addWidget(danger_btn)
            buttons_layout.addWidget(disabled_btn)
            
            layout.addWidget(buttons_group)
            
            # مجموعة حقول الإدخال
            inputs_group = QGroupBox("حقول الإدخال")
            inputs_layout = QVBoxLayout(inputs_group)
            
            line_edit = QLineEdit("حقل نص عادي")
            combo_box = QComboBox()
            combo_box.addItems(["خيار 1", "خيار 2", "خيار 3", "خيار طويل جداً"])
            
            text_edit = QTextEdit()
            text_edit.setPlainText("منطقة نص متعددة الأسطر\nيمكن كتابة نص طويل هنا")
            text_edit.setMaximumHeight(100)
            
            inputs_layout.addWidget(QLabel("حقل نص:"))
            inputs_layout.addWidget(line_edit)
            inputs_layout.addWidget(QLabel("قائمة منسدلة:"))
            inputs_layout.addWidget(combo_box)
            inputs_layout.addWidget(QLabel("منطقة نص:"))
            inputs_layout.addWidget(text_edit)
            
            layout.addWidget(inputs_group)
            
            # مجموعة مربعات الاختيار
            checkboxes_group = QGroupBox("مربعات الاختيار")
            checkboxes_layout = QVBoxLayout(checkboxes_group)
            
            checkbox1 = QCheckBox("خيار أول")
            checkbox2 = QCheckBox("خيار ثاني")
            checkbox3 = QCheckBox("خيار ثالث")
            checkbox1.setChecked(True)
            checkbox2.setChecked(False)
            checkbox3.setChecked(True)
            
            checkboxes_layout.addWidget(checkbox1)
            checkboxes_layout.addWidget(checkbox2)
            checkboxes_layout.addWidget(checkbox3)
            
            layout.addWidget(checkboxes_group)
            
            return widget
        
        def create_tables_tab(self) -> QWidget:
            """إنشاء تبويب الجداول والقوائم"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # جدول
            table_group = QGroupBox("جدول البيانات")
            table_layout = QVBoxLayout(table_group)
            
            table = QTableWidget(5, 4)
            table.setHorizontalHeaderLabels(["الاسم", "العمر", "المدينة", "الوظيفة"])
            
            # ملء الجدول ببيانات تجريبية
            data = [
                ["أحمد محمد", "25", "بغداد", "مطور"],
                ["فاطمة علي", "30", "البصرة", "مصممة"],
                ["محمد حسن", "28", "أربيل", "محاسب"],
                ["زينب أحمد", "26", "الموصل", "طبيبة"],
                ["علي حسين", "32", "النجف", "مهندس"]
            ]
            
            for row, row_data in enumerate(data):
                for col, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(cell_data)
                    table.setItem(row, col, item)
            
            table.horizontalHeader().setStretchLastSection(True)
            table_layout.addWidget(table)
            layout.addWidget(table_group)
            
            # قائمة
            list_group = QGroupBox("قائمة العناصر")
            list_layout = QVBoxLayout(list_group)
            
            list_widget = QListWidget()
            list_items = ["عنصر أول", "عنصر ثاني", "عنصر ثالث", "عنصر رابع", "عنصر خامس"]
            list_widget.addItems(list_items)
            list_widget.setMaximumHeight(120)
            
            list_layout.addWidget(list_widget)
            layout.addWidget(list_group)
            
            return widget
        
        def create_advanced_tab(self) -> QWidget:
            """إنشاء تبويب العناصر المتقدمة"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # منزلقات
            sliders_group = QGroupBox("المنزلقات")
            sliders_layout = QVBoxLayout(sliders_group)
            
            slider1 = QSlider(Qt.Horizontal)
            slider1.setRange(0, 100)
            slider1.setValue(50)
            
            slider2 = QSlider(Qt.Horizontal)
            slider2.setRange(8, 72)
            slider2.setValue(16)
            
            sliders_layout.addWidget(QLabel("منزلق عام:"))
            sliders_layout.addWidget(slider1)
            sliders_layout.addWidget(QLabel("منزلق حجم الخط:"))
            sliders_layout.addWidget(slider2)
            
            layout.addWidget(sliders_group)
            
            # مربعات رقمية
            spinboxes_group = QGroupBox("المربعات الرقمية")
            spinboxes_layout = QHBoxLayout(spinboxes_group)
            
            spinbox1 = QSpinBox()
            spinbox1.setRange(0, 1000)
            spinbox1.setValue(100)
            
            spinbox2 = QSpinBox()
            spinbox2.setRange(8, 72)
            spinbox2.setValue(12)
            spinbox2.setSuffix(" px")
            
            spinboxes_layout.addWidget(QLabel("عدد:"))
            spinboxes_layout.addWidget(spinbox1)
            spinboxes_layout.addWidget(QLabel("حجم الخط:"))
            spinboxes_layout.addWidget(spinbox2)
            
            layout.addWidget(spinboxes_group)
            
            # شريط التقدم
            progress_group = QGroupBox("شريط التقدم")
            progress_layout = QVBoxLayout(progress_group)
            
            progress_bar = QProgressBar()
            progress_bar.setRange(0, 100)
            progress_bar.setValue(65)
            
            progress_layout.addWidget(progress_bar)
            layout.addWidget(progress_group)
            
            return widget
        
        def change_theme(self, theme_mode: str):
            """تغيير وضع المظهر"""
            try:
                from src.utils.appearance_manager import get_appearance_manager
                appearance_manager = get_appearance_manager()
                appearance_manager.set_theme_mode(theme_mode)
                print(f"تم تغيير المظهر إلى: {theme_mode}")
            except Exception as e:
                print(f"خطأ في تغيير المظهر: {e}")
        
        def change_color_scheme(self, color_scheme: str):
            """تغيير مخطط الألوان"""
            try:
                from src.utils.appearance_manager import get_appearance_manager
                appearance_manager = get_appearance_manager()
                appearance_manager.set_color_scheme(color_scheme)
                print(f"تم تغيير مخطط الألوان إلى: {color_scheme}")
            except Exception as e:
                print(f"خطأ في تغيير مخطط الألوان: {e}")
        
        def open_appearance_settings(self):
            """فتح نافذة إعدادات المظهر"""
            try:
                from src.views.appearance_settings import AppearanceSettingsDialog
                dialog = AppearanceSettingsDialog(self)
                dialog.exec()
            except Exception as e:
                print(f"خطأ في فتح إعدادات المظهر: {e}")
    
    return ComprehensiveTestWindow()


def main():
    """الدالة الرئيسية"""
    print("🎨 اختبار نظام المظهر المتقدم")
    print("=" * 60)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تهيئة نظام المظهر
        try:
            from src.utils.appearance_manager import get_appearance_manager
            appearance_manager = get_appearance_manager()
            appearance_manager.load_settings()
            print("✅ تم تهيئة نظام المظهر")
        except Exception as e:
            print(f"⚠️ فشل في تهيئة نظام المظهر: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_comprehensive_test_window()
        window.show()
        
        print("✅ تم فتح نافذة اختبار المظهر الشاملة!")
        print("\n🎯 يمكنك الآن:")
        print("1. اختبار جميع عناصر الواجهة")
        print("2. تغيير المظهر بين الفاتح والداكن")
        print("3. تجربة مخططات ألوان مختلفة")
        print("4. فتح إعدادات المظهر المتقدمة")
        print("5. مشاهدة التطبيق الفوري على جميع العناصر")
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
