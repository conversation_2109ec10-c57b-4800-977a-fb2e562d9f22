#!/usr/bin/env python3
"""
اختبار لوحة التحكم المحسنة للقراءة
Test Readable Dashboard
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import Qt


def create_readable_dashboard_test():
    """إنشاء نافذة اختبار لوحة التحكم المحسنة للقراءة"""
    
    class ReadableDashboardTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار لوحة التحكم المحسنة للقراءة")
            self.setGeometry(100, 100, 1400, 900)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # عنوان الاختبار
            test_title = QLabel("🔤 اختبار قابلية القراءة المحسنة")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 20px;
                    padding: 15px;
                    background-color: #ecf0f1;
                    border-radius: 10px;
                }
            """)
            main_layout.addWidget(test_title)
            
            # إنشاء لوحة التحكم
            from src.views.dashboard import Dashboard
            self.dashboard = Dashboard()
            
            main_layout.addWidget(self.dashboard)
            
            # معلومات التحسينات
            info_label = QLabel("""
            ✅ التحسينات المطبقة:
            • خط الهيدر: 18px (بدلاً من 28px)
            • خط عناوين البطاقات: 18px (بدلاً من 14px)
            • خط القيم: 28px (بدلاً من 32px)
            • خط العناوين الفرعية: 18px (بدلاً من 12px)
            • حجم البطاقات: 180×280 بكسل
            • تباعد محسن وتفاف النص
            • ظلال نصية للوضوح
            • تباعد الأحرف المحسن
            """)
            info_label.setAlignment(Qt.AlignLeft)
            info_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #34495e;
                    background-color: #f8f9fa;
                    padding: 15px;
                    border-radius: 8px;
                    border-left: 4px solid #3498db;
                    line-height: 1.6;
                }
            """)
            main_layout.addWidget(info_label)
    
    return ReadableDashboardTest()


def test_font_readability():
    """اختبار قابلية قراءة الخطوط"""
    print("🔍 اختبار قابلية قراءة الخطوط...")
    
    font_specs = {
        "هيدر رئيسي": "18px",
        "عناوين البطاقات": "18px", 
        "قيم البطاقات": "28px",
        "عناوين فرعية": "18px",
        "عناوين الأقسام": "18px",
        "أزرار الإجراءات": "18px"
    }
    
    print("📏 مواصفات الخطوط الجديدة:")
    for element, size in font_specs.items():
        print(f"   • {element}: {size}")
    
    readability_features = [
        "تباعد الأحرف المحسن (letter-spacing)",
        "ارتفاع الأسطر المحسن (line-height)", 
        "ظلال نصية للوضوح (text-shadow)",
        "التفاف النص (word-wrap)",
        "خط عريض للعناوين (font-weight: bold)",
        "ألوان عالية التباين",
        "حجم بطاقات أكبر (180×280)",
        "تباعد داخلي محسن (padding)"
    ]
    
    print("\n🎨 ميزات قابلية القراءة:")
    for feature in readability_features:
        print(f"   ✅ {feature}")
    
    return True


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار لوحة التحكم المحسنة للقراءة")
    print("=" * 60)
    
    # اختبار قابلية القراءة
    if not test_font_readability():
        print("💥 فشل في اختبار قابلية القراءة")
        return 1
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط المحسنة
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    app.setStyleSheet(f.read())
                print("✅ تم تحميل الأنماط المحسنة للقراءة")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # تهيئة نظام المظهر
        try:
            from src.utils.appearance_manager import get_appearance_manager
            appearance_manager = get_appearance_manager()
            appearance_manager.load_settings()
            print("✅ تم تهيئة نظام المظهر")
        except Exception as e:
            print(f"⚠️ فشل في تهيئة نظام المظهر: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_readable_dashboard_test()
        window.show()
        
        print("✅ تم فتح نافذة اختبار القراءة!")
        print("\n🎯 التحسينات المطبقة:")
        print("• خط الهيدر: 18px (أكثر قابلية للقراءة)")
        print("• خط النصوص في البطاقات: 18px (واضح ومقروء)")
        print("• كتابة مميزة مع تباعد الأحرف")
        print("• ظلال نصية للوضوح")
        print("• بطاقات أكبر (180×280 بكسل)")
        print("• تباعد محسن بين العناصر")
        print("• التفاف النص للنصوص الطويلة")
        
        print("\n📖 ميزات قابلية القراءة:")
        print("• تباين عالي بين النص والخلفية")
        print("• أحجام خطوط متناسقة (18px)")
        print("• تباعد أحرف محسن (letter-spacing)")
        print("• ارتفاع أسطر مناسب (line-height)")
        print("• خط عريض للعناوين المهمة")
        print("• ألوان واضحة ومريحة للعين")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
