#!/usr/bin/env python3
"""
إدارة الأيقونات والرموز
Icons and Symbols Management
"""

from pathlib import Path
from PySide6.QtGui import QIcon, QPixmap
from PySide6.QtCore import QSize
from PySide6.QtSvg import QSvgRenderer
from PySide6.QtGui import QPainter


class IconManager:
    """مدير الأيقونات والرموز"""
    
    def __init__(self):
        self.icons_dir = Path(__file__).parent.parent / "assets" / "icons"
        self.icons_dir.mkdir(parents=True, exist_ok=True)
        
        # خريطة الأيقونات
        self.icon_map = {
            "settings": "⚙️",
            "user": "👤",
            "dashboard": "📊",
            "employees": "👥",
            "departments": "🏢",
            "financial": "💰",
            "reports": "📈",
            "backup": "💾",
            "audit": "🔍",
            "job_titles": "💼",
            "salaries": "💵",
            "add": "➕",
            "edit": "✏️",
            "delete": "🗑️",
            "save": "💾",
            "cancel": "❌",
            "search": "🔍",
            "filter": "🔽",
            "export": "📤",
            "import": "📥",
            "print": "🖨️",
            "refresh": "🔄",
            "home": "🏠",
            "logout": "🚪",
            "help": "❓",
            "info": "ℹ️",
            "warning": "⚠️",
            "error": "❌",
            "success": "✅",
            "loading": "⏳"
        }
    
    def get_icon(self, name: str, size: int = 24) -> str:
        """الحصول على أيقونة نصية"""
        return self.icon_map.get(name, "❓")
    
    def get_svg_icon(self, name: str, size: QSize = QSize(24, 24)) -> QIcon:
        """الحصول على أيقونة SVG"""
        svg_path = self.icons_dir / f"{name}.svg"
        
        if svg_path.exists():
            # تحميل أيقونة SVG
            renderer = QSvgRenderer(str(svg_path))
            pixmap = QPixmap(size)
            pixmap.fill(Qt.transparent)
            
            painter = QPainter(pixmap)
            renderer.render(painter)
            painter.end()
            
            return QIcon(pixmap)
        else:
            # إرجاع أيقونة نصية كبديل
            return self.create_text_icon(self.get_icon(name), size)
    
    def create_text_icon(self, text: str, size: QSize = QSize(24, 24)) -> QIcon:
        """إنشاء أيقونة من نص"""
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QFont
        
        pixmap = QPixmap(size)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        font = QFont("Arial", size.width() // 2)
        painter.setFont(font)
        painter.setPen(Qt.black)
        
        painter.drawText(pixmap.rect(), Qt.AlignCenter, text)
        painter.end()
        
        return QIcon(pixmap)
    
    def create_settings_icon(self, size: QSize = QSize(48, 48)) -> QIcon:
        """إنشاء أيقونة إعدادات مخصصة"""
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QBrush, QPen, QColor, QLinearGradient
        
        pixmap = QPixmap(size)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # رسم دائرة خلفية
        center = size.width() // 2
        radius = center - 4
        
        # تدرج لوني للخلفية
        gradient = QLinearGradient(0, 0, size.width(), size.height())
        gradient.setColorAt(0, QColor("#9B59B6"))
        gradient.setColorAt(0.5, QColor("#8E44AD"))
        gradient.setColorAt(1, QColor("#7D3C98"))
        
        painter.setBrush(QBrush(gradient))
        painter.setPen(QPen(QColor("#6C3483"), 2))
        painter.drawEllipse(4, 4, size.width() - 8, size.height() - 8)
        
        # رسم أيقونة الترس
        painter.setPen(QPen(QColor("#FFFFFF"), 3))
        painter.setBrush(QBrush(QColor("#FFFFFF")))
        
        # رسم الترس المبسط
        gear_size = radius // 2
        gear_center = center
        
        # الدائرة الداخلية
        painter.drawEllipse(gear_center - gear_size//3, gear_center - gear_size//3, 
                          gear_size//3 * 2, gear_size//3 * 2)
        
        # الأسنان
        for i in range(8):
            angle = i * 45
            import math
            x1 = gear_center + gear_size * math.cos(math.radians(angle))
            y1 = gear_center + gear_size * math.sin(math.radians(angle))
            x2 = gear_center + (gear_size - 6) * math.cos(math.radians(angle))
            y2 = gear_center + (gear_size - 6) * math.sin(math.radians(angle))
            
            painter.drawLine(int(x1), int(y1), int(x2), int(y2))
        
        painter.end()
        return QIcon(pixmap)


# إنشاء مثيل عام
icon_manager = IconManager()


def get_icon(name: str, size: int = 24) -> str:
    """دالة مساعدة للحصول على أيقونة نصية"""
    return icon_manager.get_icon(name, size)


def get_svg_icon(name: str, size: QSize = QSize(24, 24)) -> QIcon:
    """دالة مساعدة للحصول على أيقونة SVG"""
    return icon_manager.get_svg_icon(name, size)


def create_settings_icon(size: QSize = QSize(48, 48)) -> QIcon:
    """دالة مساعدة لإنشاء أيقونة إعدادات"""
    return icon_manager.create_settings_icon(size)


# قاموس الأيقونات للاستخدام السريع
ICONS = {
    "settings": "⚙️",
    "user": "👤", 
    "dashboard": "📊",
    "employees": "👥",
    "departments": "🏢",
    "financial": "💰",
    "reports": "📈",
    "backup": "💾",
    "audit": "🔍",
    "job_titles": "💼",
    "salaries": "💵",
    "welcome": "🌟",
    "manager": "👨‍💼",
    "system": "💼",
    "building": "🏢"
}


def get_clean_icon(name: str) -> str:
    """الحصول على أيقونة نظيفة بدون تعقيد"""
    clean_icons = {
        "settings": "⚙",
        "user": "👤",
        "dashboard": "📊", 
        "employees": "👥",
        "departments": "🏢",
        "financial": "💰",
        "reports": "📈",
        "backup": "💾",
        "audit": "🔍",
        "welcome": "👋",
        "manager": "👔",
        "system": "💻"
    }
    return clean_icons.get(name, "•")
