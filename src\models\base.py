"""
النموذج الأساسي لجميع نماذج قاعدة البيانات
Base model for all database models
"""

from datetime import datetime
from typing import Any, Dict
from sqlalchemy import Column, Integer, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session

Base = declarative_base()


class BaseModel(Base):
    """النموذج الأساسي الذي ترث منه جميع النماذج الأخرى"""
    
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل النموذج إلى قاموس"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            result[column.name] = value
        return result
    
    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """تحديث النموذج من قاموس"""
        for key, value in data.items():
            if hasattr(self, key) and key not in ['id', 'created_at']:
                setattr(self, key, value)
        self.updated_at = datetime.utcnow()
    
    def soft_delete(self) -> None:
        """حذف ناعم للسجل"""
        self.is_active = False
        self.updated_at = datetime.utcnow()
    
    def restore(self) -> None:
        """استعادة السجل المحذوف"""
        self.is_active = True
        self.updated_at = datetime.utcnow()
    
    @classmethod
    def get_active_records(cls, session: Session):
        """الحصول على السجلات النشطة فقط"""
        return session.query(cls).filter(cls.is_active == True)
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.id})>"
