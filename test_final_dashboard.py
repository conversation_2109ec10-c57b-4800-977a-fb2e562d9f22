#!/usr/bin/env python3
"""
اختبار لوحة التحكم النهائية المحسنة
Test Final Enhanced Dashboard
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow
from PySide6.QtCore import Qt


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار لوحة التحكم النهائية المحسنة")
    print("=" * 80)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            enhanced_style_file = styles_dir / "enhanced_dashboard.qss"
            
            combined_styles = ""
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read() + "\n"
                    
            if enhanced_style_file.exists():
                with open(enhanced_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read()
                    
            app.setStyleSheet(combined_styles)
            print("✅ تم تحميل الأنماط المحسنة")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # تهيئة نظام المظهر
        try:
            from src.utils.appearance_manager import get_appearance_manager
            appearance_manager = get_appearance_manager()
            appearance_manager.load_settings()
            print("✅ تم تهيئة نظام المظهر")
        except Exception as e:
            print(f"⚠️ فشل في تهيئة نظام المظهر: {e}")
        
        # إنشاء النافذة الرئيسية
        class TestWindow(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("لوحة التحكم المحسنة - اختبار نهائي")
                self.setGeometry(50, 50, 1600, 1000)
                
                # إنشاء لوحة التحكم المحسنة
                from src.widgets import EnhancedDashboard
                self.dashboard = EnhancedDashboard()
                self.setCentralWidget(self.dashboard)
                
                # ربط الإشارات
                self.dashboard.add_employee_requested.connect(lambda: print("🔄 طلب إضافة موظف"))
                self.dashboard.add_transaction_requested.connect(lambda: print("🔄 طلب إضافة معاملة"))
                self.dashboard.view_reports_requested.connect(lambda: print("🔄 طلب عرض التقارير"))
                self.dashboard.backup_requested.connect(lambda: print("🔄 طلب نسخة احتياطية"))
        
        window = TestWindow()
        window.show()
        
        print("✅ تم فتح لوحة التحكم المحسنة!")
        print("\n🎯 الميزات المطبقة:")
        print("• عنوان رئيسي بخط 24px مع تنسيق عرض محسن")
        print("• 6 بطاقات بيانات توضيحية شاملة:")
        print("  - عدد الموظفين (أزرق) 👥")
        print("  - إجمالي الرواتب المستحقة (أخضر) 💰")
        print("  - إجمالي السلف (برتقالي) 💳")
        print("  - إجمالي الديون (أحمر) 📋")
        print("  - إجمالي المكافآت (بنفسجي) 🎁")
        print("  - الأقسام النشطة (تركوازي) 🏢")
        print("• رسم بياني لتوزيع الموظفين على الأقسام")
        print("• أزرار إجراءات سريعة مع تأثيرات بصرية")
        print("• ألوان متناسقة ومميزة")
        print("• ربط صحيح مع قاعدة البيانات")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
