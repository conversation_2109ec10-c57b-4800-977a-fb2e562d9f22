# Makefile لنظام إدارة شؤون الموظفين
# HR Management System Makefile

.PHONY: help install setup test run clean reset backup docs

# المتغيرات
PYTHON = python3
PIP = pip3
VENV = venv
REQUIREMENTS = requirements.txt

# الهدف الافتراضي
help:
	@echo "🏢 نظام إدارة شؤون الموظفين"
	@echo "================================"
	@echo ""
	@echo "الأوامر المتاحة:"
	@echo ""
	@echo "📦 إعداد البيئة:"
	@echo "  install     - تثبيت المتطلبات"
	@echo "  venv        - إنشاء بيئة افتراضية"
	@echo "  venv-install - إنشاء بيئة افتراضية وتثبيت المتطلبات"
	@echo ""
	@echo "🗄️  قاعدة البيانات:"
	@echo "  setup       - إعداد قاعدة البيانات (سريع)"
	@echo "  setup-interactive - إعداد قاعدة البيانات (تفاعلي)"
	@echo "  test-db     - اختبار قاعدة البيانات"
	@echo "  reset-db    - إعادة تعيين قاعدة البيانات"
	@echo ""
	@echo "🚀 تشغيل النظام:"
	@echo "  run         - تشغيل النظام"
	@echo "  dev         - تشغيل في وضع التطوير"
	@echo ""
	@echo "🧪 الاختبارات:"
	@echo "  test        - تشغيل جميع الاختبارات"
	@echo "  test-unit   - اختبارات الوحدة"
	@echo "  test-ui     - اختبارات الواجهة"
	@echo ""
	@echo "💾 النسخ الاحتياطي:"
	@echo "  backup      - إنشاء نسخة احتياطية"
	@echo "  list-backups - عرض النسخ الاحتياطية"
	@echo ""
	@echo "📚 التوثيق:"
	@echo "  docs        - توليد التوثيق"
	@echo "  docs-serve  - تشغيل خادم التوثيق"
	@echo ""
	@echo "🧹 التنظيف:"
	@echo "  clean       - تنظيف الملفات المؤقتة"
	@echo "  clean-all   - تنظيف شامل"
	@echo ""

# تثبيت المتطلبات
install:
	@echo "📦 تثبيت المتطلبات..."
	$(PIP) install -r $(REQUIREMENTS)
	@echo "✅ تم تثبيت المتطلبات بنجاح"

# إنشاء بيئة افتراضية
venv:
	@echo "🔧 إنشاء بيئة افتراضية..."
	$(PYTHON) -m venv $(VENV)
	@echo "✅ تم إنشاء البيئة الافتراضية"
	@echo "💡 لتفعيل البيئة:"
	@echo "   source $(VENV)/bin/activate  # Linux/macOS"
	@echo "   $(VENV)\\Scripts\\activate     # Windows"

# إنشاء بيئة افتراضية وتثبيت المتطلبات
venv-install: venv
	@echo "📦 تثبيت المتطلبات في البيئة الافتراضية..."
	$(VENV)/bin/pip install -r $(REQUIREMENTS)
	@echo "✅ تم إعداد البيئة الافتراضية بالكامل"

# إعداد قاعدة البيانات (سريع)
setup:
	@echo "🗄️  إعداد قاعدة البيانات..."
	$(PYTHON) quick_setup.py
	@echo "✅ تم إعداد قاعدة البيانات"

# إعداد قاعدة البيانات (تفاعلي)
setup-interactive:
	@echo "🗄️  إعداد قاعدة البيانات التفاعلي..."
	$(PYTHON) setup_database.py

# اختبار قاعدة البيانات
test-db:
	@echo "🧪 اختبار قاعدة البيانات..."
	$(PYTHON) test_db.py

# إعادة تعيين قاعدة البيانات
reset-db:
	@echo "⚠️  إعادة تعيين قاعدة البيانات..."
	$(PYTHON) setup_database.py reset

# تشغيل النظام
run:
	@echo "🚀 تشغيل نظام إدارة شؤون الموظفين..."
	$(PYTHON) run.py

# تشغيل في وضع التطوير
dev:
	@echo "🔧 تشغيل في وضع التطوير..."
	DEBUG=true $(PYTHON) run.py

# تشغيل جميع الاختبارات
test:
	@echo "🧪 تشغيل جميع الاختبارات..."
	$(PYTHON) -m pytest tests/ -v

# اختبارات الوحدة
test-unit:
	@echo "🧪 تشغيل اختبارات الوحدة..."
	$(PYTHON) -m pytest tests/test_models.py tests/test_services.py -v

# اختبارات الواجهة
test-ui:
	@echo "🧪 تشغيل اختبارات الواجهة..."
	$(PYTHON) -m pytest tests/test_views.py -v

# إنشاء نسخة احتياطية
backup:
	@echo "💾 إنشاء نسخة احتياطية..."
	$(PYTHON) -c "from src.services.backup_service import backup_service; backup_service.create_backup()"
	@echo "✅ تم إنشاء النسخة الاحتياطية"

# عرض النسخ الاحتياطية
list-backups:
	@echo "📋 النسخ الاحتياطية المتوفرة:"
	$(PYTHON) -c "from src.services.backup_service import backup_service; [print(f'  - {b[\"name\"]} ({b[\"created_at\"]})') for b in backup_service.list_backups()]"

# توليد التوثيق
docs:
	@echo "📚 توليد التوثيق..."
	@echo "📖 التوثيق متوفر في مجلد docs/"
	@echo "   - docs/user_guide.md      # دليل المستخدم"
	@echo "   - docs/developer_guide.md # دليل المطور"
	@echo "   - docs/api_reference.md   # مرجع API"

# تشغيل خادم التوثيق
docs-serve:
	@echo "🌐 تشغيل خادم التوثيق..."
	@echo "💡 افتح المتصفح على: http://localhost:8000"
	$(PYTHON) -m http.server 8000 --directory docs/

# تنظيف الملفات المؤقتة
clean:
	@echo "🧹 تنظيف الملفات المؤقتة..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.log" -delete
	rm -rf .pytest_cache/
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	@echo "✅ تم تنظيف الملفات المؤقتة"

# تنظيف شامل
clean-all: clean
	@echo "🧹 تنظيف شامل..."
	rm -rf $(VENV)/
	rm -rf logs/
	@echo "✅ تم التنظيف الشامل"

# فحص جودة الكود
lint:
	@echo "🔍 فحص جودة الكود..."
	$(PYTHON) -m flake8 src/ --max-line-length=100
	@echo "✅ فحص الكود مكتمل"

# تنسيق الكود
format:
	@echo "✨ تنسيق الكود..."
	$(PYTHON) -m black src/ tests/
	@echo "✅ تم تنسيق الكود"

# تحديث المتطلبات
update-deps:
	@echo "🔄 تحديث المتطلبات..."
	$(PIP) install --upgrade -r $(REQUIREMENTS)
	@echo "✅ تم تحديث المتطلبات"

# إنشاء ملف تنفيذي
build:
	@echo "🔨 إنشاء ملف تنفيذي..."
	$(PYTHON) -m PyInstaller --onefile --windowed --name="HR_System" run.py
	@echo "✅ تم إنشاء الملف التنفيذي في مجلد dist/"

# عرض معلومات النظام
info:
	@echo "ℹ️  معلومات النظام:"
	@echo "  Python: $(shell $(PYTHON) --version)"
	@echo "  Pip: $(shell $(PIP) --version)"
	@echo "  المجلد الحالي: $(shell pwd)"
	@echo "  البيئة الافتراضية: $(shell if [ -d $(VENV) ]; then echo 'موجودة'; else echo 'غير موجودة'; fi)"

# التحقق من الصحة
check: test-db lint test
	@echo "✅ جميع الفحوصات مكتملة بنجاح!"

# إعداد كامل للمشروع
init: venv-install setup
	@echo "🎉 تم إعداد المشروع بالكامل!"
	@echo "🚀 يمكنك الآن تشغيل النظام بـ: make run"

# مساعدة سريعة
quick-help:
	@echo "🚀 أوامر سريعة:"
	@echo "  make init    # إعداد كامل للمشروع"
	@echo "  make setup   # إعداد قاعدة البيانات"
	@echo "  make run     # تشغيل النظام"
	@echo "  make test    # تشغيل الاختبارات"
	@echo "  make clean   # تنظيف الملفات"
