#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الوضع الليلي مع النظام الرئيسي
Main System Dark Mode Test
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))


def test_main_system_with_dark_mode():
    """اختبار النظام الرئيسي مع الوضع الليلي"""
    print("🌙 اختبار النظام الرئيسي مع الوضع الليلي")
    print("=" * 50)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط مع دعم الوضع الليلي
        from src.utils.theme_manager import ThemeManager
        theme_manager = ThemeManager()
        
        # تطبيق الوضع الليلي
        print("🔧 تطبيق الوضع الليلي...")
        success = theme_manager.set_theme("dark")
        if success:
            print("✅ تم تطبيق الوضع الليلي بنجاح")
        else:
            print("❌ فشل في تطبيق الوضع الليلي")
        
        # إنشاء النافذة الرئيسية
        print("🏗️ إنشاء النافذة الرئيسية...")
        from src.views.main_window import MainWindow
        
        main_window = MainWindow()
        main_window.show()
        
        print("✅ تم فتح النظام الرئيسي مع الوضع الليلي!")
        print("\n🎯 يمكنك الآن:")
        print("1. التنقل بين الصفحات المختلفة")
        print("2. اختبار جميع العناصر في الوضع الليلي")
        print("3. الذهاب لصفحة الإعدادات لتبديل الثيم")
        print("4. اختبار البطاقات الإحصائية والجداول")
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"💥 خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        return 1


def create_simple_theme_test():
    """إنشاء اختبار بسيط للثيمات"""
    
    class SimpleThemeTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("🌙 اختبار الثيمات البسيط")
            self.setGeometry(200, 200, 600, 400)
            
            # إنشاء مدير الثيمات
            from src.utils.theme_manager import ThemeManager
            self.theme_manager = ThemeManager()
            
            self.setup_ui()
            
        def setup_ui(self):
            """إعداد الواجهة"""
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            layout = QVBoxLayout(central_widget)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)
            
            # العنوان
            title = QLabel("🌙 اختبار الثيمات")
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
            layout.addWidget(title)
            
            # معلومات الثيم الحالي
            self.theme_label = QLabel()
            self.theme_label.setAlignment(Qt.AlignCenter)
            self.update_theme_info()
            layout.addWidget(self.theme_label)
            
            # أزرار التحكم
            buttons_layout = QHBoxLayout()
            
            light_btn = QPushButton("☀️ وضع نهاري")
            light_btn.clicked.connect(lambda: self.set_theme("light"))
            buttons_layout.addWidget(light_btn)
            
            dark_btn = QPushButton("🌙 وضع ليلي")
            dark_btn.clicked.connect(lambda: self.set_theme("dark"))
            buttons_layout.addWidget(dark_btn)
            
            toggle_btn = QPushButton("🔄 تبديل")
            toggle_btn.clicked.connect(self.toggle_theme)
            buttons_layout.addWidget(toggle_btn)
            
            layout.addLayout(buttons_layout)
            
            # عناصر اختبار
            test_group = QGroupBox("عناصر الاختبار")
            test_layout = QVBoxLayout(test_group)
            
            # حقل نص
            line_edit = QLineEdit("نص تجريبي")
            test_layout.addWidget(line_edit)
            
            # قائمة منسدلة
            combo = QComboBox()
            combo.addItems(["خيار 1", "خيار 2", "خيار 3"])
            test_layout.addWidget(combo)
            
            # خانة اختيار
            checkbox = QCheckBox("خانة اختيار")
            checkbox.setChecked(True)
            test_layout.addWidget(checkbox)
            
            # زر راديو
            radio = QRadioButton("زر راديو")
            radio.setChecked(True)
            test_layout.addWidget(radio)
            
            layout.addWidget(test_group)
            
            # شريط الحالة
            self.statusBar().showMessage("جاهز للاختبار")
            
        def set_theme(self, theme_name):
            """تعيين ثيم"""
            success = self.theme_manager.set_theme(theme_name)
            if success:
                self.update_theme_info()
                theme_display = "الوضع الليلي" if theme_name == "dark" else "الوضع النهاري"
                self.statusBar().showMessage(f"تم تطبيق {theme_display}")
                print(f"✅ تم تطبيق {theme_display}")
            else:
                self.statusBar().showMessage(f"فشل في تطبيق الثيم: {theme_name}")
                print(f"❌ فشل في تطبيق الثيم: {theme_name}")
        
        def toggle_theme(self):
            """تبديل الثيم"""
            new_theme = self.theme_manager.toggle_theme()
            self.update_theme_info()
            theme_display = "الوضع الليلي" if new_theme == "dark" else "الوضع النهاري"
            self.statusBar().showMessage(f"تم التبديل إلى {theme_display}")
            print(f"🔄 تم التبديل إلى {theme_display}")
        
        def update_theme_info(self):
            """تحديث معلومات الثيم"""
            current_theme = self.theme_manager.get_current_theme()
            if current_theme == "dark":
                self.theme_label.setText("الثيم الحالي: 🌙 الوضع الليلي")
            else:
                self.theme_label.setText("الثيم الحالي: ☀️ الوضع النهاري")
    
    return SimpleThemeTest()


def main():
    """الدالة الرئيسية"""
    if len(sys.argv) > 1 and sys.argv[1] == "--simple":
        # اختبار بسيط
        print("🔧 تشغيل الاختبار البسيط...")
        try:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
            
            window = create_simple_theme_test()
            window.show()
            
            print("✅ تم فتح نافذة الاختبار البسيط")
            return app.exec()
            
        except Exception as e:
            print(f"💥 خطأ في الاختبار البسيط: {e}")
            return 1
    else:
        # اختبار النظام الكامل
        return test_main_system_with_dark_mode()


if __name__ == "__main__":
    sys.exit(main())
