"""
واجهة التقارير المحسنة الجديدة
Enhanced Reports View
"""

from datetime import datetime
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton, QComboBox,
    QLabel, QFrame, QDateEdit, QGroupBox, QTabWidget, QTableWidget,
    QTableWidgetItem, QHeaderView, QAbstractItemView, QFileDialog
)
from PySide6.QtCore import Qt, Signal, QDate
from PySide6.QtGui import QFont, QPainter, QColor
from PySide6.QtPrintSupport import QPrinter, QPrintDialog

from ..utils import (
    apply_rtl_layout, show_message, format_currency, format_date
)
from ..database import get_db_session_context
from ..models import (
    Employee, Department, JobTitle, FinancialTransaction, SalaryRecord,
    TransactionType
)


class EnhancedReportsView(QWidget):
    """واجهة التقارير المحسنة مع التبويبات والفلترة والطباعة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_data = []
        self.filtered_data = []
        self.current_headers = []
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # العنوان الرئيسي
        self.create_main_header(layout)
        
        # إنشاء التبويبات
        self.create_tabs_section(layout)
        
        # تطبيق التخطيط العربي
        apply_rtl_layout(self)
        
    def create_main_header(self, layout):
        """إنشاء العنوان الرئيسي المتناسق مع بقية الأقسام"""
        # إنشاء إطار العنوان الرئيسي المدمج
        header_frame = QFrame()
        header_frame.setObjectName("compact_header_frame")
        header_frame.setFixedHeight(120)  # ارتفاع محسن متناسق مع الأقسام الأخرى
        header_frame.setStyleSheet("""
            QFrame#compact_header_frame {
                background-color: #e3f2fd;
                border: 2px solid #2196F3;
                border-radius: 10px;
                margin: 5px;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 8, 15, 8)  # تقليل الحشو
        header_layout.setSpacing(5)  # تقليل التباعد

        # إضافة مساحة مرنة في الأعلى لتوسيط المحتوى
        header_layout.addStretch()

        # إنشاء حاوي للتوسيط الأفقي للعنوان الرئيسي
        main_title_container = QHBoxLayout()
        main_title_container.addStretch()  # مساحة مرنة يسار

        self.main_title = QLabel("📊 إدارة التقارير")
        self.main_title.setAlignment(Qt.AlignCenter)
        self.main_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 22px;
                font-weight: bold;
                color: #2c3e50;
                background-color: transparent;
                padding: 5px;
                margin: 0px;
            }
        """)

        main_title_container.addWidget(self.main_title)
        main_title_container.addStretch()  # مساحة مرنة يمين

        # إنشاء حاوي للتوسيط الأفقي للعنوان الفرعي
        subtitle_container = QHBoxLayout()
        subtitle_container.addStretch()  # مساحة مرنة يسار

        self.subtitle = QLabel("📈 تقارير شاملة مع فلترة متقدمة وطباعة احترافية")
        self.subtitle.setAlignment(Qt.AlignCenter)
        self.subtitle.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #34495e;
                background-color: #f3f8ff;
                padding: 6px 15px;
                border-radius: 6px;
                border-left: 3px solid #2196F3;
                margin: 2px;
            }
        """)

        subtitle_container.addWidget(self.subtitle)
        subtitle_container.addStretch()  # مساحة مرنة يمين

        # تجميع العناصر
        header_layout.addLayout(main_title_container)
        header_layout.addLayout(subtitle_container)

        # إضافة مساحة مرنة في الأسفل لتوسيط المحتوى
        header_layout.addStretch()

        layout.addWidget(header_frame)

    def update_page_title(self, title: str, subtitle: str):
        """تحديث عنوان الصفحة"""
        self.main_title.setText(title)
        self.subtitle.setText(subtitle)
        
    def create_tabs_section(self, layout):
        """إنشاء قسم التبويبات المتناسق مع النظام"""
        self.tabs_widget = QTabWidget()
        self.tabs_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #dee2e6;
                border-radius: 10px;
                background-color: white;
                padding: 15px;
                margin-top: 5px;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-bottom-color: #C2C7CB;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                min-width: 140px;
                padding: 10px 16px;
                margin: 2px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-weight: bold;
                font-size: 13px;
                color: #495057;
            }
            QTabBar::tab:selected {
                background-color: #2196F3;
                color: white;
                border-color: #2196F3;
                border-bottom-color: #2196F3;
            }
            QTabBar::tab:hover:!selected {
                background-color: #e3f2fd;
                color: #1976D2;
                border-color: #90caf9;
            }
        """)

        # إنشاء التبويبات
        self.create_employees_reports_tab()
        self.create_financial_operations_tab()
        self.create_salary_reports_tab()
        self.create_statistics_tab()
        self.create_employee_statement_tab()

        layout.addWidget(self.tabs_widget)

        # تحديث عنوان الصفحة عند تغيير التبويب
        self.tabs_widget.currentChanged.connect(self.on_tab_changed)

    def on_tab_changed(self, index):
        """تحديث عنوان الصفحة عند تغيير التبويب"""
        tab_titles = {
            0: ("👥 إدارة تقارير الموظفين", "📊 تقارير شاملة للموظفين مع فلترة متقدمة"),
            1: ("💰 إدارة العمليات المالية", "💳 تقارير المعاملات المالية والسلف والمكافآت"),
            2: ("💵 إدارة تقارير الرواتب", "📋 تقارير صرف الرواتب والمكافآت والخصومات"),
            3: ("📈 إدارة الإحصائيات", "📊 إحصائيات شاملة للنظام والموظفين"),
            4: ("📋 كشف حساب الموظف", "💼 تفاصيل جميع العمليات المالية للموظف")
        }

        if index in tab_titles:
            title, subtitle = tab_titles[index]
            self.update_page_title(title, subtitle)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        pass  # سيتم إعداد الاتصالات في كل تبويب

    def create_employees_reports_tab(self):
        """إنشاء تبويب تقارير الموظفين"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # قسم الفلاتر المتناسق
        filters_frame = QFrame()
        filters_frame.setObjectName("search_frame")
        filters_frame.setStyleSheet("""
            QFrame#search_frame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)

        filters_layout = QVBoxLayout(filters_frame)
        filters_layout.setSpacing(8)

        # الصف الأول: عنوان البحث مع الإحصائيات
        first_row = QHBoxLayout()

        # عنوان البحث
        search_title = QLabel("🔍 فلاتر تقارير الموظفين")
        search_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 13px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)

        # معلومات النتائج
        self.emp_results_info = QLabel("📋 إجمالي النتائج: 0")
        self.emp_results_info.setFixedHeight(25)
        self.emp_results_info.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 11px;
                font-weight: bold;
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 4px 8px;
                border-radius: 4px;
                border: 1px solid #2196F3;
                text-align: center;
            }
        """)

        first_row.addWidget(search_title)
        first_row.addStretch()
        first_row.addWidget(self.emp_results_info)

        # الصف الثاني: الفلاتر المدمجة في صف واحد
        second_row = QHBoxLayout()
        second_row.setSpacing(10)

        # فلتر القسم
        dept_label = QLabel("🏢 القسم:")
        dept_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: 600;
                color: #495057;
                min-width: 60px;
            }
        """)
        second_row.addWidget(dept_label)
        self.emp_dept_filter = QComboBox()
        self.emp_dept_filter.setMinimumHeight(30)
        self.emp_dept_filter.setStyleSheet("""
            QComboBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 11px;
                padding: 4px 8px;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                background-color: #ffffff;
                min-width: 100px;
            }
            QComboBox:focus {
                border-color: #2196F3;
            }
        """)
        second_row.addWidget(self.emp_dept_filter)

        # فلتر العنوان الوظيفي
        job_label = QLabel("💼 المنصب:")
        job_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                font-weight: 600;
                color: #495057;
                min-width: 50px;
            }
        """)
        second_row.addWidget(job_label)

        self.emp_job_filter = QComboBox()
        self.emp_job_filter.setMinimumHeight(30)
        self.emp_job_filter.setStyleSheet("""
            QComboBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 11px;
                padding: 4px 8px;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                background-color: #ffffff;
                min-width: 100px;
            }
            QComboBox:focus {
                border-color: #2196F3;
            }
        """)
        second_row.addWidget(self.emp_job_filter)

        # فلتر الحالة
        status_label = QLabel("📊 الحالة:")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                font-weight: 600;
                color: #495057;
                min-width: 50px;
            }
        """)
        second_row.addWidget(status_label)

        self.emp_status_filter = QComboBox()
        self.emp_status_filter.addItems(["جميع الحالات", "نشط", "غير نشط"])
        self.emp_status_filter.setMinimumHeight(30)
        self.emp_status_filter.setStyleSheet("""
            QComboBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 11px;
                padding: 4px 8px;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                background-color: #ffffff;
                min-width: 80px;
            }
            QComboBox:focus {
                border-color: #2196F3;
            }
        """)
        second_row.addWidget(self.emp_status_filter)

        # أزرار التحكم
        self.emp_apply_btn = QPushButton("🔍 تطبيق")
        self.emp_apply_btn.setMinimumHeight(30)
        self.emp_apply_btn.setStyleSheet("""
            QPushButton {
                font-size: 11px;
                font-weight: bold;
                color: white;
                background-color: #28a745;
                border: none;
                border-radius: 5px;
                padding: 6px 12px;
                min-width: 70px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.emp_apply_btn.clicked.connect(self.load_employees_report)
        second_row.addWidget(self.emp_apply_btn)

        self.emp_clear_btn = QPushButton("🗑️ مسح")
        self.emp_clear_btn.setMinimumHeight(30)
        self.emp_clear_btn.setStyleSheet("""
            QPushButton {
                font-size: 11px;
                font-weight: bold;
                color: white;
                background-color: #6c757d;
                border: none;
                border-radius: 5px;
                padding: 6px 12px;
                min-width: 50px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        self.emp_clear_btn.clicked.connect(self.clear_employees_filters)
        second_row.addWidget(self.emp_clear_btn)

        # إضافة مساحة مرنة
        second_row.addStretch()

        filters_layout.addLayout(first_row)
        filters_layout.addLayout(second_row)
        layout.addWidget(filters_frame)

        # قسم الجدول
        self.emp_table_frame = self.create_table_frame("📊 تقرير الموظفين")
        self.emp_table = self.emp_table_frame.findChild(QTableWidget)
        layout.addWidget(self.emp_table_frame)

        self.tabs_widget.addTab(tab, "👥 تقارير الموظفين")

        # تحميل البيانات الأولية
        self.load_employees_filters()
        self.load_employees_report()

    def create_financial_operations_tab(self):
        """إنشاء تبويب تقارير العمليات المالية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # قسم الفلاتر المتناسق
        filters_frame = QFrame()
        filters_frame.setObjectName("search_frame")
        filters_frame.setStyleSheet("""
            QFrame#search_frame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)

        filters_layout = QGridLayout(filters_frame)
        filters_layout.setSpacing(15)

        # فلتر نوع المعاملة
        filters_layout.addWidget(QLabel("نوع المعاملة:"), 0, 0)
        self.fin_type_filter = QComboBox()
        self.fin_type_filter.addItems(["جميع المعاملات", "سلفة", "خصم", "مكافأة", "دين الماركت"])
        self.fin_type_filter.setMinimumHeight(35)
        self.fin_type_filter.setStyleSheet(self.get_combobox_style())
        filters_layout.addWidget(self.fin_type_filter, 0, 1)

        # فلتر الموظف
        filters_layout.addWidget(QLabel("الموظف:"), 0, 2)
        self.fin_emp_filter = QComboBox()
        self.fin_emp_filter.setMinimumHeight(35)
        self.fin_emp_filter.setStyleSheet(self.get_combobox_style())
        filters_layout.addWidget(self.fin_emp_filter, 0, 3)

        # فلتر التاريخ
        filters_layout.addWidget(QLabel("من تاريخ:"), 1, 0)
        self.fin_from_date = QDateEdit()
        self.fin_from_date.setDate(QDate.currentDate().addMonths(-3))
        self.fin_from_date.setCalendarPopup(True)
        self.fin_from_date.setMinimumHeight(35)
        self.fin_from_date.setStyleSheet(self.get_combobox_style())
        filters_layout.addWidget(self.fin_from_date, 1, 1)

        filters_layout.addWidget(QLabel("إلى تاريخ:"), 1, 2)
        self.fin_to_date = QDateEdit()
        self.fin_to_date.setDate(QDate.currentDate())
        self.fin_to_date.setCalendarPopup(True)
        self.fin_to_date.setMinimumHeight(35)
        self.fin_to_date.setStyleSheet(self.get_combobox_style())
        filters_layout.addWidget(self.fin_to_date, 1, 3)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        self.fin_apply_btn = QPushButton("🔍 تطبيق الفلتر")
        self.fin_apply_btn.setMinimumHeight(40)
        self.fin_apply_btn.setStyleSheet(self.get_button_style("#4CAF50"))
        self.fin_apply_btn.clicked.connect(self.load_financial_report)

        self.fin_clear_btn = QPushButton("🗑️ مسح الفلاتر")
        self.fin_clear_btn.setMinimumHeight(40)
        self.fin_clear_btn.setStyleSheet(self.get_button_style("#f44336"))
        self.fin_clear_btn.clicked.connect(self.clear_financial_filters)

        buttons_layout.addWidget(self.fin_apply_btn)
        buttons_layout.addWidget(self.fin_clear_btn)
        buttons_layout.addStretch()

        filters_layout.addLayout(buttons_layout, 2, 0, 1, 4)
        layout.addWidget(filters_frame)

        # قسم الجدول
        self.fin_table_frame = self.create_table_frame("💰 تقرير العمليات المالية")
        self.fin_table = self.fin_table_frame.findChild(QTableWidget)
        layout.addWidget(self.fin_table_frame)

        self.tabs_widget.addTab(tab, "💰 العمليات المالية")

        # تحميل البيانات الأولية
        self.load_financial_filters()
        self.load_financial_report()

    def create_salary_reports_tab(self):
        """إنشاء تبويب تقارير صرف الراتب"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # قسم الفلاتر
        filters_frame = QGroupBox("🔍 فلاتر تقارير الرواتب")
        filters_frame.setFont(QFont("Arial", 14, QFont.Bold))
        filters_frame.setStyleSheet(self.get_groupbox_style())

        filters_layout = QGridLayout(filters_frame)
        filters_layout.setSpacing(15)

        # فلتر الشهر
        filters_layout.addWidget(QLabel("الشهر:"), 0, 0)
        self.sal_month_filter = QComboBox()
        months = ["جميع الشهور", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                 "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
        self.sal_month_filter.addItems(months)
        self.sal_month_filter.setMinimumHeight(35)
        self.sal_month_filter.setStyleSheet(self.get_combobox_style())
        filters_layout.addWidget(self.sal_month_filter, 0, 1)

        # فلتر السنة
        filters_layout.addWidget(QLabel("السنة:"), 0, 2)
        self.sal_year_filter = QComboBox()
        current_year = QDate.currentDate().year()
        years = ["جميع السنوات"] + [str(year) for year in range(current_year-2, current_year+2)]
        self.sal_year_filter.addItems(years)
        self.sal_year_filter.setCurrentText(str(current_year))
        self.sal_year_filter.setMinimumHeight(35)
        self.sal_year_filter.setStyleSheet(self.get_combobox_style())
        filters_layout.addWidget(self.sal_year_filter, 0, 3)

        # فلتر القسم
        filters_layout.addWidget(QLabel("القسم:"), 1, 0)
        self.sal_dept_filter = QComboBox()
        self.sal_dept_filter.setMinimumHeight(35)
        self.sal_dept_filter.setStyleSheet(self.get_combobox_style())
        filters_layout.addWidget(self.sal_dept_filter, 1, 1)

        # فلتر حالة الصرف
        filters_layout.addWidget(QLabel("حالة الصرف:"), 1, 2)
        self.sal_status_filter = QComboBox()
        self.sal_status_filter.addItems(["جميع الحالات", "مدفوع", "غير مدفوع"])
        self.sal_status_filter.setMinimumHeight(35)
        self.sal_status_filter.setStyleSheet(self.get_combobox_style())
        filters_layout.addWidget(self.sal_status_filter, 1, 3)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        self.sal_apply_btn = QPushButton("🔍 تطبيق الفلتر")
        self.sal_apply_btn.setMinimumHeight(40)
        self.sal_apply_btn.setStyleSheet(self.get_button_style("#4CAF50"))
        self.sal_apply_btn.clicked.connect(self.load_salary_report)

        self.sal_clear_btn = QPushButton("🗑️ مسح الفلاتر")
        self.sal_clear_btn.setMinimumHeight(40)
        self.sal_clear_btn.setStyleSheet(self.get_button_style("#f44336"))
        self.sal_clear_btn.clicked.connect(self.clear_salary_filters)

        buttons_layout.addWidget(self.sal_apply_btn)
        buttons_layout.addWidget(self.sal_clear_btn)
        buttons_layout.addStretch()

        filters_layout.addLayout(buttons_layout, 2, 0, 1, 4)
        layout.addWidget(filters_frame)

        # قسم الجدول
        self.sal_table_frame = self.create_table_frame("💵 تقرير صرف الرواتب")
        self.sal_table = self.sal_table_frame.findChild(QTableWidget)
        layout.addWidget(self.sal_table_frame)

        self.tabs_widget.addTab(tab, "💵 تقارير الرواتب")

        # تحميل البيانات الأولية
        self.load_salary_filters()
        self.load_salary_report()

    def create_statistics_tab(self):
        """إنشاء تبويب تقارير الإحصائيات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # قسم الفلاتر
        filters_frame = QGroupBox("🔍 فلاتر الإحصائيات")
        filters_frame.setFont(QFont("Arial", 14, QFont.Bold))
        filters_frame.setStyleSheet(self.get_groupbox_style())

        filters_layout = QHBoxLayout(filters_frame)

        # زر تحديث الإحصائيات
        self.stats_refresh_btn = QPushButton("🔄 تحديث الإحصائيات")
        self.stats_refresh_btn.setMinimumHeight(40)
        self.stats_refresh_btn.setStyleSheet(self.get_button_style("#2196F3"))
        self.stats_refresh_btn.clicked.connect(self.load_statistics_report)

        filters_layout.addWidget(self.stats_refresh_btn)
        filters_layout.addStretch()

        layout.addWidget(filters_frame)

        # قسم الجدول
        self.stats_table_frame = self.create_table_frame("📈 تقرير الإحصائيات")
        self.stats_table = self.stats_table_frame.findChild(QTableWidget)
        layout.addWidget(self.stats_table_frame)

        self.tabs_widget.addTab(tab, "📈 الإحصائيات")

        # تحميل البيانات الأولية
        self.load_statistics_report()

    def create_employee_statement_tab(self):
        """إنشاء تبويب كشف حساب موظف"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # قسم الفلاتر
        filters_frame = QGroupBox("🔍 فلاتر كشف الحساب")
        filters_frame.setFont(QFont("Arial", 14, QFont.Bold))
        filters_frame.setStyleSheet(self.get_groupbox_style())

        filters_layout = QGridLayout(filters_frame)
        filters_layout.setSpacing(15)

        # اختيار الموظف
        filters_layout.addWidget(QLabel("الموظف:"), 0, 0)
        self.stmt_emp_filter = QComboBox()
        self.stmt_emp_filter.setMinimumHeight(35)
        self.stmt_emp_filter.setStyleSheet(self.get_combobox_style())
        filters_layout.addWidget(self.stmt_emp_filter, 0, 1, 1, 2)

        # فلتر التاريخ
        filters_layout.addWidget(QLabel("من تاريخ:"), 1, 0)
        self.stmt_from_date = QDateEdit()
        self.stmt_from_date.setDate(QDate.currentDate().addMonths(-6))
        self.stmt_from_date.setCalendarPopup(True)
        self.stmt_from_date.setMinimumHeight(35)
        self.stmt_from_date.setStyleSheet(self.get_combobox_style())
        filters_layout.addWidget(self.stmt_from_date, 1, 1)

        filters_layout.addWidget(QLabel("إلى تاريخ:"), 1, 2)
        self.stmt_to_date = QDateEdit()
        self.stmt_to_date.setDate(QDate.currentDate())
        self.stmt_to_date.setCalendarPopup(True)
        self.stmt_to_date.setMinimumHeight(35)
        self.stmt_to_date.setStyleSheet(self.get_combobox_style())
        filters_layout.addWidget(self.stmt_to_date, 1, 3)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        self.stmt_apply_btn = QPushButton("🔍 عرض كشف الحساب")
        self.stmt_apply_btn.setMinimumHeight(40)
        self.stmt_apply_btn.setStyleSheet(self.get_button_style("#4CAF50"))
        self.stmt_apply_btn.clicked.connect(self.load_employee_statement)

        self.stmt_clear_btn = QPushButton("🗑️ مسح الفلاتر")
        self.stmt_clear_btn.setMinimumHeight(40)
        self.stmt_clear_btn.setStyleSheet(self.get_button_style("#f44336"))
        self.stmt_clear_btn.clicked.connect(self.clear_statement_filters)

        buttons_layout.addWidget(self.stmt_apply_btn)
        buttons_layout.addWidget(self.stmt_clear_btn)
        buttons_layout.addStretch()

        filters_layout.addLayout(buttons_layout, 2, 0, 1, 4)
        layout.addWidget(filters_frame)

        # قسم الجدول
        self.stmt_table_frame = self.create_table_frame("📋 كشف حساب الموظف")
        self.stmt_table = self.stmt_table_frame.findChild(QTableWidget)
        layout.addWidget(self.stmt_table_frame)

        self.tabs_widget.addTab(tab, "📋 كشف الحساب")

        # تحميل البيانات الأولية
        self.load_statement_filters()

    def get_groupbox_style(self):
        """الحصول على نمط GroupBox"""
        return """
            QGroupBox {
                font-weight: bold;
                border: 2px solid #2196F3;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #1976D2;
            }
        """

    def get_combobox_style(self):
        """الحصول على نمط ComboBox"""
        return """
            QComboBox, QDateEdit {
                border: 2px solid #dee2e6;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                background: white;
            }
            QComboBox:focus, QDateEdit:focus {
                border-color: #2196F3;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 1px solid #2196F3;
                width: 8px;
                height: 8px;
                background: #2196F3;
            }
        """

    def get_button_style(self, color):
        """الحصول على نمط الأزرار"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:1 {color}dd);
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}dd, stop:1 {color}bb);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}bb, stop:1 {color}99);
            }}
        """

    def create_table_frame(self, title):
        """إنشاء إطار الجدول مع الأدوات المتناسق مع النظام"""
        frame = QFrame()
        frame.setObjectName("table_frame")
        frame.setStyleSheet("""
            QFrame#table_frame {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)

        layout = QVBoxLayout(frame)
        layout.setSpacing(10)

        # شريط العنوان والأدوات
        header_layout = QHBoxLayout()

        # عنوان الجدول
        table_title = QLabel(title)
        table_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)

        # معلومات النتائج
        results_info = QLabel("📋 إجمالي النتائج: 0")
        results_info.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 6px 10px;
                border-radius: 4px;
                border: 1px solid #2196F3;
                text-align: center;
            }
        """)
        results_info.setObjectName("results_info")

        # أزرار الأدوات
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setMinimumHeight(35)
        print_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #FF9800;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        print_btn.setObjectName("print_btn")

        export_btn = QPushButton("📤 تصدير Excel")
        export_btn.setMinimumHeight(35)
        export_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #4CAF50;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        export_btn.setObjectName("export_btn")

        header_layout.addWidget(table_title)
        header_layout.addStretch()
        header_layout.addWidget(results_info)
        header_layout.addWidget(export_btn)
        header_layout.addWidget(print_btn)

        layout.addLayout(header_layout)

        # الجدول المحسن
        table = QTableWidget()
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setSelectionMode(QAbstractItemView.MultiSelection)
        table.setSortingEnabled(True)
        table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                gridline-color: #e9ecef;
                selection-background-color: #e3f2fd;
                selection-color: #2c3e50;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
                text-align: center;
                color: #2c3e50;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #2c3e50;
                font-weight: bold;
            }
            QTableWidget::item:hover {
                background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                border: none;
                border-right: 1px solid #1976D2;
                text-align: center;
            }
            QHeaderView::section:hover {
                background-color: #1976D2;
            }
        """)

        # تعيين سياسة تغيير حجم الأعمدة
        header = table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)
        header.setStretchLastSection(True)

        layout.addWidget(table)

        # ربط أزرار الأدوات
        print_btn.clicked.connect(lambda: self.print_table(table, title))
        export_btn.clicked.connect(lambda: self.export_table(table, title))

        return frame

    # === وظائف تحميل الفلاتر ===

    def load_employees_filters(self):
        """تحميل فلاتر الموظفين"""
        try:
            with get_db_session_context() as session:
                # تحميل الأقسام
                departments = session.query(Department).filter_by(is_active=True).all()
                self.emp_dept_filter.clear()
                self.emp_dept_filter.addItem("جميع الأقسام", None)
                for dept in departments:
                    self.emp_dept_filter.addItem(dept.name, dept.id)

                # تحميل العناوين الوظيفية
                job_titles = session.query(JobTitle).filter_by(is_active=True).all()
                self.emp_job_filter.clear()
                self.emp_job_filter.addItem("جميع العناوين", None)
                for title in job_titles:
                    self.emp_job_filter.addItem(title.title, title.id)

        except Exception as e:
            print(f"خطأ في تحميل فلاتر الموظفين: {e}")

    def load_financial_filters(self):
        """تحميل فلاتر العمليات المالية"""
        try:
            with get_db_session_context() as session:
                # تحميل الموظفين
                employees = session.query(Employee).filter_by(is_active=True).all()
                self.fin_emp_filter.clear()
                self.fin_emp_filter.addItem("جميع الموظفين", None)
                for emp in employees:
                    self.fin_emp_filter.addItem(f"{emp.employee_number} - {emp.full_name}", emp.id)

        except Exception as e:
            print(f"خطأ في تحميل فلاتر العمليات المالية: {e}")

    def load_salary_filters(self):
        """تحميل فلاتر الرواتب"""
        try:
            with get_db_session_context() as session:
                # تحميل الأقسام
                departments = session.query(Department).filter_by(is_active=True).all()
                self.sal_dept_filter.clear()
                self.sal_dept_filter.addItem("جميع الأقسام", None)
                for dept in departments:
                    self.sal_dept_filter.addItem(dept.name, dept.id)

        except Exception as e:
            print(f"خطأ في تحميل فلاتر الرواتب: {e}")

    def load_statement_filters(self):
        """تحميل فلاتر كشف الحساب"""
        try:
            with get_db_session_context() as session:
                # تحميل الموظفين
                employees = session.query(Employee).filter_by(is_active=True).all()
                self.stmt_emp_filter.clear()
                self.stmt_emp_filter.addItem("اختر موظف...", None)
                for emp in employees:
                    self.stmt_emp_filter.addItem(f"{emp.employee_number} - {emp.full_name}", emp.id)

        except Exception as e:
            print(f"خطأ في تحميل فلاتر كشف الحساب: {e}")

    # === وظائف تحميل التقارير ===

    def load_employees_report(self):
        """تحميل تقرير الموظفين"""
        try:
            print("👥 بدء تحميل تقرير الموظفين...")

            with get_db_session_context() as session:
                # بناء الاستعلام
                query = session.query(Employee).filter_by(is_active=True)

                # تطبيق الفلاتر
                dept_id = self.emp_dept_filter.currentData()
                if dept_id:
                    query = query.filter(Employee.department_id == dept_id)

                job_id = self.emp_job_filter.currentData()
                if job_id:
                    query = query.filter(Employee.job_title_id == job_id)

                status_filter = self.emp_status_filter.currentText()
                if status_filter == "نشط":
                    query = query.filter(Employee.is_active == True)
                elif status_filter == "غير نشط":
                    query = query.filter(Employee.is_active == False)

                employees = query.all()

                # إعداد الجدول
                headers = [
                    "الرقم الوظيفي", "الاسم الكامل", "القسم", "العنوان الوظيفي",
                    "الراتب الأساسي", "تاريخ المباشرة", "رقم الهاتف", "الحالة"
                ]

                self.populate_table(self.emp_table, headers, [])

                # ملء البيانات
                for row, emp in enumerate(employees):
                    self.emp_table.insertRow(row)

                    data = [
                        emp.employee_number or "غير محدد",
                        emp.full_name or "غير محدد",
                        emp.department.name if emp.department else "غير محدد",
                        emp.job_title.title if emp.job_title else "غير محدد",
                        f"{float(emp.basic_salary):,.0f} دينار" if emp.basic_salary else "0 دينار",
                        emp.hire_date.strftime('%Y-%m-%d') if emp.hire_date else "غير محدد",
                        getattr(emp, 'phone', None) or "غير محدد",
                        "نشط" if emp.is_active else "غير نشط"
                    ]

                    for col, value in enumerate(data):
                        item = QTableWidgetItem(str(value))
                        item.setTextAlignment(Qt.AlignCenter)
                        self.emp_table.setItem(row, col, item)

                # تحديث معلومات النتائج
                results_info = self.emp_table_frame.findChild(QLabel, "results_info")
                if results_info:
                    results_info.setText(f"📋 إجمالي النتائج: {len(employees)}")

                # تحديث معلومات الفلاتر
                if hasattr(self, 'emp_results_info'):
                    self.emp_results_info.setText(f"📋 إجمالي النتائج: {len(employees)}")

                self.emp_table.resizeColumnsToContents()
                print(f"✅ تم تحميل {len(employees)} موظف")

        except Exception as e:
            print(f"❌ خطأ في تحميل تقرير الموظفين: {e}")
            show_message(self, "خطأ", f"فشل في تحميل تقرير الموظفين: {e}", "error")

    # === وظائف مساعدة ===

    def populate_table(self, table, headers, data):
        """ملء الجدول بالبيانات"""
        table.setRowCount(0)
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)

        for row_data in data:
            row = table.rowCount()
            table.insertRow(row)
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, col, item)

        table.resizeColumnsToContents()

    # === وظائف مسح الفلاتر ===

    def clear_employees_filters(self):
        """مسح فلاتر الموظفين"""
        self.emp_dept_filter.setCurrentIndex(0)
        self.emp_job_filter.setCurrentIndex(0)
        self.emp_status_filter.setCurrentIndex(0)
        self.load_employees_report()

    def clear_financial_filters(self):
        """مسح فلاتر العمليات المالية"""
        self.fin_type_filter.setCurrentIndex(0)
        self.fin_emp_filter.setCurrentIndex(0)
        self.fin_from_date.setDate(QDate.currentDate().addMonths(-3))
        self.fin_to_date.setDate(QDate.currentDate())
        self.load_financial_report()

    def clear_salary_filters(self):
        """مسح فلاتر الرواتب"""
        self.sal_month_filter.setCurrentIndex(0)
        self.sal_year_filter.setCurrentText(str(QDate.currentDate().year()))
        self.sal_dept_filter.setCurrentIndex(0)
        self.sal_status_filter.setCurrentIndex(0)
        self.load_salary_report()

    def clear_statement_filters(self):
        """مسح فلاتر كشف الحساب"""
        self.stmt_emp_filter.setCurrentIndex(0)
        self.stmt_from_date.setDate(QDate.currentDate().addMonths(-6))
        self.stmt_to_date.setDate(QDate.currentDate())
        self.stmt_table.setRowCount(0)

    # === وظائف الطباعة والتصدير ===

    def print_table(self, table, title):
        """طباعة الجدول"""
        try:
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageOrientation(QPrinter.Landscape)

            dialog = QPrintDialog(printer, self)
            if dialog.exec() == QPrintDialog.Accepted:
                self.render_table_for_print(table, title, printer)

        except Exception as e:
            print(f"خطأ في الطباعة: {e}")
            show_message(self, "خطأ", f"فشل في الطباعة: {e}", "error")

    def render_table_for_print(self, table, title, printer):
        """رسم الجدول للطباعة"""
        try:
            painter = QPainter(printer)

            # إعداد الخط
            font = QFont("Arial", 10)
            painter.setFont(font)

            # حساب أبعاد الصفحة
            page_rect = printer.pageRect()
            margin = 50
            content_rect = page_rect.adjusted(margin, margin, -margin, -margin)

            # رسم العنوان
            title_font = QFont("Arial", 16, QFont.Bold)
            painter.setFont(title_font)
            painter.drawText(content_rect.topLeft(), title)

            # رسم التاريخ
            date_font = QFont("Arial", 10)
            painter.setFont(date_font)
            current_date = datetime.now().strftime("%Y-%m-%d %H:%M")
            painter.drawText(content_rect.topRight().x() - 200, content_rect.topLeft().y(),
                           f"تاريخ الطباعة: {current_date}")

            painter.end()
            show_message(self, "نجح", "تم طباعة التقرير بنجاح", "info")

        except Exception as e:
            print(f"خطأ في رسم الجدول للطباعة: {e}")

    def export_table(self, table, title):
        """تصدير الجدول إلى Excel"""
        try:
            import pandas as pd

            # تحويل البيانات إلى DataFrame
            data = []
            headers = []

            # جلب رؤوس الأعمدة
            for col in range(table.columnCount()):
                header = table.horizontalHeaderItem(col).text() if table.horizontalHeaderItem(col) else f"عمود {col+1}"
                headers.append(header)

            # جلب البيانات
            for row in range(table.rowCount()):
                row_data = []
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    text = item.text() if item else ""
                    row_data.append(text)
                data.append(row_data)

            df = pd.DataFrame(data, columns=headers)

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير",
                f"{title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx);;All Files (*)"
            )

            if file_path:
                df.to_excel(file_path, index=False, engine='openpyxl')
                show_message(self, "نجح", f"تم تصدير التقرير إلى:\n{file_path}", "info")

        except ImportError:
            show_message(self, "خطأ", "يجب تثبيت pandas و openpyxl لتصدير Excel", "error")
        except Exception as e:
            print(f"خطأ في التصدير: {e}")
            show_message(self, "خطأ", f"فشل في تصدير التقرير: {e}", "error")

    # === وظائف فارغة للتقارير الأخرى ===

    def load_financial_report(self):
        """تحميل تقرير العمليات المالية"""
        try:
            print("💰 بدء تحميل تقرير العمليات المالية...")

            with get_db_session_context() as session:
                # بناء الاستعلام
                query = session.query(FinancialTransaction).filter_by(is_active=True)

                # تطبيق الفلاتر
                trans_type = self.fin_type_filter.currentText()
                if trans_type != "جميع المعاملات":
                    type_mapping = {
                        "سلفة": "ADVANCE",
                        "خصم": "DEDUCTION",
                        "مكافأة": "BONUS",
                        "دين الماركت": "MARKET_DEBT"
                    }
                    if trans_type in type_mapping:
                        query = query.filter(FinancialTransaction.transaction_type == TransactionType[type_mapping[trans_type]])

                emp_id = self.fin_emp_filter.currentData()
                if emp_id:
                    query = query.filter(FinancialTransaction.employee_id == emp_id)

                from_date = self.fin_from_date.date().toPython()
                to_date = self.fin_to_date.date().toPython()
                query = query.filter(
                    FinancialTransaction.transaction_date >= from_date,
                    FinancialTransaction.transaction_date <= to_date
                )

                transactions = query.order_by(FinancialTransaction.transaction_date.desc()).all()

                # إعداد الجدول
                headers = [
                    "الرقم الوظيفي", "اسم الموظف", "نوع المعاملة", "المبلغ",
                    "تاريخ المعاملة", "الوصف", "حالة الدفع", "المبلغ المدفوع", "المتبقي"
                ]

                self.populate_table(self.fin_table, headers, [])

                # ملء البيانات
                for row, trans in enumerate(transactions):
                    self.fin_table.insertRow(row)

                    # تحديد حالة الدفع
                    status = "معلق"
                    if hasattr(trans, 'payment_status'):
                        if trans.payment_status.name == "PAID":
                            status = "مدفوع"
                        elif trans.payment_status.name == "PARTIALLY_PAID":
                            status = "مدفوع جزئياً"
                        elif trans.payment_status.name == "CANCELLED":
                            status = "ملغي"

                    data = [
                        trans.employee.employee_number if trans.employee else "غير محدد",
                        trans.employee.full_name if trans.employee else "غير محدد",
                        trans.transaction_type.value if hasattr(trans.transaction_type, 'value') else str(trans.transaction_type),
                        f"{float(trans.amount):,.0f} دينار" if trans.amount else "0 دينار",
                        trans.transaction_date.strftime('%Y-%m-%d') if trans.transaction_date else "غير محدد",
                        trans.description or "غير محدد",
                        status,
                        f"{float(trans.paid_amount):,.0f} دينار" if hasattr(trans, 'paid_amount') and trans.paid_amount else "0 دينار",
                        f"{trans.remaining_amount:,.0f} دينار" if hasattr(trans, 'remaining_amount') else "0 دينار"
                    ]

                    for col, value in enumerate(data):
                        item = QTableWidgetItem(str(value))
                        item.setTextAlignment(Qt.AlignCenter)
                        self.fin_table.setItem(row, col, item)

                # تحديث معلومات النتائج
                results_info = self.fin_table_frame.findChild(QLabel, "results_info")
                if results_info:
                    results_info.setText(f"📋 إجمالي النتائج: {len(transactions)}")

                self.fin_table.resizeColumnsToContents()
                print(f"✅ تم تحميل {len(transactions)} معاملة مالية")

        except Exception as e:
            print(f"❌ خطأ في تحميل تقرير العمليات المالية: {e}")
            show_message(self, "خطأ", f"فشل في تحميل تقرير العمليات المالية: {e}", "error")

    def load_salary_report(self):
        """تحميل تقرير الرواتب"""
        try:
            print("💵 بدء تحميل تقرير الرواتب...")

            with get_db_session_context() as session:
                # بناء الاستعلام
                query = session.query(SalaryRecord).filter_by(is_active=True)

                # تطبيق الفلاتر
                month_filter = self.sal_month_filter.currentText()
                if month_filter != "جميع الشهور":
                    month_num = self.sal_month_filter.currentIndex()
                    if month_num > 0:
                        query = query.filter(SalaryRecord.month == month_num)

                year_filter = self.sal_year_filter.currentText()
                if year_filter != "جميع السنوات":
                    try:
                        year_num = int(year_filter)
                        query = query.filter(SalaryRecord.year == year_num)
                    except:
                        pass

                dept_id = self.sal_dept_filter.currentData()
                if dept_id:
                    query = query.join(Employee).filter(Employee.department_id == dept_id)

                status_filter = self.sal_status_filter.currentText()
                if status_filter == "مدفوع":
                    query = query.filter(SalaryRecord.is_paid == True)
                elif status_filter == "غير مدفوع":
                    query = query.filter(SalaryRecord.is_paid == False)

                salary_records = query.order_by(SalaryRecord.year.desc(), SalaryRecord.month.desc()).all()

                # إعداد الجدول
                headers = [
                    "الرقم الوظيفي", "اسم الموظف", "الشهر/السنة", "الراتب الأساسي",
                    "أيام العمل", "المكافآت", "الخصومات", "صافي الراتب", "حالة الصرف", "تاريخ الصرف"
                ]

                self.populate_table(self.sal_table, headers, [])

                # ملء البيانات
                for row, record in enumerate(salary_records):
                    self.sal_table.insertRow(row)

                    data = [
                        record.employee.employee_number if record.employee else "غير محدد",
                        record.employee.full_name if record.employee else "غير محدد",
                        f"{record.month}/{record.year}" if record.month and record.year else "غير محدد",
                        f"{float(record.basic_salary):,.0f} دينار" if record.basic_salary else "0 دينار",
                        str(record.working_days) if record.working_days else "0",
                        f"{float(record.total_bonuses):,.0f} دينار" if hasattr(record, 'total_bonuses') and record.total_bonuses else "0 دينار",
                        f"{float(record.total_deductions):,.0f} دينار" if hasattr(record, 'total_deductions') and record.total_deductions else "0 دينار",
                        f"{float(record.net_salary):,.0f} دينار" if record.net_salary else "0 دينار",
                        "مدفوع" if record.is_paid else "غير مدفوع",
                        record.payment_date.strftime('%Y-%m-%d') if record.payment_date else "غير محدد"
                    ]

                    for col, value in enumerate(data):
                        item = QTableWidgetItem(str(value))
                        item.setTextAlignment(Qt.AlignCenter)
                        self.sal_table.setItem(row, col, item)

                # تحديث معلومات النتائج
                results_info = self.sal_table_frame.findChild(QLabel, "results_info")
                if results_info:
                    results_info.setText(f"📋 إجمالي النتائج: {len(salary_records)}")

                self.sal_table.resizeColumnsToContents()
                print(f"✅ تم تحميل {len(salary_records)} سجل راتب")

        except Exception as e:
            print(f"❌ خطأ في تحميل تقرير الرواتب: {e}")
            show_message(self, "خطأ", f"فشل في تحميل تقرير الرواتب: {e}", "error")

    def load_statistics_report(self):
        """تحميل تقرير الإحصائيات"""
        try:
            print("📈 بدء تحميل تقرير الإحصائيات...")

            with get_db_session_context() as session:
                # إحصائيات الموظفين
                total_employees = session.query(Employee).filter_by(is_active=True).count()
                total_departments = session.query(Department).filter_by(is_active=True).count()
                total_job_titles = session.query(JobTitle).filter_by(is_active=True).count()

                # إحصائيات الرواتب
                salary_records = session.query(SalaryRecord).filter_by(is_active=True).all()
                total_salaries = sum(float(r.net_salary) for r in salary_records if r.net_salary)
                avg_salary = total_salaries / len(salary_records) if salary_records else 0
                paid_salaries = sum(1 for r in salary_records if r.is_paid)

                # إحصائيات المعاملات المالية
                transactions = session.query(FinancialTransaction).filter_by(is_active=True).all()
                total_transactions = len(transactions)
                total_amount = sum(float(t.amount) for t in transactions if t.amount)

                # إحصائيات الأقسام
                departments = session.query(Department).filter_by(is_active=True).all()
                dept_stats = []
                for dept in departments:
                    emp_count = session.query(Employee).filter(
                        Employee.department_id == dept.id,
                        Employee.is_active == True
                    ).count()
                    if emp_count > 0:
                        percentage = (emp_count / total_employees * 100) if total_employees > 0 else 0
                        dept_stats.append((f"موظفو {dept.name}", f"{emp_count} ({percentage:.1f}%)"))

                # تجميع البيانات الإحصائية
                stats_data = [
                    ("إجمالي الموظفين النشطين", str(total_employees)),
                    ("إجمالي الأقسام", str(total_departments)),
                    ("إجمالي العناوين الوظيفية", str(total_job_titles)),
                    ("إجمالي سجلات الرواتب", str(len(salary_records))),
                    ("إجمالي الرواتب المدفوعة", f"{total_salaries:,.0f} دينار"),
                    ("متوسط الراتب", f"{avg_salary:,.0f} دينار"),
                    ("عدد الرواتب المدفوعة", str(paid_salaries)),
                    ("عدد الرواتب غير المدفوعة", str(len(salary_records) - paid_salaries)),
                    ("إجمالي المعاملات المالية", str(total_transactions)),
                    ("إجمالي مبلغ المعاملات", f"{total_amount:,.0f} دينار")
                ]

                # إضافة إحصائيات الأقسام
                stats_data.extend(dept_stats)

                # إعداد الجدول
                headers = ["الإحصائية", "القيمة"]
                self.populate_table(self.stats_table, headers, [])

                # ملء البيانات
                for row, (stat_name, stat_value) in enumerate(stats_data):
                    self.stats_table.insertRow(row)

                    name_item = QTableWidgetItem(stat_name)
                    name_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                    self.stats_table.setItem(row, 0, name_item)

                    value_item = QTableWidgetItem(stat_value)
                    value_item.setTextAlignment(Qt.AlignCenter)
                    self.stats_table.setItem(row, 1, value_item)

                # تحديث معلومات النتائج
                results_info = self.stats_table_frame.findChild(QLabel, "results_info")
                if results_info:
                    results_info.setText(f"📋 إجمالي الإحصائيات: {len(stats_data)}")

                self.stats_table.resizeColumnsToContents()
                print(f"✅ تم تحميل {len(stats_data)} إحصائية")

        except Exception as e:
            print(f"❌ خطأ في تحميل تقرير الإحصائيات: {e}")
            show_message(self, "خطأ", f"فشل في تحميل تقرير الإحصائيات: {e}", "error")

    def load_employee_statement(self):
        """تحميل كشف حساب الموظف"""
        try:
            emp_id = self.stmt_emp_filter.currentData()
            if not emp_id:
                show_message(self, "تنبيه", "يرجى اختيار موظف أولاً", "warning")
                return

            print("📋 بدء تحميل كشف حساب الموظف...")

            with get_db_session_context() as session:
                employee = session.query(Employee).filter_by(id=emp_id).first()
                if not employee:
                    show_message(self, "خطأ", "الموظف غير موجود", "error")
                    return

                from_date = self.stmt_from_date.date().toPython()
                to_date = self.stmt_to_date.date().toPython()

                # جمع جميع العمليات
                operations = []

                # 1. المعاملات المالية
                transactions = session.query(FinancialTransaction).filter(
                    FinancialTransaction.employee_id == emp_id,
                    FinancialTransaction.transaction_date >= from_date,
                    FinancialTransaction.transaction_date <= to_date,
                    FinancialTransaction.is_active == True
                ).all()

                for trans in transactions:
                    operations.append({
                        'date': trans.transaction_date,
                        'type': 'معاملة مالية',
                        'description': f"{trans.transaction_type.value if hasattr(trans.transaction_type, 'value') else str(trans.transaction_type)} - {trans.description or ''}",
                        'debit': float(trans.amount) if trans.transaction_type.name in ['ADVANCE', 'MARKET_DEBT'] else 0,
                        'credit': float(trans.amount) if trans.transaction_type.name in ['BONUS'] else 0,
                        'reference': f"معاملة #{trans.id}"
                    })

                # 2. سجلات الرواتب
                salary_records = session.query(SalaryRecord).filter(
                    SalaryRecord.employee_id == emp_id,
                    SalaryRecord.is_active == True
                ).all()

                for record in salary_records:
                    # تحويل الشهر/السنة إلى تاريخ للمقارنة
                    try:
                        from datetime import date
                        record_date = date(record.year, record.month, 1)
                        if from_date <= record_date <= to_date:
                            operations.append({
                                'date': record_date,
                                'type': 'راتب',
                                'description': f"راتب {record.month}/{record.year}",
                                'debit': 0,
                                'credit': float(record.net_salary) if record.net_salary else 0,
                                'reference': f"راتب #{record.id}"
                            })
                    except:
                        pass

                # ترتيب العمليات حسب التاريخ
                operations.sort(key=lambda x: x['date'])

                # حساب الرصيد التراكمي
                balance = 0
                for op in operations:
                    balance += op['credit'] - op['debit']
                    op['balance'] = balance

                # إعداد الجدول
                headers = [
                    "التاريخ", "نوع العملية", "الوصف", "مدين", "دائن", "الرصيد", "المرجع"
                ]

                self.populate_table(self.stmt_table, headers, [])

                # ملء البيانات
                for row, op in enumerate(operations):
                    self.stmt_table.insertRow(row)

                    data = [
                        op['date'].strftime('%Y-%m-%d'),
                        op['type'],
                        op['description'],
                        f"{op['debit']:,.0f} دينار" if op['debit'] > 0 else "-",
                        f"{op['credit']:,.0f} دينار" if op['credit'] > 0 else "-",
                        f"{op['balance']:,.0f} دينار",
                        op['reference']
                    ]

                    for col, value in enumerate(data):
                        item = QTableWidgetItem(str(value))
                        item.setTextAlignment(Qt.AlignCenter)
                        self.stmt_table.setItem(row, col, item)

                # تحديث معلومات النتائج
                results_info = self.stmt_table_frame.findChild(QLabel, "results_info")
                if results_info:
                    results_info.setText(f"📋 إجمالي العمليات: {len(operations)} | الرصيد النهائي: {balance:,.0f} دينار")

                self.stmt_table.resizeColumnsToContents()
                print(f"✅ تم تحميل {len(operations)} عملية لكشف حساب {employee.full_name}")

        except Exception as e:
            print(f"❌ خطأ في تحميل كشف حساب الموظف: {e}")
            show_message(self, "خطأ", f"فشل في تحميل كشف حساب الموظف: {e}", "error")
