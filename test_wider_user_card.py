#!/usr/bin/env python3
"""
اختبار بطاقة المستخدم الموسعة (400px)
Test Wider User Card (400px)
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QFrame, QHBoxLayout, QPushButton
from PySide6.QtCore import Qt


def create_wider_user_card_test():
    """إنشاء نافذة اختبار بطاقة المستخدم الموسعة"""
    
    class WiderUserCardTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار بطاقة المستخدم الموسعة - 400px")
            self.setGeometry(50, 50, 1400, 800)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            # إنشاء الشريط العلوي مع البطاقة الموسعة
            self.create_wider_header()
            main_layout.addWidget(self.header)
            
            # محتوى الاختبار
            content_widget = QWidget()
            content_layout = QVBoxLayout(content_widget)
            content_layout.setContentsMargins(30, 30, 30, 30)
            content_layout.setSpacing(25)
            
            # عنوان الاختبار
            test_title = QLabel("📏 اختبار بطاقة المستخدم الموسعة - 400px")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 26px;
                    font-weight: bold;
                    color: white;
                    margin: 20px;
                    padding: 20px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                              stop: 0 #667eea, stop: 1 #764ba2);
                    border-radius: 12px;
                }
            """)
            content_layout.addWidget(test_title)
            
            # معلومات التوسيع
            expansion_info = QLabel("""
            📏 توسيع بطاقة المستخدم من 320px إلى 400px:
            
            🎯 التحسين المطبق:
            • العرض الجديد: 400px (زيادة 80px = +25%)
            • الارتفاع: 85px (بدون تغيير)
            • النسبة الجديدة: 4.7:1 (مستطيل أفقي أوسع)
            • مساحة إضافية: 6,800 بكسل (80×85)
            
            ✨ فوائد التوسيع:
            • مساحة أكبر لعرض المعلومات
            • توزيع أفضل للعناصر الثلاثة
            • مظهر أكثر أناقة وفخامة
            • تناسق أفضل مع الشاشات العريضة
            • راحة أكبر في القراءة
            • توازن بصري محسن
            
            📐 توزيع المساحة الإضافية:
            • قسم الأيقونة: مساحة إضافية للتنفس
            • قسم المعلومات: مساحة أوسع للنصوص
            • قسم الحالة: توسيط أفضل للعناصر
            • التباعد العام: توزيع أكثر راحة
            
            🎨 التأثير البصري:
            • مظهر أكثر احترافية
            • تناسق مع العناصر الأخرى
            • استغلال أمثل للمساحة الأفقية
            • تدرج لوني أوسع وأجمل
            • تأثير بصري أقوى
            
            📱 التوافق مع الشاشات:
            • مناسب للشاشات العريضة
            • تناسق مع دقة 1920×1080 وأعلى
            • استغلال أفضل للمساحة المتاحة
            • مظهر متوازن على الشاشات الكبيرة
            
            🔧 التحسينات التقنية:
            • نفس الكود مع تغيير رقم واحد فقط
            • لا تأثير على الأداء
            • سهولة في الصيانة
            • مرونة في التعديل المستقبلي
            
            📊 مقارنة الأحجام:
            • الحجم السابق: 320×85px = 27,200 بكسل
            • الحجم الجديد: 400×85px = 34,000 بكسل
            • الزيادة: 6,800 بكسل (+25%)
            • النسبة السابقة: 3.8:1
            • النسبة الجديدة: 4.7:1
            
            🎯 النتيجة:
            بطاقة مستخدم أوسع وأكثر أناقة مع استغلال أفضل للمساحة!
            """)
            expansion_info.setAlignment(Qt.AlignLeft)
            expansion_info.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #2c3e50;
                    background-color: #f8f9fa;
                    padding: 25px;
                    border-radius: 12px;
                    border-left: 4px solid #667eea;
                    line-height: 1.6;
                    border: 1px solid #dee2e6;
                }
            """)
            content_layout.addWidget(expansion_info)
            
            # مقارنة الأحجام
            comparison_frame = QFrame()
            comparison_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 2px solid #dee2e6;
                    border-radius: 12px;
                    padding: 20px;
                }
            """)
            
            comparison_layout = QVBoxLayout(comparison_frame)
            
            comparison_title = QLabel("📊 مقارنة الأحجام")
            comparison_title.setAlignment(Qt.AlignCenter)
            comparison_title.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 15px;
                    padding: 10px;
                    background-color: #ecf0f1;
                    border-radius: 8px;
                }
            """)
            comparison_layout.addWidget(comparison_title)
            
            comparison_text = QLabel("""
            📏 جدول مقارنة الأحجام:
            
            ┌─────────────────────┬─────────────────┬─────────────────┬─────────────────┐
            │ الخاصية             │ الحجم الأصلي     │ الحجم السابق     │ الحجم الجديد     │
            ├─────────────────────┼─────────────────┼─────────────────┼─────────────────┤
            │ العرض               │ 200px           │ 320px           │ 400px           │
            │ الارتفاع            │ 80px            │ 85px            │ 85px            │
            │ المساحة الكلية       │ 16,000 بكسل     │ 27,200 بكسل     │ 34,000 بكسل     │
            │ النسبة (عرض:ارتفاع) │ 2.5:1           │ 3.8:1           │ 4.7:1           │
            │ الزيادة عن الأصلي    │ -               │ +70%            │ +113%           │
            │ الزيادة عن السابق    │ -               │ -               │ +25%            │
            │ التصنيف            │ مربع            │ مستطيل          │ مستطيل عريض     │
            └─────────────────────┴─────────────────┴─────────────────┴─────────────────┘
            
            📈 تطور الحجم:
            200px → 320px → 400px
            (+60%) → (+25%) = (+100% إجمالي)
            
            🎨 تطور النسبة:
            2.5:1 → 3.8:1 → 4.7:1
            (مربع → مستطيل → مستطيل عريض)
            
            ✅ الفوائد التراكمية:
            • مساحة أكبر للمحتوى
            • مظهر أكثر حداثة
            • تناسق مع الشاشات العريضة
            • توزيع أفضل للعناصر
            • راحة أكبر في القراءة
            
            🎯 النتيجة: تطور مستمر نحو الأفضل!
            """)
            comparison_text.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    color: #34495e;
                    font-family: 'Courier New', monospace;
                    line-height: 1.4;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    border: 1px solid #dee2e6;
                }
            """)
            comparison_layout.addWidget(comparison_text)
            
            content_layout.addWidget(comparison_frame)
            content_layout.addStretch()
            
            main_layout.addWidget(content_widget)
            
        def create_wider_header(self):
            """إنشاء الشريط العلوي مع البطاقة الموسعة"""
            self.header = QFrame()
            self.header.setObjectName("main_header_frame")
            self.header.setFixedHeight(100)

            header_layout = QHBoxLayout(self.header)
            header_layout.setContentsMargins(30, 20, 30, 20)
            header_layout.setSpacing(30)

            # قسم العنوان المحسن
            title_section = QVBoxLayout()
            title_section.setSpacing(8)

            # العنوان الرئيسي المحسن
            main_title = QLabel("🏢 نظام إدارة الموارد البشرية")
            main_title.setAlignment(Qt.AlignLeft)
            main_title.setStyleSheet("""
                QLabel {
                    font-size: 28px;
                    font-weight: bold;
                    color: #2c3e50;
                    letter-spacing: 2px;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                              stop: 0 rgba(52, 152, 219, 0.1),
                                              stop: 1 rgba(155, 89, 182, 0.1));
                    padding: 8px 15px;
                    border-radius: 8px;
                    border-left: 4px solid #3498db;
                }
            """)

            # العنوان الفرعي المحسن
            subtitle = QLabel("💼 نظام شامل ومتطور لإدارة الموظفين والمعاملات المالية")
            subtitle.setAlignment(Qt.AlignLeft)
            subtitle.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    font-weight: 500;
                    color: #7f8c8d;
                    letter-spacing: 1px;
                    padding: 5px 15px;
                    font-style: italic;
                }
            """)

            title_section.addWidget(main_title)
            title_section.addWidget(subtitle)

            # قسم معلومات المستخدم مع البطاقة الموسعة
            user_section = QHBoxLayout()
            user_section.setSpacing(20)

            # بطاقة المستخدم الموسعة (400px)
            user_card = QFrame()
            user_card.setFixedSize(400, 85)  # ← العرض الجديد 400px
            user_card.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                              stop: 0 #667eea, stop: 1 #764ba2);
                    border-radius: 15px;
                    border: none;
                }
            """)
            
            user_card_layout = QHBoxLayout(user_card)
            user_card_layout.setContentsMargins(25, 15, 25, 15)  # هوامش أوسع قليلاً
            user_card_layout.setSpacing(20)  # تباعد أوسع بين الأقسام

            # قسم الأيقونة
            icon_section = QVBoxLayout()
            icon_section.setSpacing(5)
            
            # أيقونة المستخدم
            user_icon = QLabel("👤")
            user_icon.setStyleSheet("""
                QLabel {
                    font-size: 28px;
                    color: white;
                    background-color: rgba(255, 255, 255, 0.25);
                    border-radius: 22px;
                    padding: 8px;
                    min-width: 44px;
                    max-width: 44px;
                    min-height: 44px;
                    max-height: 44px;
                }
            """)
            user_icon.setAlignment(Qt.AlignCenter)
            icon_section.addWidget(user_icon)
            
            user_card_layout.addLayout(icon_section)
            
            # قسم المعلومات (مساحة أوسع)
            info_section = QVBoxLayout()
            info_section.setSpacing(3)
            
            # رسالة الترحيب
            welcome_label = QLabel("مرحباً بك في النظام")
            welcome_label.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 13px;
                    font-weight: 500;
                }
            """)
            info_section.addWidget(welcome_label)
            
            # اسم المستخدم
            user_name = QLabel("المدير العام")
            user_name.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 18px;
                    font-weight: bold;
                }
            """)
            info_section.addWidget(user_name)
            
            user_card_layout.addLayout(info_section)
            
            # قسم الحالة
            status_section = QVBoxLayout()
            status_section.setSpacing(5)
            
            # نقطة الحالة
            status_dot = QLabel("●")
            status_dot.setStyleSheet("""
                QLabel {
                    color: #2ecc71;
                    font-size: 20px;
                }
            """)
            status_dot.setAlignment(Qt.AlignCenter)
            status_section.addWidget(status_dot)
            
            # نص الحالة
            status_text = QLabel("متصل الآن")
            status_text.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 11px;
                    font-weight: 500;
                }
            """)
            status_text.setAlignment(Qt.AlignCenter)
            status_section.addWidget(status_text)
            
            user_card_layout.addLayout(status_section)

            # زر الإعدادات (نفس الشكل السابق)
            settings_btn = QPushButton("⚙️ الإعدادات")
            settings_btn.setFixedSize(140, 50)
            settings_btn.setToolTip("إعدادات النظام")
            settings_btn.clicked.connect(lambda: print("🔄 تم النقر على زر الإعدادات"))
            settings_btn.setStyleSheet("""
                QPushButton {
                    font-size: 14px;
                    font-weight: bold;
                    color: white;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #95a5a6, stop: 1 #7f8c8d);
                    border: none;
                    border-radius: 8px;
                    padding: 12px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #bdc3c7, stop: 1 #95a5a6);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #7f8c8d, stop: 1 #6c7b7d);
                }
            """)

            user_section.addWidget(user_card)
            user_section.addWidget(settings_btn)

            # تجميع الأقسام
            header_layout.addLayout(title_section)
            header_layout.addStretch()
            header_layout.addLayout(user_section)
    
    return WiderUserCardTest()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار بطاقة المستخدم الموسعة - 400px")
    print("=" * 60)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            enhanced_style_file = styles_dir / "enhanced_dashboard.qss"
            
            combined_styles = ""
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read() + "\n"
                    
            if enhanced_style_file.exists():
                with open(enhanced_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read()
                    
            app.setStyleSheet(combined_styles)
            print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_wider_user_card_test()
        window.show()
        
        print("✅ تم فتح نافذة اختبار البطاقة الموسعة!")
        print("\n📏 التوسيع المطبق:")
        print("• العرض الجديد: 400px (كان 320px)")
        print("• الزيادة: +80px (+25%)")
        print("• الارتفاع: 85px (بدون تغيير)")
        print("• النسبة الجديدة: 4.7:1")
        
        print("\n✨ الفوائد:")
        print("• مساحة أكبر للمعلومات")
        print("• توزيع أفضل للعناصر")
        print("• مظهر أكثر أناقة")
        print("• تناسق مع الشاشات العريضة")
        
        print("\n🎨 التحسينات:")
        print("• هوامش أوسع (25px)")
        print("• تباعد أكبر بين الأقسام (20px)")
        print("• نص حالة محسن ('متصل الآن')")
        print("• توزيع متوازن للمساحة")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
