#!/usr/bin/env python3
"""
اختبار الشريط العلوي بنفس تنسيق لوحة التحكم
Test Header with Dashboard Style
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QFrame, QHBoxLayout, QPushButton
from PySide6.QtCore import Qt


def create_dashboard_style_header_test():
    """إنشاء نافذة اختبار الشريط العلوي بتنسيق لوحة التحكم"""
    
    class DashboardStyleHeaderTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار الشريط العلوي - بنفس تنسيق لوحة التحكم")
            self.setGeometry(50, 50, 1400, 900)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            # إنشاء الشريط العلوي بتنسيق لوحة التحكم
            self.create_dashboard_style_header()
            main_layout.addWidget(self.header)
            
            # محتوى الاختبار
            content_widget = QWidget()
            content_layout = QVBoxLayout(content_widget)
            content_layout.setContentsMargins(30, 30, 30, 30)
            content_layout.setSpacing(25)
            
            # عنوان الاختبار
            test_title = QLabel("🎨 اختبار الشريط العلوي - بنفس تنسيق لوحة التحكم")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 26px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 20px;
                    padding: 20px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #9b59b6, stop: 1 #8e44ad);
                    color: white;
                    border-radius: 12px;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                }
            """)
            content_layout.addWidget(test_title)
            
            # معلومات التناسق مع لوحة التحكم
            consistency_info = QLabel("""
            ✨ التناسق مع لوحة التحكم الرئيسية:
            
            🎯 العناصر المتناسقة:
            • نفس إطار العنوان الرئيسي (main_header_frame)
            • نفس العنوان الرئيسي (main_dashboard_title) - 24px
            • نفس العنوان الفرعي (dashboard_subtitle) - 16px
            • نفس بطاقة البيانات (data_card) - 220×140px
            • نفس زر الإجراءات السريعة - 180×50px
            
            📏 أحجام الخطوط المتطابقة:
            • العنوان الرئيسي: 24px (نفس لوحة التحكم)
            • العنوان الفرعي: 16px (نفس لوحة التحكم)
            • عنوان البطاقة: 14px (نفس بطاقات البيانات)
            • قيمة البطاقة: 28px (نفس بطاقات البيانات)
            • العنوان الفرعي للبطاقة: 12px (نفس بطاقات البيانات)
            • زر الإعدادات: 14px (نفس أزرار الإجراءات السريعة)
            
            🎨 الألوان والتدرجات المتطابقة:
            • إطار العنوان: نفس خلفية لوحة التحكم
            • بطاقة المستخدم: تدرج أزرق (نفس بطاقات البيانات)
            • زر الإعدادات: تدرج رمادي (نفس أزرار الإجراءات)
            • الحدود والظلال: نفس التأثيرات البصرية
            
            📐 الأبعاد والتباعد المتطابق:
            • ارتفاع الشريط: 120px (مناسب للمحتوى)
            • الهوامش: 30px من الجانبين، 25px من الأعلى والأسفل
            • التباعد بين العناصر: 30px (نفس لوحة التحكم)
            • حجم بطاقة المستخدم: 220×140px (نفس بطاقات البيانات)
            • حجم زر الإعدادات: 180×50px (نفس أزرار الإجراءات)
            
            🔧 التفاصيل التقنية المتطابقة:
            • نفس أسماء الكلاسات CSS
            • نفس التدرجات اللونية
            • نفس تأثيرات الظلال
            • نفس تباعد الأحرف (letter-spacing)
            • نفس تأثيرات النصوص (text-shadow)
            
            ✅ النتيجة: تناسق مثالي مع لوحة التحكم الرئيسية!
            """)
            consistency_info.setAlignment(Qt.AlignLeft)
            consistency_info.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #2c3e50;
                    background-color: #f8f9fa;
                    padding: 25px;
                    border-radius: 12px;
                    border-left: 4px solid #9b59b6;
                    line-height: 1.6;
                    border: 1px solid #dee2e6;
                }
            """)
            content_layout.addWidget(consistency_info)
            
            # مقارنة مع التصميم السابق
            comparison_frame = QFrame()
            comparison_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 2px solid #dee2e6;
                    border-radius: 12px;
                    padding: 20px;
                }
            """)
            
            comparison_layout = QVBoxLayout(comparison_frame)
            
            comparison_title = QLabel("📊 مقارنة مع التصميم السابق")
            comparison_title.setAlignment(Qt.AlignCenter)
            comparison_title.setStyleSheet("""
                QLabel {
                    font-size: 20px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 15px;
                    padding: 12px;
                    background-color: #ecf0f1;
                    border-radius: 8px;
                }
            """)
            comparison_layout.addWidget(comparison_title)
            
            comparison_text = QLabel("""
            ❌ التصميم السابق (غير متناسق):
            • أحجام خطوط مختلفة (20px موحد)
            • ألوان مختلفة عن لوحة التحكم
            • أبعاد مختلفة للعناصر
            • أنماط CSS منفصلة
            • عدم تناسق مع باقي النظام
            
            ✅ التصميم الجديد (متناسق تماماً):
            • نفس أحجام خطوط لوحة التحكم (24px، 16px، 14px، 28px، 12px)
            • نفس ألوان وتدرجات لوحة التحكم
            • نفس أبعاد العناصر (220×140px للبطاقات، 180×50px للأزرار)
            • نفس أنماط CSS المستخدمة في لوحة التحكم
            • تناسق مثالي مع جميع أجزاء النظام
            
            🎯 الفوائد:
            • مظهر موحد ومتناسق
            • تجربة مستخدم متسقة
            • سهولة في الصيانة والتطوير
            • احترافية أكبر في التصميم
            • تطابق مع معايير التصميم الموحد
            """)
            comparison_text.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #34495e;
                    line-height: 1.6;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                }
            """)
            comparison_layout.addWidget(comparison_text)
            
            content_layout.addWidget(comparison_frame)
            content_layout.addStretch()
            
            main_layout.addWidget(content_widget)
            
        def create_dashboard_style_header(self):
            """إنشاء الشريط العلوي بتنسيق لوحة التحكم"""
            self.header = QFrame()
            self.header.setObjectName("main_header_frame")
            self.header.setFixedHeight(120)

            header_layout = QHBoxLayout(self.header)
            header_layout.setContentsMargins(30, 25, 30, 25)
            header_layout.setSpacing(30)

            # قسم العنوان بنفس تنسيق لوحة التحكم
            title_section = QVBoxLayout()
            title_section.setSpacing(10)

            # العنوان الرئيسي بنفس تنسيق لوحة التحكم
            main_title = QLabel("📊 نظام إدارة الموارد البشرية")
            main_title.setObjectName("main_dashboard_title")
            main_title.setAlignment(Qt.AlignLeft)

            # العنوان الفرعي بنفس تنسيق لوحة التحكم
            subtitle = QLabel("نظام إدارة الموارد البشرية المتقدم")
            subtitle.setObjectName("dashboard_subtitle")
            subtitle.setAlignment(Qt.AlignLeft)

            title_section.addWidget(main_title)
            title_section.addWidget(subtitle)

            # قسم معلومات المستخدم بنفس تنسيق بطاقات لوحة التحكم
            user_section = QHBoxLayout()
            user_section.setSpacing(20)

            # بطاقة المستخدم بنفس تنسيق بطاقات البيانات
            user_card = QFrame()
            user_card.setObjectName("data_card")
            user_card.setFixedSize(220, 140)
            
            user_card_layout = QVBoxLayout(user_card)
            user_card_layout.setContentsMargins(20, 15, 20, 15)
            user_card_layout.setSpacing(8)

            # الأيقونة والعنوان
            header_layout_card = QHBoxLayout()
            
            user_icon = QLabel("👤")
            user_icon.setStyleSheet("font-size: 24px;")
            header_layout_card.addWidget(user_icon)
            
            card_title = QLabel("معلومات المستخدم")
            card_title.setObjectName("card_title")
            card_title.setAlignment(Qt.AlignCenter)
            header_layout_card.addWidget(card_title)
            header_layout_card.addStretch()
            
            user_card_layout.addLayout(header_layout_card)
            
            # القيمة الرئيسية
            user_name = QLabel("المدير العام")
            user_name.setObjectName("card_value")
            user_name.setAlignment(Qt.AlignCenter)
            user_card_layout.addWidget(user_name)
            
            # العنوان الفرعي
            welcome_label = QLabel("مرحباً بك في النظام")
            welcome_label.setObjectName("card_subtitle")
            welcome_label.setAlignment(Qt.AlignCenter)
            user_card_layout.addWidget(welcome_label)
            
            # تطبيق نفس ألوان بطاقات البيانات
            user_card.setStyleSheet("""
                QFrame#data_card {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #3498db, stop: 1 #2980b9);
                    border-radius: 12px;
                    border: 2px solid #2471a3;
                }
                QLabel#card_title {
                    color: rgba(255, 255, 255, 0.95);
                    font-size: 14px;
                    font-weight: bold;
                    letter-spacing: 0.5px;
                }
                QLabel#card_value {
                    color: white;
                    font-size: 28px;
                    font-weight: 900;
                    letter-spacing: 1px;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                }
                QLabel#card_subtitle {
                    color: rgba(255, 255, 255, 0.85);
                    font-size: 12px;
                    font-weight: 500;
                    letter-spacing: 0.3px;
                }
            """)

            # زر الإعدادات بنفس تنسيق أزرار الإجراءات السريعة
            settings_btn = QPushButton("⚙️ الإعدادات")
            settings_btn.setFixedSize(180, 50)
            settings_btn.setToolTip("إعدادات النظام")
            settings_btn.clicked.connect(lambda: print("🔄 تم النقر على زر الإعدادات - تنسيق لوحة التحكم"))
            settings_btn.setStyleSheet("""
                QPushButton {
                    font-size: 14px;
                    font-weight: bold;
                    color: white;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #95a5a6, stop: 1 #7f8c8d);
                    border: none;
                    border-radius: 8px;
                    padding: 12px;
                    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #bdc3c7, stop: 1 #95a5a6);
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #7f8c8d, stop: 1 #6c7b7d);
                    transform: translateY(0px);
                }
            """)

            user_section.addWidget(user_card)
            user_section.addWidget(settings_btn)

            # تجميع الأقسام
            header_layout.addLayout(title_section)
            header_layout.addStretch()
            header_layout.addLayout(user_section)
    
    return DashboardStyleHeaderTest()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الشريط العلوي بتنسيق لوحة التحكم")
    print("=" * 70)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            enhanced_style_file = styles_dir / "enhanced_dashboard.qss"
            
            combined_styles = ""
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read() + "\n"
                    
            if enhanced_style_file.exists():
                with open(enhanced_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read()
                    
            app.setStyleSheet(combined_styles)
            print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_dashboard_style_header_test()
        window.show()
        
        print("✅ تم فتح نافذة اختبار تنسيق لوحة التحكم!")
        print("\n🎨 التناسق المطبق:")
        print("• نفس إطار العنوان الرئيسي")
        print("• نفس أحجام الخطوط (24px، 16px، 14px، 28px، 12px)")
        print("• نفس بطاقة البيانات (220×140px)")
        print("• نفس زر الإجراءات السريعة (180×50px)")
        print("• نفس الألوان والتدرجات")
        
        print("\n📏 أحجام الخطوط المتطابقة:")
        print("• العنوان الرئيسي: 24px")
        print("• العنوان الفرعي: 16px")
        print("• عنوان البطاقة: 14px")
        print("• قيمة البطاقة: 28px")
        print("• العنوان الفرعي للبطاقة: 12px")
        print("• زر الإعدادات: 14px")
        
        print("\n🎯 النتيجة:")
        print("• تناسق مثالي مع لوحة التحكم")
        print("• مظهر موحد ومتسق")
        print("• تجربة مستخدم احترافية")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
