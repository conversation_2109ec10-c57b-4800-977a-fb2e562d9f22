#!/usr/bin/env python3
"""
اختبار تفاصيل الموظف
Test Employee Details
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication
from src.database import db_manager, get_db_session_context
from src.models import Employee
from src.views.employee_details import EmployeeDetailsDialog


def test_employee_details():
    """اختبار نافذة تفاصيل الموظف"""
    print("🧪 اختبار نافذة تفاصيل الموظف...")
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        # البحث عن موظف
        with get_db_session_context() as session:
            employee = session.query(Employee).filter_by(is_active=True).first()
            
            if not employee:
                print("❌ لا يوجد موظفين في النظام")
                return False
            
            print(f"✅ تم العثور على الموظف: {employee.full_name}")
            print(f"   معرف الموظف: {employee.id}")
            print(f"   الرقم الوظيفي: {employee.employee_number}")
            
            # إنشاء التطبيق
            app = QApplication(sys.argv)
            
            # إنشاء نافذة التفاصيل
            print("\n🔧 إنشاء نافذة تفاصيل الموظف...")
            dialog = EmployeeDetailsDialog(employee.id)
            
            # اختبار تحميل البيانات
            print("📊 اختبار تحميل البيانات...")
            dialog.load_employee_data()
            
            if hasattr(dialog, 'employee') and dialog.employee:
                print(f"✅ تم تحميل بيانات الموظف: {dialog.employee.full_name}")
                
                # اختبار تحميل المعلومات الأساسية
                dialog.load_basic_info()
                print("✅ تم تحميل المعلومات الأساسية")
                
                # اختبار تحميل المعاملات المالية
                dialog.load_financial_transactions()
                print("✅ تم تحميل المعاملات المالية")
                
                # اختبار تحميل سجل الرواتب
                dialog.load_salary_records()
                print("✅ تم تحميل سجل الرواتب")
                
                # عرض النافذة
                print("\n🖥️ عرض نافذة التفاصيل...")
                dialog.show()
                
                print("✅ تم فتح نافذة تفاصيل الموظف بنجاح!")
                print("\nيمكنك الآن:")
                print("1. مشاهدة المعلومات الأساسية للموظف")
                print("2. مراجعة المعاملات المالية")
                print("3. مراجعة سجل الرواتب")
                print("4. إغلاق النافذة")
                print("\nاضغط Ctrl+C لإغلاق النافذة")
                
                # تشغيل التطبيق
                return app.exec()
            else:
                print("❌ فشل في تحميل بيانات الموظف")
                return False
            
    except KeyboardInterrupt:
        print("\n👋 تم إغلاق النافذة")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في اختبار نافذة التفاصيل: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_employee_details_simple():
    """اختبار بسيط لتفاصيل الموظف بدون واجهة"""
    print("🧪 اختبار بسيط لتفاصيل الموظف...")
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        # البحث عن موظف
        with get_db_session_context() as session:
            employee = session.query(Employee).filter_by(is_active=True).first()
            
            if not employee:
                print("❌ لا يوجد موظفين في النظام")
                return False
            
            print(f"✅ الموظف: {employee.full_name}")
            print(f"   الرقم الوظيفي: {employee.employee_number}")
            print(f"   القسم: {employee.department.name if employee.department else 'غير محدد'}")
            print(f"   العنوان الوظيفي: {employee.job_title.title if employee.job_title else 'غير محدد'}")
            print(f"   الراتب الأساسي: {employee.basic_salary:,.0f} د.ع")
            print(f"   تاريخ التوظيف: {employee.hire_date}")
            print(f"   الحالة: {'نشط' if employee.is_active else 'غير نشط'}")
            
            # اختبار المعاملات المالية
            from src.models import FinancialTransaction
            transactions = session.query(FinancialTransaction).filter_by(
                employee_id=employee.id,
                is_active=True
            ).all()
            
            print(f"\n💰 المعاملات المالية: {len(transactions)} معاملة")
            for transaction in transactions[:5]:  # أول 5 معاملات
                print(f"   {transaction.transaction_type.value}: {transaction.amount:,.0f} د.ع - {transaction.transaction_date}")
            
            # اختبار سجل الرواتب
            from src.models import SalaryRecord
            salary_records = session.query(SalaryRecord).filter_by(
                employee_id=employee.id
            ).all()
            
            print(f"\n💵 سجل الرواتب: {len(salary_records)} سجل")
            for record in salary_records[:3]:  # أول 3 سجلات
                print(f"   {record.month}/{record.year}: صافي الراتب {record.net_salary:,.0f} د.ع")
            
            print("\n✅ جميع البيانات متاحة وصحيحة!")
            return True
            
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار نافذة تفاصيل الموظف")
    print("=" * 50)
    
    # اختبار بسيط أولاً
    if not test_employee_details_simple():
        print("💥 فشل الاختبار البسيط!")
        return 1
    
    print("\n" + "=" * 50)
    print("هل تريد فتح نافذة التفاصيل؟ (y/n): ", end="")
    
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', 'نعم', '1']:
            result = test_employee_details()
            if result == 0:
                print("✅ اختبار نافذة التفاصيل نجح!")
                return 0
            else:
                print("💥 فشل اختبار نافذة التفاصيل!")
                return 1
        else:
            print("✅ تم تخطي اختبار النافذة")
            return 0
    except KeyboardInterrupt:
        print("\n👋 تم إلغاء الاختبار")
        return 0


if __name__ == "__main__":
    sys.exit(main())
