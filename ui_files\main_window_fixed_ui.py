# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'main_window_fixed.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (Q<PERSON>rush, QColor, QC<PERSON>alGradient, QCursor,
    QF<PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QFrame, QHBoxLayout, QLabel,
    QMainWindow, QMenuBar, QPushButton, QScrollArea,
    QSizePolicy, QSpacerItem, QStackedWidget, QStatusBar,
    QVBoxLayout, QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1400, 900)
        MainWindow.setMinimumSize(QSize(1200, 800))
        MainWindow.setStyleSheet(u"QMainWindow {\n"
"    background-color: #f8f9fa;\n"
"}\n"
"\n"
"QMenuBar {\n"
"    background-color: #2c3e50;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 5px;\n"
"}\n"
"\n"
"QMenuBar::item {\n"
"    background-color: transparent;\n"
"    padding: 8px 12px;\n"
"    margin: 2px;\n"
"    border-radius: 4px;\n"
"}\n"
"\n"
"QMenuBar::item:selected {\n"
"    background-color: #34495e;\n"
"}\n"
"\n"
"QStatusBar {\n"
"    background-color: #34495e;\n"
"    color: white;\n"
"    border-top: 2px solid #3498db;\n"
"    padding: 5px;\n"
"}")
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.main_layout = QHBoxLayout(self.centralwidget)
        self.main_layout.setSpacing(0)
        self.main_layout.setObjectName(u"main_layout")
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.sidebar_frame = QFrame(self.centralwidget)
        self.sidebar_frame.setObjectName(u"sidebar_frame")
        self.sidebar_frame.setMinimumSize(QSize(250, 0))
        self.sidebar_frame.setMaximumSize(QSize(250, 16777215))
        self.sidebar_frame.setStyleSheet(u"QFrame {\n"
"    background-color: #2c3e50;\n"
"    border: none;\n"
"}")
        self.sidebar_layout = QVBoxLayout(self.sidebar_frame)
        self.sidebar_layout.setSpacing(0)
        self.sidebar_layout.setObjectName(u"sidebar_layout")
        self.sidebar_layout.setContentsMargins(0, 0, 0, 0)
        self.logo_frame = QFrame(self.sidebar_frame)
        self.logo_frame.setObjectName(u"logo_frame")
        self.logo_frame.setMinimumSize(QSize(0, 80))
        self.logo_frame.setStyleSheet(u"QFrame {\n"
"    background-color: #34495e;\n"
"    border-bottom: 2px solid #3498db;\n"
"}")
        self.logo_layout = QHBoxLayout(self.logo_frame)
        self.logo_layout.setObjectName(u"logo_layout")
        self.logo_label = QLabel(self.logo_frame)
        self.logo_label.setObjectName(u"logo_label")
        self.logo_label.setAlignment(Qt.AlignCenter)
        self.logo_label.setStyleSheet(u"QLabel {\n"
"    color: white;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"    padding: 10px;\n"
"}")

        self.logo_layout.addWidget(self.logo_label)


        self.sidebar_layout.addWidget(self.logo_frame)

        self.menu_scroll_area = QScrollArea(self.sidebar_frame)
        self.menu_scroll_area.setObjectName(u"menu_scroll_area")
        self.menu_scroll_area.setStyleSheet(u"QScrollArea {\n"
"    border: none;\n"
"    background-color: transparent;\n"
"}")
        self.menu_scroll_area.setWidgetResizable(True)
        self.menu_content = QWidget()
        self.menu_content.setObjectName(u"menu_content")
        self.menu_content.setGeometry(QRect(0, 0, 248, 718))
        self.menu_layout = QVBoxLayout(self.menu_content)
        self.menu_layout.setSpacing(5)
        self.menu_layout.setObjectName(u"menu_layout")
        self.menu_layout.setContentsMargins(10, 20, 10, 20)
        self.dashboard_btn = QPushButton(self.menu_content)
        self.dashboard_btn.setObjectName(u"dashboard_btn")
        self.dashboard_btn.setMinimumSize(QSize(0, 50))
        self.dashboard_btn.setStyleSheet(u"QPushButton {\n"
"    background-color: #3498db;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 8px;\n"
"    padding: 12px;\n"
"    text-align: left;\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #2980b9;\n"
"}")

        self.menu_layout.addWidget(self.dashboard_btn)

        self.employees_btn = QPushButton(self.menu_content)
        self.employees_btn.setObjectName(u"employees_btn")
        self.employees_btn.setMinimumSize(QSize(0, 50))
        self.employees_btn.setStyleSheet(u"QPushButton {\n"
"    background-color: transparent;\n"
"    color: #ecf0f1;\n"
"    border: none;\n"
"    border-radius: 8px;\n"
"    padding: 12px;\n"
"    text-align: left;\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #34495e;\n"
"    color: #3498db;\n"
"}")

        self.menu_layout.addWidget(self.employees_btn)

        self.departments_btn = QPushButton(self.menu_content)
        self.departments_btn.setObjectName(u"departments_btn")
        self.departments_btn.setMinimumSize(QSize(0, 50))
        self.departments_btn.setStyleSheet(u"QPushButton {\n"
"    background-color: transparent;\n"
"    color: #ecf0f1;\n"
"    border: none;\n"
"    border-radius: 8px;\n"
"    padding: 12px;\n"
"    text-align: left;\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #34495e;\n"
"    color: #3498db;\n"
"}")

        self.menu_layout.addWidget(self.departments_btn)

        self.salaries_btn = QPushButton(self.menu_content)
        self.salaries_btn.setObjectName(u"salaries_btn")
        self.salaries_btn.setMinimumSize(QSize(0, 50))
        self.salaries_btn.setStyleSheet(u"QPushButton {\n"
"    background-color: transparent;\n"
"    color: #ecf0f1;\n"
"    border: none;\n"
"    border-radius: 8px;\n"
"    padding: 12px;\n"
"    text-align: left;\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #34495e;\n"
"    color: #3498db;\n"
"}")

        self.menu_layout.addWidget(self.salaries_btn)

        self.reports_btn = QPushButton(self.menu_content)
        self.reports_btn.setObjectName(u"reports_btn")
        self.reports_btn.setMinimumSize(QSize(0, 50))
        self.reports_btn.setStyleSheet(u"QPushButton {\n"
"    background-color: transparent;\n"
"    color: #ecf0f1;\n"
"    border: none;\n"
"    border-radius: 8px;\n"
"    padding: 12px;\n"
"    text-align: left;\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #34495e;\n"
"    color: #3498db;\n"
"}")

        self.menu_layout.addWidget(self.reports_btn)

        self.menu_spacer = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.menu_layout.addItem(self.menu_spacer)

        self.menu_scroll_area.setWidget(self.menu_content)

        self.sidebar_layout.addWidget(self.menu_scroll_area)


        self.main_layout.addWidget(self.sidebar_frame)

        self.content_frame = QFrame(self.centralwidget)
        self.content_frame.setObjectName(u"content_frame")
        self.content_frame.setStyleSheet(u"QFrame {\n"
"    background-color: #ecf0f1;\n"
"    border: none;\n"
"}")
        self.content_layout = QVBoxLayout(self.content_frame)
        self.content_layout.setSpacing(0)
        self.content_layout.setObjectName(u"content_layout")
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_stack = QStackedWidget(self.content_frame)
        self.content_stack.setObjectName(u"content_stack")
        self.dashboard_page = QWidget()
        self.dashboard_page.setObjectName(u"dashboard_page")
        self.dashboard_layout = QVBoxLayout(self.dashboard_page)
        self.dashboard_layout.setObjectName(u"dashboard_layout")
        self.dashboard_label = QLabel(self.dashboard_page)
        self.dashboard_label.setObjectName(u"dashboard_label")
        self.dashboard_label.setAlignment(Qt.AlignCenter)
        self.dashboard_label.setStyleSheet(u"QLabel {\n"
"    font-size: 24px;\n"
"    font-weight: bold;\n"
"    color: #2c3e50;\n"
"    padding: 20px;\n"
"}")

        self.dashboard_layout.addWidget(self.dashboard_label)

        self.content_stack.addWidget(self.dashboard_page)

        self.content_layout.addWidget(self.content_stack)


        self.main_layout.addWidget(self.content_frame)

        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QMenuBar(MainWindow)
        self.menubar.setObjectName(u"menubar")
        self.menubar.setGeometry(QRect(0, 0, 1400, 22))
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"\ud83c\udfe2 \u0646\u0638\u0627\u0645 \u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0645\u0648\u0627\u0631\u062f \u0627\u0644\u0628\u0634\u0631\u064a\u0629 \u0627\u0644\u0645\u062a\u0642\u062f\u0645", None))
        self.logo_label.setText(QCoreApplication.translate("MainWindow", u"\ud83c\udfe2 \u0646\u0638\u0627\u0645 \u0627\u0644\u0645\u0648\u0627\u0631\u062f \u0627\u0644\u0628\u0634\u0631\u064a\u0629", None))
        self.dashboard_btn.setText(QCoreApplication.translate("MainWindow", u"\ud83d\udcca \u0644\u0648\u062d\u0629 \u0627\u0644\u062a\u062d\u0643\u0645", None))
        self.employees_btn.setText(QCoreApplication.translate("MainWindow", u"\ud83d\udc65 \u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0645\u0648\u0638\u0641\u064a\u0646", None))
        self.departments_btn.setText(QCoreApplication.translate("MainWindow", u"\ud83c\udfe2 \u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0623\u0642\u0633\u0627\u0645", None))
        self.salaries_btn.setText(QCoreApplication.translate("MainWindow", u"\ud83d\udcb0 \u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0631\u0648\u0627\u062a\u0628", None))
        self.reports_btn.setText(QCoreApplication.translate("MainWindow", u"\ud83d\udcca \u0627\u0644\u062a\u0642\u0627\u0631\u064a\u0631", None))
        self.dashboard_label.setText(QCoreApplication.translate("MainWindow", u"\ud83d\udcca \u0644\u0648\u062d\u0629 \u0627\u0644\u062a\u062d\u0643\u0645 \u0627\u0644\u0631\u0626\u064a\u0633\u064a\u0629", None))
    # retranslateUi

