"""
نموذج إضافة/تعديل المعاملة المالية
Financial Transaction Add/Edit Form
"""

from datetime import date
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit, QComboBox,
    QDateEdit, QTextEdit, QPushButton, QLabel, QFrame, QGroupBox, QCheckBox
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont, QDoubleValidator

from ..utils import (
    apply_rtl_layout, show_message, clean_numeric_input, setup_combobox,
    get_combobox_value, set_combobox_value
)
from ..database import get_db_session_context
from ..models import FinancialTransaction, Employee, TransactionType, PaymentStatus


class FinancialForm(QDialog):
    """نموذج إضافة/تعديل المعاملة المالية"""
    
    # إشارات
    transaction_saved = Signal(int)  # إشارة حفظ المعاملة
    
    def __init__(self, transaction_id: int = None, parent=None):
        super().__init__(parent)
        self.transaction_id = transaction_id
        self.is_edit_mode = transaction_id is not None
        self.transaction = None
        
        self.setup_ui()
        self.setup_connections()
        self.load_reference_data()
        
        if self.is_edit_mode:
            self.load_transaction_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم المحسن"""
        self.setObjectName("financial_form")
        apply_rtl_layout(self)

        # إعداد النافذة المحسنة
        title = "💰 تعديل المعاملة المالية" if self.is_edit_mode else "💰 إضافة معاملة مالية جديدة"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(650, 750)  # حجم أكبر للتصميم المحسن

        # تطبيق الستايل الذهبي المحسن
        self.setStyleSheet("""
            QDialog {
                background-color: #fffbf0;
                border: 2px solid #ffc107;
                border-radius: 15px;
            }
            QGroupBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #ffc107;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #ffc107;
                color: #212529;
                border-radius: 5px;
                font-weight: bold;
            }
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 13px;
                font-weight: 600;
                color: #2c3e50;
            }
            QLineEdit, QComboBox, QDateEdit, QTextEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 13px;
                padding: 8px 12px;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: #ffffff;
                min-height: 20px;
            }
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus, QTextEdit:focus {
                border-color: #ffc107;
                background-color: #fff3cd;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6c757d;
                margin-right: 5px;
            }
        """)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(25, 25, 25, 25)
        main_layout.setSpacing(20)

        # إنشاء رأس النموذج المحسن
        self.create_enhanced_header(main_layout)

        # إنشاء مجموعات الحقول المحسنة
        self.create_enhanced_transaction_info_group(main_layout)
        self.create_enhanced_payment_info_group(main_layout)

        # إنشاء أزرار الإجراءات المحسنة
        self.create_enhanced_action_buttons(main_layout)
        
    def create_enhanced_header(self, layout: QVBoxLayout):
        """إنشاء رأس النموذج المحسن"""
        # إنشاء إطار العنوان الذهبي
        header_frame = QFrame()
        header_frame.setObjectName("header_frame")
        header_frame.setFixedHeight(120)
        header_frame.setStyleSheet("""
            QFrame#header_frame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #fff3cd, stop:0.5 #ffc107, stop:1 #e0a800);
                border: 3px solid #e0a800;
                border-radius: 15px;
                margin: 5px;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 15, 20, 15)
        header_layout.setSpacing(8)

        # العنوان الرئيسي
        title = "💰 تعديل بيانات المعاملة المالية" if self.is_edit_mode else "💰 إضافة معاملة مالية جديدة"
        self.main_title = QLabel(title)
        self.main_title.setAlignment(Qt.AlignCenter)
        self.main_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 20px;
                font-weight: bold;
                color: #212529;
                background-color: transparent;
                padding: 5px;
                margin: 0px;
            }
        """)

        # العنوان الفرعي
        subtitle = "💳 تعديل بيانات المعاملة المالية الموجودة" if self.is_edit_mode else "💳 إدخال بيانات معاملة مالية جديدة للنظام"
        self.subtitle = QLabel(subtitle)
        self.subtitle.setAlignment(Qt.AlignCenter)
        self.subtitle.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 13px;
                font-weight: 600;
                color: #6c5ce7;
                background-color: rgba(255, 255, 255, 0.8);
                padding: 8px 15px;
                border-radius: 8px;
                border: 1px solid #a29bfe;
                margin: 2px;
            }
        """)

        # رقم المعاملة (في حالة التعديل)
        if self.is_edit_mode:
            self.transaction_id_label = QLabel(f"🔢 رقم المعاملة: {self.transaction_id}")
            self.transaction_id_label.setAlignment(Qt.AlignCenter)
            self.transaction_id_label.setStyleSheet("""
                QLabel {
                    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                    font-size: 12px;
                    font-weight: bold;
                    color: #e17055;
                    background-color: rgba(255, 255, 255, 0.9);
                    padding: 4px 10px;
                    border-radius: 5px;
                    border: 1px solid #fd79a8;
                }
            """)
            header_layout.addWidget(self.transaction_id_label)

        header_layout.addWidget(self.main_title)
        header_layout.addWidget(self.subtitle)

        layout.addWidget(header_frame)
        
    def create_enhanced_transaction_info_group(self, layout: QVBoxLayout):
        """إنشاء مجموعة معلومات المعاملة المحسنة"""
        group = QGroupBox("📋 معلومات المعاملة الأساسية")
        group_layout = QFormLayout(group)
        group_layout.setSpacing(15)
        group_layout.setContentsMargins(20, 25, 20, 20)

        # الموظف مع أيقونة
        employee_label = QLabel("👤 الموظف *:")
        employee_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px 0px;
            }
        """)

        self.employee_combo = QComboBox()
        self.employee_combo.setEditable(True)
        self.employee_combo.setMinimumWidth(300)
        self.employee_combo.setMinimumHeight(40)
        self.employee_combo.setStyleSheet("""
            QComboBox {
                font-size: 14px;
                padding: 10px 15px;
                border: 2px solid #ffc107;
                border-radius: 10px;
                background-color: #fffbf0;
            }
            QComboBox:focus {
                border-color: #e0a800;
                background-color: #fff3cd;
            }
            QComboBox::drop-down {
                border: none;
                width: 35px;
                background-color: #ffc107;
                border-radius: 8px;
                margin: 2px;
            }
        """)
        group_layout.addRow(employee_label, self.employee_combo)

        # نوع المعاملة مع أيقونة
        type_label = QLabel("💳 نوع المعاملة *:")
        type_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px 0px;
            }
        """)

        self.transaction_type_combo = QComboBox()
        self.transaction_type_combo.setMinimumHeight(40)
        self.transaction_type_combo.setStyleSheet("""
            QComboBox {
                font-size: 14px;
                padding: 10px 15px;
                border: 2px solid #ffc107;
                border-radius: 10px;
                background-color: #fffbf0;
            }
            QComboBox:focus {
                border-color: #e0a800;
                background-color: #fff3cd;
            }
            QComboBox::drop-down {
                border: none;
                width: 35px;
                background-color: #ffc107;
                border-radius: 8px;
                margin: 2px;
            }
        """)
        group_layout.addRow(type_label, self.transaction_type_combo)

        # المبلغ مع أيقونة
        amount_label = QLabel("💰 المبلغ *:")
        amount_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px 0px;
            }
        """)

        self.amount_input = QLineEdit()
        self.amount_input.setPlaceholderText("أدخل المبلغ (مثال: 100,000)")
        self.amount_input.setMinimumHeight(40)
        validator = QDoubleValidator(0.0, 999999999.99, 2)
        self.amount_input.setValidator(validator)
        self.amount_input.setStyleSheet("""
            QLineEdit {
                font-size: 14px;
                padding: 10px 15px;
                border: 2px solid #ffc107;
                border-radius: 10px;
                background-color: #fffbf0;
            }
            QLineEdit:focus {
                border-color: #e0a800;
                background-color: #fff3cd;
            }
        """)
        group_layout.addRow(amount_label, self.amount_input)

        # تاريخ المعاملة مع أيقونة
        date_label = QLabel("📅 تاريخ المعاملة *:")
        date_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px 0px;
            }
        """)

        self.transaction_date_input = QDateEdit()
        self.transaction_date_input.setDate(QDate.currentDate())
        self.transaction_date_input.setMaximumDate(QDate.currentDate())
        self.transaction_date_input.setCalendarPopup(True)
        self.transaction_date_input.setMinimumHeight(40)
        self.transaction_date_input.setStyleSheet("""
            QDateEdit {
                font-size: 14px;
                padding: 10px 15px;
                border: 2px solid #ffc107;
                border-radius: 10px;
                background-color: #fffbf0;
            }
            QDateEdit:focus {
                border-color: #e0a800;
                background-color: #fff3cd;
            }
        """)
        group_layout.addRow(date_label, self.transaction_date_input)

        # الملاحظات مع أيقونة
        notes_label = QLabel("📝 الملاحظات:")
        notes_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px 0px;
            }
        """)

        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(100)
        self.description_input.setPlaceholderText("أدخل أي ملاحظات إضافية حول هذه المعاملة...")
        self.description_input.setStyleSheet("""
            QTextEdit {
                font-size: 13px;
                padding: 10px 15px;
                border: 2px solid #ffc107;
                border-radius: 10px;
                background-color: #fffbf0;
            }
            QTextEdit:focus {
                border-color: #e0a800;
                background-color: #fff3cd;
            }
        """)
        group_layout.addRow(notes_label, self.description_input)

        layout.addWidget(group)
        
    def create_enhanced_payment_info_group(self, layout: QVBoxLayout):
        """إنشاء مجموعة معلومات الدفع المحسنة"""
        self.payment_group = QGroupBox("💳 معلومات الدفع (للسلف والديون)")
        group_layout = QFormLayout(self.payment_group)
        group_layout.setSpacing(15)
        group_layout.setContentsMargins(20, 25, 20, 20)

        # حالة الدفع مع أيقونة
        status_label = QLabel("📊 حالة الدفع:")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px 0px;
            }
        """)

        self.payment_status_combo = QComboBox()
        self.payment_status_combo.setMinimumHeight(40)
        self.payment_status_combo.setStyleSheet("""
            QComboBox {
                font-size: 14px;
                padding: 10px 15px;
                border: 2px solid #17a2b8;
                border-radius: 10px;
                background-color: #e7f3ff;
            }
            QComboBox:focus {
                border-color: #138496;
                background-color: #d1ecf1;
            }
            QComboBox::drop-down {
                border: none;
                width: 35px;
                background-color: #17a2b8;
                border-radius: 8px;
                margin: 2px;
            }
        """)
        group_layout.addRow(status_label, self.payment_status_combo)

        # المبلغ المدفوع مع أيقونة
        paid_label = QLabel("💵 المبلغ المدفوع:")
        paid_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px 0px;
            }
        """)

        self.paid_amount_input = QLineEdit()
        self.paid_amount_input.setPlaceholderText("أدخل المبلغ المدفوع (افتراضي: 0)")
        self.paid_amount_input.setMinimumHeight(40)
        validator_paid = QDoubleValidator(0.0, 999999999.99, 2)
        self.paid_amount_input.setValidator(validator_paid)
        self.paid_amount_input.setStyleSheet("""
            QLineEdit {
                font-size: 14px;
                padding: 10px 15px;
                border: 2px solid #28a745;
                border-radius: 10px;
                background-color: #f0fff4;
            }
            QLineEdit:focus {
                border-color: #1e7e34;
                background-color: #d4edda;
            }
        """)
        group_layout.addRow(paid_label, self.paid_amount_input)

        # تاريخ الدفع مع أيقونة
        payment_date_label = QLabel("📅 تاريخ الدفع:")
        payment_date_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px 0px;
            }
        """)

        self.payment_date_input = QDateEdit()
        self.payment_date_input.setDate(QDate.currentDate())
        self.payment_date_input.setCalendarPopup(True)
        self.payment_date_input.setEnabled(False)
        self.payment_date_input.setMinimumHeight(40)
        self.payment_date_input.setStyleSheet("""
            QDateEdit {
                font-size: 14px;
                padding: 10px 15px;
                border: 2px solid #6c757d;
                border-radius: 10px;
                background-color: #f8f9fa;
            }
            QDateEdit:enabled {
                border-color: #007bff;
                background-color: #e7f1ff;
            }
            QDateEdit:enabled:focus {
                border-color: #0056b3;
                background-color: #cce7ff;
            }
        """)
        group_layout.addRow(payment_date_label, self.payment_date_input)

        # خانة اختيار الدفع الكامل مع تصميم محسن
        self.full_payment_checkbox = QCheckBox("✅ دفع كامل (تحديد المبلغ تلقائياً)")
        self.full_payment_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 14px;
                font-weight: bold;
                color: #155724;
                padding: 10px;
                background-color: #d4edda;
                border: 2px solid #28a745;
                border-radius: 10px;
                margin: 5px 0px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #28a745;
                border-radius: 5px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #28a745;
                image: none;
            }
            QCheckBox::indicator:checked:after {
                content: "✓";
                color: white;
                font-weight: bold;
            }
        """)
        group_layout.addRow("", self.full_payment_checkbox)

        layout.addWidget(self.payment_group)
        
    def create_enhanced_action_buttons(self, layout: QVBoxLayout):
        """إنشاء أزرار الإجراءات المحسنة"""
        # إطار الأزرار المحسن
        buttons_frame = QFrame()
        buttons_frame.setObjectName("buttons_frame")
        buttons_frame.setStyleSheet("""
            QFrame#buttons_frame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 15px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)
        buttons_layout.setContentsMargins(20, 15, 20, 15)

        # زر الحفظ المحسن
        save_text = "💾 تحديث البيانات" if self.is_edit_mode else "💾 حفظ المعاملة"
        self.save_btn = QPushButton(save_text)
        self.save_btn.setMinimumWidth(150)
        self.save_btn.setMinimumHeight(50)
        self.save_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 15px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                border: 3px solid #1e7e34;
                border-radius: 12px;
                padding: 12px 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34ce57, stop:1 #28a745);
                border-color: #155724;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e7e34, stop:1 #155724);
                transform: translateY(1px);
            }
        """)

        # زر الإلغاء المحسن
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setMinimumWidth(120)
        self.cancel_btn.setMinimumHeight(50)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 15px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc3545, stop:1 #c82333);
                border: 3px solid #bd2130;
                border-radius: 12px;
                padding: 12px 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e4606d, stop:1 #dc3545);
                border-color: #a71e2a;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c82333, stop:1 #a71e2a);
                transform: translateY(1px);
            }
        """)

        # زر إعادة تعيين (للنموذج الجديد فقط)
        if not self.is_edit_mode:
            self.reset_btn = QPushButton("🔄 إعادة تعيين")
            self.reset_btn.setMinimumWidth(120)
            self.reset_btn.setMinimumHeight(50)
            self.reset_btn.setStyleSheet("""
                QPushButton {
                    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                    font-size: 15px;
                    font-weight: bold;
                    color: white;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #6c757d, stop:1 #5a6268);
                    border: 3px solid #495057;
                    border-radius: 12px;
                    padding: 12px 20px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #7c8a96, stop:1 #6c757d);
                    border-color: #3d4142;
                    transform: translateY(-2px);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #5a6268, stop:1 #495057);
                    transform: translateY(1px);
                }
            """)
            buttons_layout.addWidget(self.reset_btn)

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات المحسنة"""
        self.save_btn.clicked.connect(self.save_transaction)
        self.cancel_btn.clicked.connect(self.reject)

        # ربط زر إعادة التعيين (للنموذج الجديد فقط)
        if not self.is_edit_mode and hasattr(self, 'reset_btn'):
            self.reset_btn.clicked.connect(self.reset_form)

        # ربط تغيير نوع المعاملة بإظهار/إخفاء معلومات الدفع
        self.transaction_type_combo.currentTextChanged.connect(self.update_payment_visibility)

        # ربط خانة الدفع الكامل
        self.full_payment_checkbox.toggled.connect(self.on_full_payment_toggled)

        # ربط تغيير المبلغ المدفوع
        self.paid_amount_input.textChanged.connect(self.update_payment_date_status)

        # ربط تغيير المبلغ الأصلي مع خانة الدفع الكامل
        self.amount_input.textChanged.connect(self.on_amount_changed)
        
    def load_reference_data(self):
        """تحميل البيانات المرجعية"""
        try:
            with get_db_session_context() as session:
                # تحميل الموظفين
                employees = session.query(Employee).filter_by(is_active=True).all()
                employee_items = [(f"{emp.employee_number} - {emp.full_name}", emp.id) for emp in employees]
                setup_combobox(self.employee_combo, employee_items)
                
                # تحميل أنواع المعاملات
                type_items = [(trans_type.value, trans_type.name) for trans_type in TransactionType]
                setup_combobox(self.transaction_type_combo, type_items)
                
                # تحميل حالات الدفع
                status_items = [(status.value, status.name) for status in PaymentStatus]
                setup_combobox(self.payment_status_combo, status_items, PaymentStatus.PENDING.name)
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل البيانات المرجعية: {e}", "error")
            
    def update_payment_visibility(self):
        """تحديث إظهار/إخفاء معلومات الدفع"""
        transaction_type = get_combobox_value(self.transaction_type_combo)
        
        # إظهار معلومات الدفع للسلف والديون فقط
        is_debt_type = transaction_type in [TransactionType.ADVANCE.name, TransactionType.MARKET_DEBT.name]
        self.payment_group.setVisible(is_debt_type)
        
        if not is_debt_type:
            # إخفاء معلومات الدفع للمكافآت والخصومات
            self.paid_amount_input.setText("0")
            set_combobox_value(self.payment_status_combo, PaymentStatus.PAID.name)
            
    def on_full_payment_toggled(self, checked):
        """معالج تغيير خانة الدفع الكامل"""
        if checked:
            amount = clean_numeric_input(self.amount_input.text())
            if amount:
                self.paid_amount_input.setText(str(amount))
                set_combobox_value(self.payment_status_combo, PaymentStatus.PAID.name)
                self.payment_date_input.setEnabled(True)
        else:
            self.paid_amount_input.setText("0")
            set_combobox_value(self.payment_status_combo, PaymentStatus.PENDING.name)
            self.payment_date_input.setEnabled(False)
            
    def update_payment_date_status(self):
        """تحديث حالة تاريخ الدفع"""
        paid_amount = clean_numeric_input(self.paid_amount_input.text())
        self.payment_date_input.setEnabled(paid_amount is not None and paid_amount > 0)

    def on_amount_changed(self):
        """معالج تغيير المبلغ الأصلي"""
        if self.full_payment_checkbox.isChecked():
            amount = clean_numeric_input(self.amount_input.text())
            if amount:
                self.paid_amount_input.setText(str(amount))

    def reset_form(self):
        """إعادة تعيين النموذج"""
        # إعادة تعيين الحقول الأساسية
        self.employee_combo.setCurrentIndex(0)
        self.transaction_type_combo.setCurrentIndex(0)
        self.amount_input.clear()
        self.transaction_date_input.setDate(QDate.currentDate())
        self.description_input.clear()

        # إعادة تعيين حقول الدفع
        self.payment_status_combo.setCurrentIndex(0)
        self.paid_amount_input.setText("0")
        self.payment_date_input.setDate(QDate.currentDate())
        self.payment_date_input.setEnabled(False)
        self.full_payment_checkbox.setChecked(False)

        # تحديث إظهار معلومات الدفع
        self.update_payment_visibility()
        
    def load_transaction_data(self):
        """تحميل بيانات المعاملة للتعديل"""
        try:
            with get_db_session_context() as session:
                self.transaction = session.query(FinancialTransaction).filter_by(id=self.transaction_id).first()
                
                if not self.transaction:
                    show_message(self, "خطأ", "المعاملة غير موجودة", "error")
                    self.reject()
                    return
                
                # ملء الحقول
                set_combobox_value(self.employee_combo, self.transaction.employee_id)
                set_combobox_value(self.transaction_type_combo, self.transaction.transaction_type.name)
                self.amount_input.setText(str(self.transaction.amount))
                self.transaction_date_input.setDate(QDate.fromString(str(self.transaction.transaction_date), "yyyy-MM-dd"))
                
                if self.transaction.description:
                    self.description_input.setPlainText(self.transaction.description)
                
                # معلومات الدفع
                set_combobox_value(self.payment_status_combo, self.transaction.payment_status.name)
                self.paid_amount_input.setText(str(self.transaction.paid_amount))
                
                if self.transaction.payment_date:
                    self.payment_date_input.setDate(QDate.fromString(str(self.transaction.payment_date), "yyyy-MM-dd"))
                
                # تحديث إظهار معلومات الدفع
                self.update_payment_visibility()
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل بيانات المعاملة: {e}", "error")
            self.reject()
            
    def validate_form(self) -> tuple[bool, str]:
        """التحقق من صحة النموذج"""
        # التحقق من الحقول المطلوبة
        if not get_combobox_value(self.employee_combo):
            return False, "يجب اختيار الموظف"
        
        if not get_combobox_value(self.transaction_type_combo):
            return False, "يجب اختيار نوع المعاملة"
        
        if not self.amount_input.text().strip():
            return False, "المبلغ مطلوب"
        
        # التحقق من صحة البيانات
        amount = clean_numeric_input(self.amount_input.text())
        if amount is None or amount <= 0:
            return False, "المبلغ يجب أن يكون رقماً موجباً"
        
        paid_amount = clean_numeric_input(self.paid_amount_input.text()) or 0
        if paid_amount < 0:
            return False, "المبلغ المدفوع لا يمكن أن يكون سالباً"
        
        if paid_amount > amount:
            return False, "المبلغ المدفوع لا يمكن أن يكون أكبر من المبلغ الأصلي"
        
        return True, ""
        
    def save_transaction(self):
        """حفظ بيانات المعاملة"""
        # التحقق من صحة النموذج
        is_valid, error_message = self.validate_form()
        if not is_valid:
            show_message(self, "خطأ في البيانات", error_message, "error")
            return
        
        try:
            with get_db_session_context() as session:
                # إنشاء أو تحديث المعاملة
                if self.is_edit_mode:
                    transaction = session.query(FinancialTransaction).filter_by(id=self.transaction_id).first()
                else:
                    transaction = FinancialTransaction()
                    session.add(transaction)
                
                # تعيين البيانات
                transaction.employee_id = get_combobox_value(self.employee_combo)
                
                transaction_type_name = get_combobox_value(self.transaction_type_combo)
                transaction.transaction_type = TransactionType[transaction_type_name]
                
                transaction.amount = clean_numeric_input(self.amount_input.text())
                transaction.transaction_date = self.transaction_date_input.date().toPython()
                transaction.description = self.description_input.toPlainText().strip() or None
                
                # معلومات الدفع
                payment_status_name = get_combobox_value(self.payment_status_combo)
                transaction.payment_status = PaymentStatus[payment_status_name]
                
                transaction.paid_amount = clean_numeric_input(self.paid_amount_input.text()) or 0
                
                if transaction.paid_amount > 0:
                    transaction.payment_date = self.payment_date_input.date().toPython()
                else:
                    transaction.payment_date = None
                
                session.commit()
                
                # إرسال إشارة النجاح
                self.transaction_saved.emit(transaction.id)
                
                success_message = "تم تحديث بيانات المعاملة بنجاح" if self.is_edit_mode else "تم إضافة المعاملة بنجاح"
                show_message(self, "نجح", success_message, "information")
                
                self.accept()
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في حفظ بيانات المعاملة: {e}", "error")
