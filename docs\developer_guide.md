# دليل المطور - نظام إدارة شؤون الموظفين

## مقدمة

هذا الدليل مخصص للمطورين الذين يرغبون في فهم، تطوير، أو المساهمة في نظام إدارة شؤون الموظفين. يغطي الدليل البنية التقنية، أنماط التصميم، وأفضل الممارسات المستخدمة في المشروع.

## البنية التقنية

### التقنيات المستخدمة

- **Python 3.8+**: لغة البرمجة الأساسية
- **PySide6**: واجهة المستخدم الرسومية
- **SQLAlchemy**: ORM لقاعدة البيانات
- **PostgreSQL**: قاعدة البيانات الرئيسية
- **ReportLab**: توليد تقارير PDF
- **OpenPyXL**: التعامل مع ملفات Excel
- **pytest**: إطار الاختبارات

### هيكل المشروع

```
hr-management-system/
├── src/                    # الكود المصدري
│   ├── config/            # ملفات التكوين
│   ├── database/          # إدارة قاعدة البيانات
│   ├── models/            # نماذج البيانات
│   ├── views/             # واجهات المستخدم
│   ├── services/          # الخدمات والمنطق التجاري
│   ├── utils/             # الأدوات المساعدة
│   └── styles/            # ملفات الأنماط
├── tests/                 # الاختبارات
├── docs/                  # الوثائق
├── requirements.txt       # متطلبات Python
└── run.py                # نقطة دخول التطبيق
```

## أنماط التصميم

### Model-View-Controller (MVC)

- **Models**: في مجلد `src/models/` - تمثل بنية البيانات
- **Views**: في مجلد `src/views/` - واجهات المستخدم
- **Controllers**: منطق التحكم موزع في الخدمات والواجهات

### Repository Pattern

```python
# مثال على نمط Repository
class EmployeeRepository:
    def __init__(self, session):
        self.session = session
    
    def create(self, employee_data):
        employee = Employee(**employee_data)
        self.session.add(employee)
        self.session.commit()
        return employee
    
    def get_by_id(self, employee_id):
        return self.session.query(Employee).filter_by(id=employee_id).first()
```

### Observer Pattern

```python
# استخدام إشارات Qt للتواصل بين المكونات
class EmployeeForm(QDialog):
    employee_saved = Signal(int)  # إشارة حفظ الموظف
    
    def save_employee(self):
        # حفظ البيانات
        self.employee_saved.emit(employee.id)
```

## نماذج البيانات

### النموذج الأساسي

```python
class BaseModel:
    """النموذج الأساسي لجميع الجداول"""
    id = Column(Integer, primary_key=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
```

### العلاقات

```python
class Employee(BaseModel):
    # علاقة مع القسم
    department_id = Column(Integer, ForeignKey('departments.id'))
    department = relationship("Department", back_populates="employees")
    
    # علاقة مع العنوان الوظيفي
    job_title_id = Column(Integer, ForeignKey('job_titles.id'))
    job_title = relationship("JobTitle", back_populates="employees")
```

## إدارة قاعدة البيانات

### الاتصال

```python
# src/database/connection.py
def get_db_session_context():
    """إنشاء جلسة قاعدة بيانات مع إدارة السياق"""
    session = SessionLocal()
    try:
        yield session
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()
```

### الترحيلات

```python
# إضافة ترحيل جديد
def create_migration(name):
    """إنشاء ملف ترحيل جديد"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{timestamp}_{name}.py"
    # إنشاء الملف...
```

## واجهات المستخدم

### البنية الأساسية

```python
class BaseView(QWidget):
    """الواجهة الأساسية لجميع الواجهات"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_connections()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        pass
    
    def setup_connections(self):
        """ربط الإشارات"""
        pass
    
    def load_data(self):
        """تحميل البيانات"""
        pass
```

### إدارة الأنماط

```python
# استخدام مدير الأنماط
from src.utils.theme_manager import theme_manager

# تطبيق ثيم
theme_manager.set_theme("dark")

# الاستماع لتغيير الثيم
theme_manager.theme_changed.connect(self.on_theme_changed)
```

## الخدمات

### خدمة التقارير

```python
class ReportService:
    """خدمة توليد التقارير"""
    
    def generate_employees_report(self, parameters):
        """توليد تقرير الموظفين"""
        # جمع البيانات
        # توليد PDF
        # إرجاع البيانات
        pass
```

### خدمة النسخ الاحتياطي

```python
class BackupService:
    """خدمة النسخ الاحتياطي"""
    
    def create_backup(self, backup_name=None):
        """إنشاء نسخة احتياطية"""
        # نسخ قاعدة البيانات
        # نسخ الملفات المهمة
        # ضغط النسخة
        pass
```

## الاختبارات

### إعداد الاختبارات

```python
# tests/conftest.py
@pytest.fixture
def db_session():
    """جلسة قاعدة بيانات للاختبار"""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    Session = sessionmaker(bind=engine)
    session = Session()
    yield session
    session.close()
```

### اختبار النماذج

```python
def test_create_employee(db_session):
    """اختبار إنشاء موظف"""
    employee = Employee(
        employee_number="EMP001",
        full_name="محمد أحمد",
        # ... باقي البيانات
    )
    db_session.add(employee)
    db_session.commit()
    
    assert employee.id is not None
    assert employee.full_name == "محمد أحمد"
```

### اختبار الواجهات

```python
def test_employee_form(qtbot):
    """اختبار نموذج الموظف"""
    form = EmployeeForm()
    qtbot.addWidget(form)
    
    # ملء النموذج
    form.full_name_input.setText("اختبار")
    
    # محاكاة النقر
    qtbot.mouseClick(form.save_btn, Qt.LeftButton)
    
    # التحقق من النتيجة
    assert form.validate_form()
```

## أفضل الممارسات

### كتابة الكود

1. **اتبع PEP 8**: معايير كتابة Python
2. **استخدم Type Hints**: لتحسين وضوح الكود
3. **اكتب Docstrings**: لتوثيق الدوال والكلاسات
4. **استخدم أسماء واضحة**: للمتغيرات والدوال

```python
def calculate_net_salary(
    basic_salary: Decimal,
    allowances: Decimal,
    deductions: Decimal
) -> Decimal:
    """
    حساب صافي الراتب
    
    Args:
        basic_salary: الراتب الأساسي
        allowances: البدلات
        deductions: الخصومات
    
    Returns:
        صافي الراتب بعد الخصومات
    """
    return basic_salary + allowances - deductions
```

### إدارة الأخطاء

```python
try:
    # العملية المطلوبة
    result = perform_operation()
except SpecificException as e:
    # معالجة خطأ محدد
    logger.error(f"خطأ محدد: {e}")
    show_error_message(str(e))
except Exception as e:
    # معالجة الأخطاء العامة
    logger.exception("خطأ غير متوقع")
    show_error_message("حدث خطأ غير متوقع")
```

### الأمان

1. **تشفير كلمات المرور**: استخدم bcrypt أو مشابه
2. **تنظيف المدخلات**: تحقق من صحة البيانات
3. **سجل التدقيق**: سجل جميع العمليات المهمة
4. **النسخ الاحتياطي**: نسخ دورية للبيانات

## إضافة ميزات جديدة

### إضافة نموذج جديد

1. **إنشاء النموذج**:
```python
# src/models/new_model.py
class NewModel(BaseModel):
    __tablename__ = 'new_models'
    
    name = Column(String(100), nullable=False)
    description = Column(Text)
```

2. **إضافة الترحيل**:
```python
# migrations/create_new_model.py
def upgrade():
    op.create_table('new_models', ...)

def downgrade():
    op.drop_table('new_models')
```

3. **إنشاء الواجهة**:
```python
# src/views/new_model_view.py
class NewModelView(BaseView):
    def setup_ui(self):
        # إعداد الواجهة
        pass
```

### إضافة خدمة جديدة

```python
# src/services/new_service.py
class NewService:
    """خدمة جديدة"""
    
    def __init__(self):
        pass
    
    def perform_operation(self):
        """تنفيذ عملية"""
        pass
```

## التحسين والأداء

### قاعدة البيانات

1. **الفهارس**: أضف فهارس للأعمدة المستخدمة في البحث
2. **الاستعلامات**: استخدم eager loading للعلاقات
3. **التخزين المؤقت**: خزن النتائج المتكررة

### الواجهة

1. **التحميل التدريجي**: حمّل البيانات عند الحاجة
2. **الخيوط**: استخدم QThread للعمليات الطويلة
3. **التخزين المؤقت**: خزن البيانات المرجعية

## النشر والتوزيع

### إنشاء ملف تنفيذي

```bash
# استخدام PyInstaller
pip install pyinstaller
pyinstaller --onefile --windowed run.py
```

### Docker

```dockerfile
FROM python:3.8-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY src/ ./src/
COPY run.py .

CMD ["python", "run.py"]
```

## المساهمة في المشروع

### إعداد بيئة التطوير

1. **استنساخ المشروع**:
```bash
git clone https://github.com/your-repo/hr-system.git
cd hr-system
```

2. **إنشاء بيئة افتراضية**:
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
venv\Scripts\activate     # Windows
```

3. **تثبيت المتطلبات**:
```bash
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

### سير العمل

1. **إنشاء فرع جديد**:
```bash
git checkout -b feature/new-feature
```

2. **كتابة الكود والاختبارات**
3. **تشغيل الاختبارات**:
```bash
pytest tests/
```

4. **إرسال Pull Request**

### معايير المراجعة

- الكود يتبع معايير PEP 8
- جميع الاختبارات تمر بنجاح
- الكود موثق بشكل مناسب
- لا توجد أخطاء أمنية واضحة

## الموارد الإضافية

### الوثائق

- [PySide6 Documentation](https://doc.qt.io/qtforpython/)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
- [pytest Documentation](https://docs.pytest.org/)

### الأدوات المفيدة

- **Black**: تنسيق الكود تلقائياً
- **flake8**: فحص جودة الكود
- **mypy**: فحص الأنواع
- **pre-commit**: خطافات Git

---

**هذا الدليل يوفر نظرة شاملة على البنية التقنية للمشروع. للمزيد من التفاصيل، راجع الكود المصدري والتعليقات المضمنة.**
