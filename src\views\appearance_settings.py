#!/usr/bin/env python3
"""
نافذة إعدادات المظهر المتقدمة
Advanced Appearance Settings Window
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QPushButton, QLabel, QFrame, QGroupBox, QComboBox, QSlider,
    QColorDialog, QTabWidget, QWidget, QScrollArea, QSpacerItem,
    QSizePolicy, QCheckBox, QSpinBox, QTextEdit, QTableWidget,
    QTableWidgetItem, QHeaderView, QLineEdit
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QColor, QPalette

from ..utils.appearance_manager import get_appearance_manager
from ..utils import show_message


class ColorPreviewWidget(QWidget):
    """عنصر معاينة الألوان"""
    
    def __init__(self, color: str, name: str):
        super().__init__()
        self.color = color
        self.name = name
        self.setFixedSize(80, 60)
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {color};
                border: 2px solid #bdc3c7;
                border-radius: 6px;
            }}
        """)
        
        # إضافة تسمية
        layout = QVBoxLayout(self)
        label = QLabel(name)
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("""
            QLabel {
                background-color: rgba(255, 255, 255, 200);
                color: #2c3e50;
                border-radius: 3px;
                padding: 2px;
                font-size: 10px;
                font-weight: bold;
            }
        """)
        layout.addWidget(label)
        layout.setContentsMargins(5, 5, 5, 5)
    
    def update_color(self, color: str):
        """تحديث اللون"""
        self.color = color
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {color};
                border: 2px solid #bdc3c7;
                border-radius: 6px;
            }}
        """)


class LivePreviewWidget(QWidget):
    """عنصر المعاينة المباشرة"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المعاينة"""
        layout = QVBoxLayout(self)
        
        # عنوان المعاينة
        title = QLabel("معاينة مباشرة")
        title.setObjectName("app_title")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # مجموعة الأزرار
        buttons_group = QGroupBox("الأزرار")
        buttons_layout = QHBoxLayout(buttons_group)
        
        primary_btn = QPushButton("زر أساسي")
        secondary_btn = QPushButton("زر ثانوي")
        secondary_btn.setObjectName("sidebar_button")
        disabled_btn = QPushButton("زر معطل")
        disabled_btn.setEnabled(False)
        
        buttons_layout.addWidget(primary_btn)
        buttons_layout.addWidget(secondary_btn)
        buttons_layout.addWidget(disabled_btn)
        layout.addWidget(buttons_group)
        
        # مجموعة الحقول
        inputs_group = QGroupBox("حقول الإدخال")
        inputs_layout = QFormLayout(inputs_group)
        
        line_edit = QLineEdit("نص تجريبي")
        combo_box = QComboBox()
        combo_box.addItems(["خيار 1", "خيار 2", "خيار 3"])
        
        inputs_layout.addRow("حقل نص:", line_edit)
        inputs_layout.addRow("قائمة منسدلة:", combo_box)
        layout.addWidget(inputs_group)
        
        # جدول تجريبي
        table_group = QGroupBox("الجداول")
        table_layout = QVBoxLayout(table_group)
        
        table = QTableWidget(3, 3)
        table.setHorizontalHeaderLabels(["العمود 1", "العمود 2", "العمود 3"])
        
        # ملء الجدول
        for row in range(3):
            for col in range(3):
                item = QTableWidgetItem(f"خلية {row+1}-{col+1}")
                table.setItem(row, col, item)
        
        table.setMaximumHeight(120)
        table_layout.addWidget(table)
        layout.addWidget(table_group)
        
        # مربعات اختيار
        checkbox_group = QGroupBox("مربعات الاختيار")
        checkbox_layout = QVBoxLayout(checkbox_group)
        
        checkbox1 = QCheckBox("خيار أول")
        checkbox2 = QCheckBox("خيار ثاني")
        checkbox1.setChecked(True)
        
        checkbox_layout.addWidget(checkbox1)
        checkbox_layout.addWidget(checkbox2)
        layout.addWidget(checkbox_group)


class AppearanceSettingsDialog(QDialog):
    """نافذة إعدادات المظهر المتقدمة"""
    
    # إشارات
    settings_changed = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.appearance_manager = get_appearance_manager()
        self.color_previews = {}
        self.setup_ui()
        self.load_current_settings()
        self.connect_signals()
        
        # مؤقت للمعاينة المباشرة
        self.preview_timer = QTimer()
        self.preview_timer.setSingleShot(True)
        self.preview_timer.timeout.connect(self.apply_preview)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إعدادات المظهر")
        self.setModal(True)
        self.resize(1000, 700)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(self)
        
        # الجانب الأيسر - الإعدادات
        settings_widget = self.create_settings_widget()
        main_layout.addWidget(settings_widget, 2)
        
        # الجانب الأيمن - المعاينة
        preview_widget = self.create_preview_widget()
        main_layout.addWidget(preview_widget, 1)
    
    def create_settings_widget(self) -> QWidget:
        """إنشاء عنصر الإعدادات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويب الخط
        font_tab = self.create_font_tab()
        tabs.addTab(font_tab, "الخط")
        
        # تبويب المظهر
        theme_tab = self.create_theme_tab()
        tabs.addTab(theme_tab, "المظهر")
        
        # تبويب الألوان
        colors_tab = self.create_colors_tab()
        tabs.addTab(colors_tab, "الألوان")
        
        layout.addWidget(tabs)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.apply_btn = QPushButton("تطبيق")
        self.apply_btn.setObjectName("success_button")
        
        self.reset_btn = QPushButton("إعادة تعيين")
        self.reset_btn.setObjectName("danger_button")
        
        self.save_btn = QPushButton("حفظ")
        self.save_btn.setObjectName("primary_button")
        
        self.cancel_btn = QPushButton("إلغاء")
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.apply_btn)
        buttons_layout.addWidget(self.reset_btn)
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        return widget
    
    def create_font_tab(self) -> QWidget:
        """إنشاء تبويب الخط"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # مجموعة نوع الخط
        font_family_group = QGroupBox("نوع الخط")
        font_family_layout = QFormLayout(font_family_group)
        
        self.font_family_combo = QComboBox()
        self.font_family_combo.addItems(self.appearance_manager.available_fonts)
        font_family_layout.addRow("نوع الخط:", self.font_family_combo)
        
        layout.addWidget(font_family_group)
        
        # مجموعة حجم الخط
        font_size_group = QGroupBox("حجم الخط")
        font_size_layout = QFormLayout(font_size_group)
        
        # منزلق حجم الخط
        font_size_container = QHBoxLayout()
        
        self.font_size_slider = QSlider(Qt.Horizontal)
        self.font_size_slider.setRange(8, 72)
        self.font_size_slider.setValue(12)
        
        self.font_size_spinbox = QSpinBox()
        self.font_size_spinbox.setRange(8, 72)
        self.font_size_spinbox.setValue(12)
        self.font_size_spinbox.setSuffix(" px")
        
        font_size_container.addWidget(self.font_size_slider)
        font_size_container.addWidget(self.font_size_spinbox)
        
        font_size_layout.addRow("حجم الخط:", font_size_container)
        
        # أزرار الأحجام السريعة
        quick_sizes_layout = QHBoxLayout()
        
        sizes = [("صغير", 10), ("عادي", 12), ("متوسط", 16), ("كبير", 20), ("كبير جداً", 24)]
        for name, size in sizes:
            btn = QPushButton(f"{name} ({size})")
            btn.clicked.connect(lambda checked, s=size: self.set_quick_font_size(s))
            quick_sizes_layout.addWidget(btn)
        
        font_size_layout.addRow("أحجام سريعة:", quick_sizes_layout)
        
        layout.addWidget(font_size_group)
        
        layout.addStretch()
        return widget
    
    def create_theme_tab(self) -> QWidget:
        """إنشاء تبويب المظهر"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # مجموعة وضع المظهر
        theme_mode_group = QGroupBox("وضع المظهر")
        theme_mode_layout = QFormLayout(theme_mode_group)
        
        self.theme_mode_combo = QComboBox()
        self.theme_mode_combo.addItems([
            ("فاتح", "light"),
            ("داكن", "dark"),
            ("تلقائي", "auto")
        ])
        
        theme_mode_layout.addRow("الوضع:", self.theme_mode_combo)
        layout.addWidget(theme_mode_group)
        
        # مجموعة مخطط الألوان
        color_scheme_group = QGroupBox("مخطط الألوان")
        color_scheme_layout = QFormLayout(color_scheme_group)
        
        self.color_scheme_combo = QComboBox()
        schemes = [
            ("أزرق", "blue"),
            ("أخضر", "green"), 
            ("بنفسجي", "purple"),
            ("برتقالي", "orange"),
            ("أحمر", "red")
        ]
        
        for name, value in schemes:
            self.color_scheme_combo.addItem(name, value)
        
        color_scheme_layout.addRow("المخطط:", self.color_scheme_combo)
        layout.addWidget(color_scheme_group)
        
        layout.addStretch()
        return widget
    
    def create_colors_tab(self) -> QWidget:
        """إنشاء تبويب الألوان"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # معاينة الألوان الحالية
        preview_group = QGroupBox("معاينة الألوان")
        preview_layout = QGridLayout(preview_group)
        
        colors = self.appearance_manager.get_current_colors()
        color_names = [
            ("أساسي", "primary"),
            ("ثانوي", "secondary"),
            ("تمييز", "accent"),
            ("خلفية", "background"),
            ("سطح", "surface"),
            ("نص أساسي", "text_primary"),
            ("نص ثانوي", "text_secondary"),
            ("حدود", "border")
        ]
        
        row, col = 0, 0
        for display_name, key in color_names:
            color_preview = ColorPreviewWidget(colors.get(key, "#ffffff"), display_name)
            self.color_previews[key] = color_preview
            
            preview_layout.addWidget(color_preview, row, col)
            
            col += 1
            if col >= 4:
                col = 0
                row += 1
        
        layout.addWidget(preview_group)
        
        # أزرار تخصيص الألوان
        custom_group = QGroupBox("تخصيص الألوان")
        custom_layout = QVBoxLayout(custom_group)
        
        customize_btn = QPushButton("فتح محرر الألوان المتقدم")
        customize_btn.clicked.connect(self.open_color_editor)
        custom_layout.addWidget(customize_btn)
        
        layout.addWidget(custom_group)
        
        layout.addStretch()
        return widget
    
    def create_preview_widget(self) -> QWidget:
        """إنشاء عنصر المعاينة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # عنوان المعاينة
        title = QLabel("المعاينة المباشرة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-weight: bold; font-size: 16px; margin: 10px;")
        layout.addWidget(title)
        
        # منطقة التمرير للمعاينة
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # عنصر المعاينة المباشرة
        self.preview_widget = LivePreviewWidget()
        scroll_area.setWidget(self.preview_widget)
        
        layout.addWidget(scroll_area)
        
        return widget

    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        # تحميل نوع الخط
        current_font = self.appearance_manager.get_setting('font_family')
        index = self.font_family_combo.findText(current_font)
        if index >= 0:
            self.font_family_combo.setCurrentIndex(index)

        # تحميل حجم الخط
        current_size = self.appearance_manager.get_setting('font_size')
        self.font_size_slider.setValue(current_size)
        self.font_size_spinbox.setValue(current_size)

        # تحميل وضع المظهر
        current_theme = self.appearance_manager.get_setting('theme_mode')
        for i in range(self.theme_mode_combo.count()):
            if self.theme_mode_combo.itemData(i) == current_theme:
                self.theme_mode_combo.setCurrentIndex(i)
                break

        # تحميل مخطط الألوان
        current_scheme = self.appearance_manager.get_setting('color_scheme')
        for i in range(self.color_scheme_combo.count()):
            if self.color_scheme_combo.itemData(i) == current_scheme:
                self.color_scheme_combo.setCurrentIndex(i)
                break

        # تحديث معاينة الألوان
        self.update_color_previews()

    def connect_signals(self):
        """ربط الإشارات"""
        # إشارات الخط
        self.font_family_combo.currentTextChanged.connect(self.on_font_changed)
        self.font_size_slider.valueChanged.connect(self.on_font_size_changed)
        self.font_size_spinbox.valueChanged.connect(self.on_font_size_spinbox_changed)

        # إشارات المظهر
        self.theme_mode_combo.currentTextChanged.connect(self.on_theme_changed)
        self.color_scheme_combo.currentTextChanged.connect(self.on_color_scheme_changed)

        # إشارات الأزرار
        self.apply_btn.clicked.connect(self.apply_settings)
        self.reset_btn.clicked.connect(self.reset_settings)
        self.save_btn.clicked.connect(self.save_settings)
        self.cancel_btn.clicked.connect(self.reject)

    def set_quick_font_size(self, size: int):
        """تعيين حجم خط سريع"""
        self.font_size_slider.setValue(size)
        self.font_size_spinbox.setValue(size)

    def on_font_changed(self):
        """معالج تغيير نوع الخط"""
        self.start_preview_timer()

    def on_font_size_changed(self, value: int):
        """معالج تغيير حجم الخط من المنزلق"""
        self.font_size_spinbox.blockSignals(True)
        self.font_size_spinbox.setValue(value)
        self.font_size_spinbox.blockSignals(False)
        self.start_preview_timer()

    def on_font_size_spinbox_changed(self, value: int):
        """معالج تغيير حجم الخط من المربع الرقمي"""
        self.font_size_slider.blockSignals(True)
        self.font_size_slider.setValue(value)
        self.font_size_slider.blockSignals(False)
        self.start_preview_timer()

    def on_theme_changed(self):
        """معالج تغيير وضع المظهر"""
        self.update_color_previews()
        self.start_preview_timer()

    def on_color_scheme_changed(self):
        """معالج تغيير مخطط الألوان"""
        self.update_color_previews()
        self.start_preview_timer()

    def start_preview_timer(self):
        """بدء مؤقت المعاينة"""
        self.preview_timer.stop()
        self.preview_timer.start(300)  # 300ms تأخير

    def apply_preview(self):
        """تطبيق المعاينة"""
        # تطبيق مؤقت للمعاينة
        font_family = self.font_family_combo.currentText()
        font_size = self.font_size_slider.value()
        theme_mode = self.theme_mode_combo.currentData()
        color_scheme = self.color_scheme_combo.currentData()

        # تطبيق الإعدادات مؤقتاً
        self.appearance_manager.set_font_family(font_family)
        self.appearance_manager.set_font_size(font_size)
        self.appearance_manager.set_theme_mode(theme_mode)
        self.appearance_manager.set_color_scheme(color_scheme)

    def update_color_previews(self):
        """تحديث معاينة الألوان"""
        # محاكاة الإعدادات الجديدة
        old_theme = self.appearance_manager.current_settings['theme_mode']
        old_scheme = self.appearance_manager.current_settings['color_scheme']

        # تطبيق مؤقت
        new_theme = self.theme_mode_combo.currentData() or old_theme
        new_scheme = self.color_scheme_combo.currentData() or old_scheme

        self.appearance_manager.current_settings['theme_mode'] = new_theme
        self.appearance_manager.current_settings['color_scheme'] = new_scheme

        # الحصول على الألوان الجديدة
        colors = self.appearance_manager.get_current_colors()

        # تحديث المعاينات
        for key, preview_widget in self.color_previews.items():
            if key in colors:
                preview_widget.update_color(colors[key])

        # إعادة الإعدادات القديمة
        self.appearance_manager.current_settings['theme_mode'] = old_theme
        self.appearance_manager.current_settings['color_scheme'] = old_scheme

    def open_color_editor(self):
        """فتح محرر الألوان المتقدم"""
        show_message(self, "قريباً", "محرر الألوان المتقدم سيكون متاحاً قريباً", "information")

    def apply_settings(self):
        """تطبيق الإعدادات"""
        font_family = self.font_family_combo.currentText()
        font_size = self.font_size_slider.value()
        theme_mode = self.theme_mode_combo.currentData()
        color_scheme = self.color_scheme_combo.currentData()

        # تطبيق الإعدادات
        self.appearance_manager.set_font_family(font_family)
        self.appearance_manager.set_font_size(font_size)
        self.appearance_manager.set_theme_mode(theme_mode)
        self.appearance_manager.set_color_scheme(color_scheme)

        # إرسال إشارة التغيير
        self.settings_changed.emit()

        show_message(self, "تم التطبيق", "تم تطبيق إعدادات المظهر بنجاح", "information")

    def save_settings(self):
        """حفظ الإعدادات"""
        self.apply_settings()

        # حفظ في الملف
        if self.appearance_manager.save_settings():
            show_message(self, "تم الحفظ", "تم حفظ إعدادات المظهر بنجاح", "information")
            self.accept()
        else:
            show_message(self, "خطأ", "فشل في حفظ إعدادات المظهر", "error")

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        from ..utils import show_confirmation

        if show_confirmation(self, "إعادة تعيين", "هل تريد إعادة تعيين جميع إعدادات المظهر للافتراضية؟"):
            self.appearance_manager.reset_to_defaults()
            self.load_current_settings()
            show_message(self, "تم إعادة التعيين", "تم إعادة تعيين إعدادات المظهر للافتراضية", "information")

    def closeEvent(self, event):
        """معالج إغلاق النافذة"""
        # إعادة تحميل الإعدادات الأصلية عند الإغلاق بدون حفظ
        self.appearance_manager.load_settings()
        super().closeEvent(event)
