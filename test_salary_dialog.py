#!/usr/bin/env python3
"""
اختبار نافذة تعديل تفاصيل الراتب
Test Salary Details Dialog
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication
from src.database import db_manager, get_db_session_context
from src.models import SalaryRecord
from src.views.salary_details_dialog import SalaryDetailsDialog


def test_salary_dialog():
    """اختبار نافذة تعديل الراتب"""
    print("🧪 اختبار نافذة تعديل تفاصيل الراتب...")
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        # البحث عن سجل راتب
        with get_db_session_context() as session:
            salary_record = session.query(SalaryRecord).first()
            
            if not salary_record:
                print("❌ لا يوجد سجلات رواتب في النظام")
                print("قم بتشغيل: python test_salary_calculation.py أولاً")
                return False
            
            print(f"✅ تم العثور على سجل راتب للموظف: {salary_record.employee.full_name}")
            print(f"   معرف السجل: {salary_record.id}")
            print(f"   أيام الدوام الحالية: {salary_record.working_days}")
            print(f"   صافي الراتب الحالي: {salary_record.net_salary:,.0f} د.ع")
            
            # إنشاء التطبيق
            app = QApplication(sys.argv)
            
            # فتح نافذة التعديل
            print("\n🔧 فتح نافذة تعديل تفاصيل الراتب...")
            dialog = SalaryDetailsDialog(salary_record.id)
            
            # عرض النافذة
            dialog.show()
            
            print("✅ تم فتح النافذة بنجاح!")
            print("\nيمكنك الآن:")
            print("1. تغيير عدد أيام الدوام")
            print("2. تعديل المعاملات المالية")
            print("3. مشاهدة إعادة حساب الراتب تلقائياً")
            print("4. حفظ التعديلات")
            print("\nاضغط Ctrl+C لإغلاق النافذة")
            
            # تشغيل التطبيق
            return app.exec()
            
    except KeyboardInterrupt:
        print("\n👋 تم إغلاق النافذة")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في اختبار النافذة: {e}")
        import traceback
        traceback.print_exc()
        return 1


def main():
    """الدالة الرئيسية"""
    result = test_salary_dialog()
    
    if result == 0:
        print("✅ اختبار النافذة نجح!")
    else:
        print("💥 فشل اختبار النافذة!")
    
    return result


if __name__ == "__main__":
    sys.exit(main())
