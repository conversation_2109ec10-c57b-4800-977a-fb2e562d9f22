"""
الشريط الجانبي للتنقل
Navigation Sidebar
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
    QFrame, QScrollArea, QButtonGroup
)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QFont, QIcon

from ..utils import apply_rtl_layout, set_font_size, make_bold


class SidebarButton(QPushButton):
    """زر الشريط الجانبي"""
    
    def __init__(self, text: str, page_id: str, icon_name: str = None):
        super().__init__(text)
        self.page_id = page_id
        self.icon_name = icon_name
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم المحسن والمميز"""
        self.setObjectName("sidebar_button")
        self.setCheckable(True)

        # تكبير حجم الأزرار بشكل كبير
        self.setMinimumHeight(80)  # زيادة كبيرة لجعلها مميزة
        self.setMaximumHeight(80)
        self.setMinimumWidth(200)

        # تطبيق التخطيط من اليمين إلى اليسار
        apply_rtl_layout(self)

        # تكبير النص إلى 24 كما طلب
        set_font_size(self, 24)

        # أيقونات محسنة وأكبر
        icon_map = {
            "dashboard": "📊",
            "users": "👥",
            "building": "🏢",
            "briefcase": "💼",
            "money": "💰",
            "calculator": "💵",
            "chart": "📋",
            "backup": "💾",
            "audit": "📝",
            "settings": "⚙️"
        }

        if self.icon_name and self.icon_name in icon_map:
            icon_text = icon_map[self.icon_name]
            # إضافة الأيقونة إلى النص مع تنسيق أفضل
            self.setText(f"{icon_text}\n{self.text()}")

        # تحسين المظهر بشكل كامل
        self.setStyleSheet(f"""
            QPushButton {{
                text-align: center;
                padding: 15px 10px;
                border-radius: 15px;
                font-weight: bold;
                font-size: 24px;
                margin: 5px;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #f8f9fa, stop: 1 #e9ecef);
                border: 2px solid #dee2e6;
                color: #495057;
                min-height: 80px;
                max-height: 80px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #007bff, stop: 1 #0056b3);
                color: white;
                border: 2px solid #0056b3;
            }}
            QPushButton:checked {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #28a745, stop: 1 #1e7e34);
                color: white;
                border: 2px solid #1e7e34;
                font-weight: 900;
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #dc3545, stop: 1 #c82333);
                color: white;
            }}
        """)


class Sidebar(QWidget):
    """الشريط الجانبي للتنقل"""
    
    # إشارات
    page_requested = Signal(str)  # إشارة طلب صفحة
    
    def __init__(self):
        super().__init__()
        self.button_group = QButtonGroup()
        self.buttons = {}
        
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setObjectName("sidebar")

        # تطبيق التخطيط من اليمين إلى اليسار
        apply_rtl_layout(self)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # إضافة عنوان القائمة الرئيسية الثابت في الأعلى
        self.create_fixed_header(main_layout)

        # إنشاء منطقة التمرير للأزرار فقط
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # إنشاء محتوى الشريط الجانبي (الأزرار فقط)
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(10, 10, 10, 20)
        content_layout.setSpacing(5)

        # إضافة الأزرار فقط (بدون العنوان)
        self.create_navigation_buttons(content_layout)

        # إضافة مساحة مرنة في النهاية
        content_layout.addStretch()

        # إعداد منطقة التمرير
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)

    def create_fixed_header(self, layout: QVBoxLayout):
        """إنشاء العنوان الثابت في الأعلى"""
        # إضافة عنوان القسم الثابت - مكبر جداً ومميز
        section_label = QLabel("📋 القائمة الرئيسية")
        section_label.setObjectName("sidebar_section_title_fixed")
        section_label.setAlignment(Qt.AlignCenter)
        make_bold(section_label)
        set_font_size(section_label, 24)
        section_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 15px 10px;
                border-radius: 10px;
                border: 3px solid #3498db;
                margin: 8px;
                font-weight: bold;
                font-size: 24px;
            }
        """)
        layout.addWidget(section_label)

        # إضافة خط فاصل ثابت
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setObjectName("sidebar_separator_fixed")
        separator.setStyleSheet("""
            QFrame {
                color: #bdc3c7;
                margin: 5px 10px;
            }
        """)
        layout.addWidget(separator)
        
    def create_navigation_buttons(self, layout: QVBoxLayout):
        """إنشاء أزرار التنقل"""
        # قائمة الصفحات
        pages = [
            ("dashboard", "لوحة التحكم", "dashboard"),
            ("employees", "إدارة الموظفين", "users"),
            ("departments", "الأقسام", "building"),
            ("job_titles", "العناوين الوظيفية", "briefcase"),
            ("financial", "المعاملات المالية", "money"),
            ("salaries", "الرواتب", "calculator"),
            ("reports", "التقارير", "chart"),
            ("backup", "النسخ الاحتياطي", "backup"),
            ("audit", "تدقيق الأنشطة", "audit"),
            ("settings", "الإعدادات", "settings"),
        ]
        
        # العنوان الآن في الأعلى كعنصر ثابت
        
        # إضافة الأزرار
        for page_id, text, icon in pages:
            button = SidebarButton(text, page_id, icon)
            self.buttons[page_id] = button
            self.button_group.addButton(button)
            layout.addWidget(button)
            
        # تحديد الزر الافتراضي
        if "dashboard" in self.buttons:
            self.buttons["dashboard"].setChecked(True)
            
    def setup_connections(self):
        """إعداد الاتصالات"""
        # ربط إشارات الأزرار
        for page_id, button in self.buttons.items():
            button.clicked.connect(lambda checked, pid=page_id: self.on_button_clicked(pid))
            
    def on_button_clicked(self, page_id: str):
        """معالج النقر على الزر"""
        # إرسال إشارة طلب الصفحة
        self.page_requested.emit(page_id)
        
    def set_active_page(self, page_id: str):
        """تعيين الصفحة النشطة"""
        if page_id in self.buttons:
            self.buttons[page_id].setChecked(True)
            
    def get_active_page(self) -> str:
        """الحصول على الصفحة النشطة"""
        for page_id, button in self.buttons.items():
            if button.isChecked():
                return page_id
        return ""
