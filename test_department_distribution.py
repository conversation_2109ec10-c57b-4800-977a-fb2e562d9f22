#!/usr/bin/env python3
"""
اختبار رسم توزيع الموظفين على الأقسام المحسن
Test Enhanced Department Distribution Chart
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout, QPushButton
from PySide6.QtCore import Qt


def create_department_test_window():
    """إنشاء نافذة اختبار توزيع الأقسام"""
    
    class DepartmentTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار رسم توزيع الموظفين على الأقسام المحسن")
            self.setGeometry(100, 100, 800, 600)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # عنوان الاختبار
            from PySide6.QtWidgets import QLabel
            test_title = QLabel("🧪 اختبار رسم توزيع الموظفين (بدون عنوان مكرر)")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 20px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 10px;
                    padding: 15px;
                    background-color: #ecf0f1;
                    border-radius: 10px;
                }
            """)
            main_layout.addWidget(test_title)
            
            # إنشاء رسم توزيع الأقسام
            from src.widgets.enhanced_dashboard import DepartmentDistributionChart
            self.dept_chart = DepartmentDistributionChart()
            main_layout.addWidget(self.dept_chart)
            
            # أزرار التحكم
            controls_layout = QHBoxLayout()
            
            # زر البيانات التجريبية 1
            sample1_btn = QPushButton("📊 بيانات تجريبية 1")
            sample1_btn.setStyleSheet("""
                QPushButton {
                    font-size: 14px;
                    font-weight: bold;
                    padding: 10px 20px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #3498db, stop: 1 #2980b9);
                    color: white;
                    border: none;
                    border-radius: 6px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #5dade2, stop: 1 #3498db);
                }
            """)
            sample1_btn.clicked.connect(self.load_sample_data_1)
            
            # زر البيانات التجريبية 2
            sample2_btn = QPushButton("📈 بيانات تجريبية 2")
            sample2_btn.setStyleSheet("""
                QPushButton {
                    font-size: 14px;
                    font-weight: bold;
                    padding: 10px 20px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #27ae60, stop: 1 #229954);
                    color: white;
                    border: none;
                    border-radius: 6px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #58d68d, stop: 1 #27ae60);
                }
            """)
            sample2_btn.clicked.connect(self.load_sample_data_2)
            
            # زر البيانات الفارغة
            empty_btn = QPushButton("🗑️ مسح البيانات")
            empty_btn.setStyleSheet("""
                QPushButton {
                    font-size: 14px;
                    font-weight: bold;
                    padding: 10px 20px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #e74c3c, stop: 1 #c0392b);
                    color: white;
                    border: none;
                    border-radius: 6px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #ec7063, stop: 1 #e74c3c);
                }
            """)
            empty_btn.clicked.connect(self.clear_data)
            
            # زر البيانات من قاعدة البيانات
            db_btn = QPushButton("💾 بيانات قاعدة البيانات")
            db_btn.setStyleSheet("""
                QPushButton {
                    font-size: 14px;
                    font-weight: bold;
                    padding: 10px 20px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #9b59b6, stop: 1 #8e44ad);
                    color: white;
                    border: none;
                    border-radius: 6px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #bb8fce, stop: 1 #9b59b6);
                }
            """)
            db_btn.clicked.connect(self.load_database_data)
            
            controls_layout.addWidget(sample1_btn)
            controls_layout.addWidget(sample2_btn)
            controls_layout.addWidget(empty_btn)
            controls_layout.addWidget(db_btn)
            controls_layout.addStretch()
            
            main_layout.addLayout(controls_layout)
            
            # تحميل البيانات التجريبية الأولى
            self.load_sample_data_1()
            
        def load_sample_data_1(self):
            """تحميل البيانات التجريبية الأولى"""
            sample_data = [
                ("الإدارة العامة", 12),
                ("المحاسبة", 8),
                ("الموارد البشرية", 5),
                ("التسويق", 9),
                ("المبيعات", 15),
                ("تقنية المعلومات", 7),
                ("خدمة العملاء", 6)
            ]
            self.dept_chart.update_data(sample_data)
            print("📊 تم تحميل البيانات التجريبية الأولى")
            
        def load_sample_data_2(self):
            """تحميل البيانات التجريبية الثانية"""
            sample_data = [
                ("الإنتاج", 25),
                ("الجودة", 4),
                ("الصيانة", 8),
                ("المشتريات", 6),
                ("المخازن", 10),
                ("النقل", 3)
            ]
            self.dept_chart.update_data(sample_data)
            print("📈 تم تحميل البيانات التجريبية الثانية")
            
        def clear_data(self):
            """مسح البيانات"""
            self.dept_chart.update_data([])
            print("🗑️ تم مسح البيانات")
            
        def load_database_data(self):
            """تحميل البيانات من قاعدة البيانات"""
            try:
                from src.database import get_db_session_context
                from src.models import Department, Employee
                
                with get_db_session_context() as session:
                    departments_data = []
                    departments = session.query(Department).filter_by(is_active=True).all()
                    
                    for dept in departments:
                        emp_count = session.query(Employee).filter(
                            Employee.department_id == dept.id,
                            Employee.is_active == True
                        ).count()
                        departments_data.append((dept.name, emp_count))
                    
                    if departments_data:
                        self.dept_chart.update_data(departments_data)
                        print(f"💾 تم تحميل بيانات قاعدة البيانات: {departments_data}")
                    else:
                        print("⚠️ لا توجد بيانات في قاعدة البيانات")
                        self.load_sample_data_1()  # تحميل بيانات تجريبية
                        
            except Exception as e:
                print(f"❌ خطأ في تحميل بيانات قاعدة البيانات: {e}")
                self.load_sample_data_1()  # تحميل بيانات تجريبية كبديل
    
    return DepartmentTestWindow()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار رسم توزيع الموظفين على الأقسام المحسن")
    print("=" * 70)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            enhanced_style_file = styles_dir / "enhanced_dashboard.qss"
            
            combined_styles = ""
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read() + "\n"
                    
            if enhanced_style_file.exists():
                with open(enhanced_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read()
                    
            app.setStyleSheet(combined_styles)
            print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_department_test_window()
        window.show()
        
        print("✅ تم فتح نافذة اختبار توزيع الأقسام!")
        print("\n🎯 الميزات المحسنة:")
        print("• رسم بياني واضح ومقروء")
        print("• ألوان متدرجة ومتناسقة")
        print("• عرض القيم والنسب المئوية")
        print("• مقياس جانبي للقراءة")
        print("• تقصير أسماء الأقسام الطويلة")
        print("• رسالة واضحة عند عدم وجود بيانات")
        
        print("\n🎮 أزرار التحكم:")
        print("• بيانات تجريبية 1: أقسام إدارية")
        print("• بيانات تجريبية 2: أقسام إنتاجية")
        print("• مسح البيانات: عرض رسالة 'لا توجد بيانات'")
        print("• بيانات قاعدة البيانات: البيانات الحقيقية")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
