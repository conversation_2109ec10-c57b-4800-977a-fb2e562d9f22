#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تطبيق الوضع الليلي المحسن على النظام الرئيسي
Apply Enhanced Dark Mode to Main System
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))


def apply_dark_mode_to_running_system():
    """تطبيق الوضع الليلي على النظام الجاري"""
    print("🌙 تطبيق الوضع الليلي المحسن على النظام الجاري...")
    
    try:
        # البحث عن التطبيق الجاري
        app = QApplication.instance()
        
        if not app:
            print("❌ لا يوجد تطبيق Qt جاري")
            return False
        
        # تطبيق الوضع الليلي
        from src.utils.theme_manager import ThemeManager
        theme_manager = ThemeManager()
        
        success = theme_manager.set_theme("dark")
        
        if success:
            print("✅ تم تطبيق الوضع الليلي بنجاح على النظام الجاري")
            return True
        else:
            print("❌ فشل في تطبيق الوضع الليلي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تطبيق الوضع الليلي: {e}")
        return False


def create_theme_controller():
    """إنشاء نافذة تحكم في الثيم"""
    
    class ThemeController(QWidget):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("🌙 تحكم في الوضع الليلي")
            self.setGeometry(100, 100, 400, 300)
            self.setWindowFlags(Qt.WindowStaysOnTopHint)
            
            # إنشاء مدير الثيمات
            from src.utils.theme_manager import ThemeManager
            self.theme_manager = ThemeManager()
            
            self.setup_ui()
            
        def setup_ui(self):
            """إعداد واجهة التحكم"""
            layout = QVBoxLayout(self)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)
            
            # العنوان
            title = QLabel("🌙 تحكم في الوضع الليلي")
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet("""
                font-size: 18px; 
                font-weight: bold; 
                color: #2c3e50; 
                margin: 10px;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
            """)
            layout.addWidget(title)
            
            # معلومات الثيم الحالي
            self.theme_info = QLabel()
            self.theme_info.setAlignment(Qt.AlignCenter)
            self.theme_info.setStyleSheet("""
                font-size: 14px; 
                color: #34495e; 
                padding: 8px;
                background-color: #f8f9fa;
                border-radius: 6px;
                border: 1px solid #dee2e6;
            """)
            self.update_theme_info()
            layout.addWidget(self.theme_info)
            
            # أزرار التحكم
            buttons_layout = QVBoxLayout()
            
            # زر الوضع الليلي
            dark_btn = QPushButton("🌙 تطبيق الوضع الليلي")
            dark_btn.setStyleSheet("""
                QPushButton {
                    font-size: 14px; font-weight: bold; color: white; 
                    background-color: #2c3e50; border: none; border-radius: 8px; 
                    padding: 12px 20px; margin: 5px;
                }
                QPushButton:hover { background-color: #34495e; }
                QPushButton:pressed { background-color: #1a252f; }
            """)
            dark_btn.clicked.connect(lambda: self.set_theme("dark"))
            buttons_layout.addWidget(dark_btn)
            
            # زر الوضع النهاري
            light_btn = QPushButton("☀️ تطبيق الوضع النهاري")
            light_btn.setStyleSheet("""
                QPushButton {
                    font-size: 14px; font-weight: bold; color: #2c3e50; 
                    background-color: #f39c12; border: none; border-radius: 8px; 
                    padding: 12px 20px; margin: 5px;
                }
                QPushButton:hover { background-color: #e67e22; }
                QPushButton:pressed { background-color: #d35400; }
            """)
            light_btn.clicked.connect(lambda: self.set_theme("light"))
            buttons_layout.addWidget(light_btn)
            
            # زر التبديل السريع
            toggle_btn = QPushButton("🔄 تبديل سريع")
            toggle_btn.setStyleSheet("""
                QPushButton {
                    font-size: 14px; font-weight: bold; color: white; 
                    background-color: #27ae60; border: none; border-radius: 8px; 
                    padding: 12px 20px; margin: 5px;
                }
                QPushButton:hover { background-color: #2ecc71; }
                QPushButton:pressed { background-color: #1e8449; }
            """)
            toggle_btn.clicked.connect(self.toggle_theme)
            buttons_layout.addWidget(toggle_btn)
            
            # زر إعادة التحميل
            reload_btn = QPushButton("🔄 إعادة تحميل الثيم")
            reload_btn.setStyleSheet("""
                QPushButton {
                    font-size: 14px; font-weight: bold; color: white; 
                    background-color: #e74c3c; border: none; border-radius: 8px; 
                    padding: 12px 20px; margin: 5px;
                }
                QPushButton:hover { background-color: #c0392b; }
                QPushButton:pressed { background-color: #a93226; }
            """)
            reload_btn.clicked.connect(self.force_reload)
            buttons_layout.addWidget(reload_btn)
            
            layout.addLayout(buttons_layout)
            
            # معلومات إضافية
            info_label = QLabel("""
📝 ملاحظات:
• يتم تطبيق الثيم على النظام الرئيسي مباشرة
• التغييرات تظهر فوراً على جميع النوافذ
• يتم حفظ الثيم المختار تلقائياً
• يمكن الوصول لإعدادات الثيم من صفحة الإعدادات
            """)
            info_label.setStyleSheet("""
                font-size: 12px; 
                color: #7f8c8d; 
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 6px;
                border-left: 4px solid #3498db;
            """)
            layout.addWidget(info_label)
            
            # شريط الحالة
            self.status_label = QLabel("جاهز للتحكم في الثيم")
            self.status_label.setAlignment(Qt.AlignCenter)
            self.status_label.setStyleSheet("""
                font-size: 12px; 
                color: #27ae60; 
                padding: 5px;
                background-color: #d5f4e6;
                border-radius: 4px;
            """)
            layout.addWidget(self.status_label)
            
        def set_theme(self, theme_name):
            """تعيين ثيم"""
            try:
                success = self.theme_manager.set_theme(theme_name)
                if success:
                    self.update_theme_info()
                    theme_display = "الوضع الليلي 🌙" if theme_name == "dark" else "الوضع النهاري ☀️"
                    self.status_label.setText(f"✅ تم تطبيق {theme_display}")
                    self.status_label.setStyleSheet("""
                        font-size: 12px; color: #27ae60; padding: 5px;
                        background-color: #d5f4e6; border-radius: 4px;
                    """)
                    print(f"✅ تم تطبيق {theme_display}")
                else:
                    self.status_label.setText(f"❌ فشل في تطبيق الثيم: {theme_name}")
                    self.status_label.setStyleSheet("""
                        font-size: 12px; color: #e74c3c; padding: 5px;
                        background-color: #fadbd8; border-radius: 4px;
                    """)
                    print(f"❌ فشل في تطبيق الثيم: {theme_name}")
            except Exception as e:
                self.status_label.setText(f"❌ خطأ: {e}")
                print(f"❌ خطأ في تطبيق الثيم: {e}")
        
        def toggle_theme(self):
            """تبديل الثيم"""
            try:
                new_theme = self.theme_manager.toggle_theme()
                self.update_theme_info()
                theme_display = "الوضع الليلي 🌙" if new_theme == "dark" else "الوضع النهاري ☀️"
                self.status_label.setText(f"🔄 تم التبديل إلى {theme_display}")
                self.status_label.setStyleSheet("""
                    font-size: 12px; color: #f39c12; padding: 5px;
                    background-color: #fef9e7; border-radius: 4px;
                """)
                print(f"🔄 تم التبديل إلى {theme_display}")
            except Exception as e:
                self.status_label.setText(f"❌ خطأ في التبديل: {e}")
                print(f"❌ خطأ في التبديل: {e}")
        
        def force_reload(self):
            """إعادة تحميل الثيم بقوة"""
            try:
                success = self.theme_manager.force_reload_theme()
                if success:
                    self.update_theme_info()
                    self.status_label.setText("🔄 تم إعادة تحميل الثيم بنجاح")
                    self.status_label.setStyleSheet("""
                        font-size: 12px; color: #8e44ad; padding: 5px;
                        background-color: #f4ecf7; border-radius: 4px;
                    """)
                    print("🔄 تم إعادة تحميل الثيم بنجاح")
                else:
                    self.status_label.setText("❌ فشل في إعادة تحميل الثيم")
                    print("❌ فشل في إعادة تحميل الثيم")
            except Exception as e:
                self.status_label.setText(f"❌ خطأ في إعادة التحميل: {e}")
                print(f"❌ خطأ في إعادة التحميل: {e}")
        
        def update_theme_info(self):
            """تحديث معلومات الثيم"""
            try:
                current_theme = self.theme_manager.get_current_theme()
                if current_theme == "dark":
                    self.theme_info.setText("الثيم الحالي: 🌙 الوضع الليلي النظيف")
                    self.theme_info.setStyleSheet("""
                        font-size: 14px; color: #2c3e50; padding: 8px;
                        background-color: #2c3e50; color: white; border-radius: 6px;
                    """)
                else:
                    self.theme_info.setText("الثيم الحالي: ☀️ الوضع النهاري")
                    self.theme_info.setStyleSheet("""
                        font-size: 14px; color: #34495e; padding: 8px;
                        background-color: #f8f9fa; border-radius: 6px;
                        border: 1px solid #dee2e6;
                    """)
            except Exception as e:
                self.theme_info.setText(f"❌ خطأ في قراءة الثيم: {e}")
    
    return ThemeController()


def main():
    """الدالة الرئيسية"""
    print("🌙 أداة تطبيق الوضع الليلي على النظام الرئيسي")
    print("=" * 60)
    
    try:
        # التحقق من وجود تطبيق Qt جاري
        app = QApplication.instance()
        
        if not app:
            # إنشاء تطبيق جديد إذا لم يكن موجود
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
            print("📱 تم إنشاء تطبيق Qt جديد")
        else:
            print("📱 تم العثور على تطبيق Qt جاري")
        
        # إنشاء نافذة التحكم
        controller = create_theme_controller()
        controller.show()
        
        print("✅ تم فتح نافذة التحكم في الثيم!")
        print("\n🎯 يمكنك الآن:")
        print("1. تطبيق الوضع الليلي على النظام الرئيسي")
        print("2. التبديل بين الأوضاع بسهولة")
        print("3. إعادة تحميل الثيم عند الحاجة")
        print("4. مراقبة حالة الثيم الحالي")
        print("\nاضغط Ctrl+C لإنهاء أداة التحكم")
        
        # تشغيل التطبيق إذا لم يكن جارياً
        if not QApplication.instance():
            return app.exec()
        else:
            # إبقاء النافذة مفتوحة
            return 0
        
    except Exception as e:
        print(f"💥 خطأ في تشغيل أداة التحكم: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
