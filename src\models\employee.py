"""
نموذج الموظفين
Employee model
"""

from datetime import date, datetime
from enum import Enum
from sqlalchemy import Column, String, Date, Numeric, ForeignKey, Integer, Enum as SQLEnum
from sqlalchemy.orm import relationship, object_session
from .base import BaseModel


class EmploymentStatus(Enum):
    """حالة التوظيف"""
    ACTIVE = "نشط"
    TERMINATED = "مفصول"
    SUSPENDED = "موقوف"
    RESIGNED = "مستقيل"


class Employee(BaseModel):
    """نموذج الموظفين"""
    
    __tablename__ = "employees"
    
    # المعلومات الشخصية
    employee_number = Column(String(20), nullable=False, unique=True, comment="الرقم الوظيفي")
    full_name = Column(String(200), nullable=False, comment="الاسم الكامل")
    birth_date = Column(Date, nullable=True, comment="تاريخ الميلاد")
    nationality = Column(String(50), nullable=True, comment="الجنسية")
    phone = Column(String(20), nullable=True, comment="رقم الهاتف")
    address = Column(String(500), nullable=True, comment="العنوان")
    
    # المعلومات الوظيفية
    hire_date = Column(Date, nullable=False, comment="تاريخ المباشرة")
    basic_salary = Column(Numeric(12, 2), nullable=False, comment="الراتب الأساسي")
    employment_status = Column(
        SQLEnum(EmploymentStatus), 
        nullable=False, 
        default=EmploymentStatus.ACTIVE,
        comment="حالة التوظيف"
    )
    
    # المفاتيح الخارجية
    department_id = Column(Integer, ForeignKey("departments.id"), nullable=False)
    job_title_id = Column(Integer, ForeignKey("job_titles.id"), nullable=False)
    
    # العلاقات
    department = relationship("Department", back_populates="employees")
    job_title = relationship("JobTitle", back_populates="employees")
    financial_transactions = relationship("FinancialTransaction", back_populates="employee", lazy="dynamic")
    salary_records = relationship("SalaryRecord", back_populates="employee", lazy="dynamic")
    
    def __repr__(self) -> str:
        return f"<Employee(id={self.id}, number='{self.employee_number}', name='{self.full_name}')>"
    
    def __str__(self) -> str:
        return f"{self.employee_number} - {self.full_name}"
    
    @property
    def age(self) -> int:
        """حساب العمر"""
        if not self.birth_date:
            return 0
        today = date.today()
        return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))
    
    @property
    def years_of_service(self) -> int:
        """حساب سنوات الخدمة"""
        today = date.today()
        return today.year - self.hire_date.year - ((today.month, today.day) < (self.hire_date.month, self.hire_date.day))
    
    @property
    def daily_salary(self) -> float:
        """الراتب اليومي (الراتب الأساسي / 30)"""
        return float(self.basic_salary) / 30
    
    def get_total_advances(self, month: int = None, year: int = None) -> float:
        """إجمالي السلف"""
        from .financial_transaction import FinancialTransaction, TransactionType
        from sqlalchemy import and_, extract

        try:
            # استخدام session من العلاقة
            session = object_session(self)
            if not session:
                return 0.0

            query = session.query(FinancialTransaction).filter(
                and_(
                    FinancialTransaction.employee_id == self.id,
                    FinancialTransaction.transaction_type == TransactionType.ADVANCE,
                    FinancialTransaction.is_active == True
                )
            )

            if month and year:
                query = query.filter(
                    and_(
                        extract('month', FinancialTransaction.transaction_date) == month,
                        extract('year', FinancialTransaction.transaction_date) == year
                    )
                )

            transactions = query.all()
            return sum(float(t.amount) for t in transactions)
        except:
            return 0.0

    def get_total_deductions(self, month: int = None, year: int = None) -> float:
        """إجمالي الخصومات"""
        from .financial_transaction import FinancialTransaction, TransactionType
        from sqlalchemy import and_, extract

        try:
            session = object_session(self)
            if not session:
                return 0.0

            query = session.query(FinancialTransaction).filter(
                and_(
                    FinancialTransaction.employee_id == self.id,
                    FinancialTransaction.transaction_type == TransactionType.DEDUCTION,
                    FinancialTransaction.is_active == True
                )
            )

            if month and year:
                query = query.filter(
                    and_(
                        extract('month', FinancialTransaction.transaction_date) == month,
                        extract('year', FinancialTransaction.transaction_date) == year
                    )
                )

            transactions = query.all()
            return sum(float(t.amount) for t in transactions)
        except:
            return 0.0

    def get_total_bonuses(self, month: int = None, year: int = None) -> float:
        """إجمالي المكافآت"""
        from .financial_transaction import FinancialTransaction, TransactionType
        from sqlalchemy import and_, extract

        try:
            session = object_session(self)
            if not session:
                return 0.0

            query = session.query(FinancialTransaction).filter(
                and_(
                    FinancialTransaction.employee_id == self.id,
                    FinancialTransaction.transaction_type == TransactionType.BONUS,
                    FinancialTransaction.is_active == True
                )
            )

            if month and year:
                query = query.filter(
                    and_(
                        extract('month', FinancialTransaction.transaction_date) == month,
                        extract('year', FinancialTransaction.transaction_date) == year
                    )
                )

            transactions = query.all()
            return sum(float(t.amount) for t in transactions)
        except Exception as e:
            print(f"خطأ في حساب المكافآت: {e}")
            return 0.0

    def get_total_debts(self, month: int = None, year: int = None) -> float:
        """إجمالي الديون (ديون الماركت)"""
        from .financial_transaction import FinancialTransaction, TransactionType
        from sqlalchemy import and_, extract

        try:
            session = object_session(self)
            if not session:
                return 0.0

            query = session.query(FinancialTransaction).filter(
                and_(
                    FinancialTransaction.employee_id == self.id,
                    FinancialTransaction.transaction_type == TransactionType.MARKET_DEBT,
                    FinancialTransaction.is_active == True
                )
            )

            if month and year:
                query = query.filter(
                    and_(
                        extract('month', FinancialTransaction.transaction_date) == month,
                        extract('year', FinancialTransaction.transaction_date) == year
                    )
                )

            transactions = query.all()
            return sum(float(t.amount) for t in transactions)
        except:
            return 0.0
