"""
نافذة تفاصيل الراتب
Salary Details Dialog
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel, QPushButton,
    QSpinBox, QLineEdit, QGroupBox, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from ..utils import apply_rtl_layout, show_message, format_currency, clean_numeric_input
from ..database import get_db_session_context
from ..models import SalaryRecord


class SalaryDetailsDialog(QDialog):
    """نافذة تفاصيل وتعديل الراتب"""
    
    # إشارات
    salary_updated = Signal(int)  # إشارة تحديث الراتب
    
    def __init__(self, salary_record_id: int, parent=None):
        super().__init__(parent)
        self.salary_record_id = salary_record_id
        self.salary_record = None
        
        self.setup_ui()
        self.load_salary_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setObjectName("salary_details_dialog")
        apply_rtl_layout(self)
        
        # إعداد النافذة
        self.setWindowTitle("تفاصيل الراتب")
        self.setModal(True)
        self.resize(500, 600)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # إنشاء رأس النافذة
        self.create_header(main_layout)
        
        # إنشاء مجموعة معلومات الموظف
        self.create_employee_info_group(main_layout)
        
        # إنشاء مجموعة تفاصيل الراتب
        self.create_salary_details_group(main_layout)
        
        # إنشاء مجموعة المعاملات المالية
        self.create_financial_group(main_layout)
        
        # إنشاء مجموعة الملخص
        self.create_summary_group(main_layout)
        
        # إنشاء أزرار الإجراءات
        self.create_action_buttons(main_layout)
        
    def create_header(self, layout: QVBoxLayout):
        """إنشاء رأس النافذة"""
        header_label = QLabel("تفاصيل وتعديل الراتب")
        header_label.setObjectName("dialog_header")
        header_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(header_label)
        
        # إضافة خط فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        layout.addWidget(separator)
        
    def create_employee_info_group(self, layout: QVBoxLayout):
        """إنشاء مجموعة معلومات الموظف"""
        group = QGroupBox("معلومات الموظف")
        group_layout = QFormLayout(group)
        
        # اسم الموظف
        self.employee_name_label = QLabel()
        group_layout.addRow("اسم الموظف:", self.employee_name_label)
        
        # الرقم الوظيفي
        self.employee_number_label = QLabel()
        group_layout.addRow("الرقم الوظيفي:", self.employee_number_label)
        
        # الفترة
        self.period_label = QLabel()
        group_layout.addRow("الفترة:", self.period_label)
        
        layout.addWidget(group)
        
    def create_salary_details_group(self, layout: QVBoxLayout):
        """إنشاء مجموعة تفاصيل الراتب"""
        group = QGroupBox("تفاصيل الراتب")
        group_layout = QFormLayout(group)
        
        # الراتب الأساسي
        self.basic_salary_label = QLabel()
        group_layout.addRow("الراتب الأساسي:", self.basic_salary_label)
        
        # الراتب اليومي
        self.daily_salary_label = QLabel()
        group_layout.addRow("الراتب اليومي:", self.daily_salary_label)
        
        # عدد أيام الدوام (قابل للتعديل)
        self.working_days_spin = QSpinBox()
        self.working_days_spin.setRange(1, 31)
        self.working_days_spin.setValue(30)
        self.working_days_spin.valueChanged.connect(self.recalculate_salary)
        group_layout.addRow("عدد أيام الدوام:", self.working_days_spin)
        
        layout.addWidget(group)
        
    def create_financial_group(self, layout: QVBoxLayout):
        """إنشاء مجموعة المعاملات المالية"""
        group = QGroupBox("المعاملات المالية للشهر الحالي")
        group_layout = QFormLayout(group)

        # المكافآت والحوافز
        self.bonuses_input = QLineEdit()
        self.bonuses_input.setPlaceholderText("0")
        self.bonuses_input.textChanged.connect(self.recalculate_salary)
        group_layout.addRow("المكافآت والحوافز:", self.bonuses_input)

        # السلف (الاستقطاع الشهري)
        self.advances_input = QLineEdit()
        self.advances_input.setPlaceholderText("0")
        self.advances_input.textChanged.connect(self.recalculate_salary)

        # إضافة تسمية توضيحية للسلف
        advances_layout = QVBoxLayout()
        self.original_advances_label = QLabel()
        self.original_advances_label.setStyleSheet("color: #666; font-size: 10px;")
        advances_layout.addWidget(self.advances_input)
        advances_layout.addWidget(self.original_advances_label)
        group_layout.addRow("استقطاع السلف:", advances_layout)

        # الخصومات
        self.deductions_input = QLineEdit()
        self.deductions_input.setPlaceholderText("0")
        self.deductions_input.textChanged.connect(self.recalculate_salary)
        group_layout.addRow("الخصومات:", self.deductions_input)

        # الديون (الاستقطاع الشهري)
        self.debts_input = QLineEdit()
        self.debts_input.setPlaceholderText("0")
        self.debts_input.textChanged.connect(self.recalculate_salary)

        # إضافة تسمية توضيحية للديون
        debts_layout = QVBoxLayout()
        self.original_debts_label = QLabel()
        self.original_debts_label.setStyleSheet("color: #666; font-size: 10px;")
        debts_layout.addWidget(self.debts_input)
        debts_layout.addWidget(self.original_debts_label)
        group_layout.addRow("استقطاع الديون:", debts_layout)

        layout.addWidget(group)
        
    def create_summary_group(self, layout: QVBoxLayout):
        """إنشاء مجموعة الملخص"""
        group = QGroupBox("ملخص الراتب")
        group_layout = QFormLayout(group)
        
        # إجمالي الراتب
        self.gross_salary_label = QLabel()
        self.gross_salary_label.setFont(QFont("Arial", 12, QFont.Bold))
        group_layout.addRow("إجمالي الراتب:", self.gross_salary_label)
        
        # صافي الراتب
        self.net_salary_label = QLabel()
        self.net_salary_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.net_salary_label.setStyleSheet("color: #2E8B57;")
        group_layout.addRow("صافي الراتب:", self.net_salary_label)
        
        layout.addWidget(group)
        
    def create_action_buttons(self, layout: QVBoxLayout):
        """إنشاء أزرار الإجراءات"""
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_btn = QPushButton("حفظ التعديلات")
        self.save_btn.setObjectName("success_button")
        self.save_btn.setMinimumWidth(120)
        self.save_btn.clicked.connect(self.save_changes)
        
        # زر الإلغاء
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setMinimumWidth(100)
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
        
    def load_salary_data(self):
        """تحميل بيانات الراتب"""
        try:
            with get_db_session_context() as session:
                salary_record = session.query(SalaryRecord).filter_by(id=self.salary_record_id).first()

                if not salary_record:
                    show_message(self, "خطأ", "سجل الراتب غير موجود", "error")
                    self.reject()
                    return

                # حفظ البيانات كقيم منفصلة لتجنب مشكلة Session
                self.salary_data = {
                    'id': salary_record.id,
                    'employee_name': salary_record.employee.full_name,
                    'employee_number': salary_record.employee.employee_number,
                    'employee_id': salary_record.employee_id,
                    'month': salary_record.month,
                    'year': salary_record.year,
                    'basic_salary': float(salary_record.basic_salary),
                    'daily_salary': float(salary_record.daily_salary),
                    'working_days': salary_record.working_days,
                    'total_bonuses': float(salary_record.total_bonuses),
                    'total_advances': float(salary_record.total_advances),
                    'total_deductions': float(salary_record.total_deductions),
                    'total_market_debts': float(salary_record.total_market_debts)
                }

                # حساب المبالغ الأصلية
                employee = salary_record.employee
                self.original_advances = employee.get_total_advances(salary_record.month, salary_record.year)
                self.original_debts = employee.get_total_debts(salary_record.month, salary_record.year)

                # ملء معلومات الموظف
                self.employee_name_label.setText(self.salary_data['employee_name'])
                self.employee_number_label.setText(self.salary_data['employee_number'])
                self.period_label.setText(f"{self.salary_data['month']}/{self.salary_data['year']}")

                # ملء تفاصيل الراتب
                self.basic_salary_label.setText(format_currency(self.salary_data['basic_salary']))
                self.daily_salary_label.setText(format_currency(self.salary_data['daily_salary']))
                self.working_days_spin.setValue(self.salary_data['working_days'])

                # ملء المعاملات المالية
                self.bonuses_input.setText(str(int(self.salary_data['total_bonuses'])))
                self.advances_input.setText(str(int(self.salary_data['total_advances'])))
                self.deductions_input.setText(str(int(self.salary_data['total_deductions'])))
                self.debts_input.setText(str(int(self.salary_data['total_market_debts'])))

                # عرض المبالغ الأصلية
                self.original_advances_label.setText(f"إجمالي السلف الأصلي: {self.original_advances:,.0f} د.ع")
                self.original_debts_label.setText(f"إجمالي الديون الأصلية: {self.original_debts:,.0f} د.ع")

                # حساب وعرض الملخص
                self.recalculate_salary()

        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل بيانات الراتب: {e}", "error")
            self.reject()
            
    def recalculate_salary(self):
        """إعادة حساب الراتب"""
        try:
            if not hasattr(self, 'salary_data'):
                print("لا توجد بيانات راتب")
                return

            # الحصول على القيم
            working_days = self.working_days_spin.value()
            daily_salary = self.salary_data['daily_salary']
            bonuses = clean_numeric_input(self.bonuses_input.text()) or 0
            advances = clean_numeric_input(self.advances_input.text()) or 0
            deductions = clean_numeric_input(self.deductions_input.text()) or 0
            debts = clean_numeric_input(self.debts_input.text()) or 0

            print(f"إعادة حساب الراتب:")
            print(f"  أيام الدوام: {working_days}")
            print(f"  الراتب اليومي: {daily_salary}")
            print(f"  المكافآت: {bonuses}")
            print(f"  السلف: {advances}")
            print(f"  الخصومات: {deductions}")
            print(f"  الديون: {debts}")

            # حساب إجمالي الراتب (تأكد من أن جميع القيم float)
            gross_salary = (float(daily_salary) * float(working_days)) + float(bonuses)

            # حساب صافي الراتب
            net_salary = gross_salary - float(advances) - float(deductions) - float(debts)

            print(f"  إجمالي الراتب: {gross_salary}")
            print(f"  صافي الراتب: {net_salary}")

            # عرض النتائج
            self.gross_salary_label.setText(format_currency(gross_salary))
            self.net_salary_label.setText(format_currency(net_salary))

        except Exception as e:
            print(f"خطأ في حساب الراتب: {e}")
            import traceback
            traceback.print_exc()
            
    def save_changes(self):
        """حفظ التعديلات مع إنشاء ديون للباقي"""
        try:
            print("بدء حفظ التعديلات...")

            with get_db_session_context() as session:
                salary_record = session.query(SalaryRecord).filter_by(id=self.salary_record_id).first()

                if not salary_record:
                    show_message(self, "خطأ", "سجل الراتب غير موجود", "error")
                    return

                employee = salary_record.employee

                # الحصول على المبالغ الأصلية المحفوظة
                original_advances = self.original_advances
                original_debts = self.original_debts

                # الحصول على القيم الجديدة (الاستقطاع)
                new_working_days = self.working_days_spin.value()
                new_bonuses = clean_numeric_input(self.bonuses_input.text()) or 0
                deducted_advances = clean_numeric_input(self.advances_input.text()) or 0
                new_deductions = clean_numeric_input(self.deductions_input.text()) or 0
                deducted_debts = clean_numeric_input(self.debts_input.text()) or 0

                print(f"المبالغ الأصلية:")
                print(f"  السلف الأصلية: {original_advances:,.0f} د.ع")
                print(f"  الديون الأصلية: {original_debts:,.0f} د.ع")

                print(f"الاستقطاع الجديد:")
                print(f"  استقطاع السلف: {deducted_advances:,.0f} د.ع")
                print(f"  استقطاع الديون: {deducted_debts:,.0f} د.ع")

                # حساب الباقي
                remaining_advances = max(0, original_advances - deducted_advances)
                remaining_debts = max(0, original_debts - deducted_debts)

                print(f"الباقي كدين:")
                print(f"  باقي السلف: {remaining_advances:,.0f} د.ع")
                print(f"  باقي الديون: {remaining_debts:,.0f} د.ع")

                # تحديث سجل الراتب
                salary_record.working_days = new_working_days
                salary_record.total_bonuses = new_bonuses
                salary_record.total_advances = deducted_advances
                salary_record.total_deductions = new_deductions
                salary_record.total_market_debts = deducted_debts

                # إعادة حساب الراتب
                salary_record.calculate_salary()

                print(f"الراتب الجديد:")
                print(f"  إجمالي الراتب: {salary_record.gross_salary:,.0f} د.ع")
                print(f"  صافي الراتب: {salary_record.net_salary:,.0f} د.ع")

                # إنشاء ديون جديدة للباقي (للشهر القادم)
                if remaining_advances > 0 or remaining_debts > 0:
                    from ..models import FinancialTransaction, TransactionType
                    from datetime import date, timedelta

                    # تاريخ الشهر القادم
                    next_month_date = date.today().replace(day=1)
                    if next_month_date.month == 12:
                        next_month_date = next_month_date.replace(year=next_month_date.year + 1, month=1)
                    else:
                        next_month_date = next_month_date.replace(month=next_month_date.month + 1)

                    # إنشاء دين للسلف الباقية
                    if remaining_advances > 0:
                        advance_debt = FinancialTransaction(
                            employee_id=employee.id,
                            transaction_type=TransactionType.ADVANCE,
                            amount=remaining_advances,
                            description=f"باقي سلف من {salary_record.month}/{salary_record.year}",
                            transaction_date=next_month_date
                        )
                        session.add(advance_debt)
                        print(f"تم إنشاء دين سلف: {remaining_advances:,.0f} د.ع")

                    # إنشاء دين للديون الباقية
                    if remaining_debts > 0:
                        market_debt = FinancialTransaction(
                            employee_id=employee.id,
                            transaction_type=TransactionType.MARKET_DEBT,
                            amount=remaining_debts,
                            description=f"باقي ديون من {salary_record.month}/{salary_record.year}",
                            transaction_date=next_month_date
                        )
                        session.add(market_debt)
                        print(f"تم إنشاء دين ماركت: {remaining_debts:,.0f} د.ع")

                session.commit()
                print("تم حفظ جميع التعديلات في قاعدة البيانات")

                # إرسال إشارة التحديث
                self.salary_updated.emit(self.salary_record_id)

                # رسالة نجاح مفصلة
                success_msg = "تم حفظ التعديلات بنجاح!\n\n"
                if remaining_advances > 0 or remaining_debts > 0:
                    success_msg += "تم إنشاء ديون للمبالغ الباقية:\n"
                    if remaining_advances > 0:
                        success_msg += f"• باقي السلف: {remaining_advances:,.0f} د.ع\n"
                    if remaining_debts > 0:
                        success_msg += f"• باقي الديون: {remaining_debts:,.0f} د.ع\n"
                    success_msg += "\nسيتم احتسابها في الشهر القادم"

                show_message(self, "نجح", success_msg, "information")
                self.accept()

        except Exception as e:
            print(f"خطأ في حفظ التعديلات: {e}")
            import traceback
            traceback.print_exc()
            show_message(self, "خطأ", f"فشل في حفظ التعديلات: {e}", "error")
