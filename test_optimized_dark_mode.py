#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الوضع الليلي المحسن مع النظام الرئيسي
Optimized Dark Mode Integration Test
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))


def test_optimized_theme_manager():
    """اختبار مدير الثيمات المحسن"""
    print("🔧 اختبار مدير الثيمات المحسن...")
    
    try:
        from src.utils.theme_manager import ThemeManager
        
        theme_manager = ThemeManager()
        
        # اختبار الثيمات المتاحة
        themes = theme_manager.get_available_themes()
        print(f"✅ الثيمات المتاحة: {themes}")
        
        # اختبار تطبيق الوضع الليلي المحسن
        result = theme_manager.set_theme("dark")
        print(f"✅ تطبيق الوضع الليلي المحسن: {'نجح' if result else 'فشل'}")
        
        # اختبار التبديل
        new_theme = theme_manager.toggle_theme()
        print(f"✅ تبديل الثيم: {new_theme}")
        
        # اختبار إعادة التحميل بقوة
        reload_result = theme_manager.force_reload_theme()
        print(f"✅ إعادة تحميل الثيم بقوة: {'نجح' if reload_result else 'فشل'}")
        
        # اختبار الوضع الليلي
        is_dark = theme_manager.is_dark_mode()
        print(f"✅ الوضع الحالي ليلي: {is_dark}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير الثيمات: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_main_system_integration():
    """اختبار التكامل مع النظام الرئيسي"""
    print("\n🏗️ اختبار التكامل مع النظام الرئيسي...")
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق الوضع الليلي المحسن
        from src.utils.theme_manager import ThemeManager
        theme_manager = ThemeManager()
        
        print("🌙 تطبيق الوضع الليلي المحسن...")
        success = theme_manager.set_theme("dark")
        
        if not success:
            print("❌ فشل في تطبيق الوضع الليلي")
            return 1
        
        print("✅ تم تطبيق الوضع الليلي المحسن بنجاح")
        
        # إنشاء النافذة الرئيسية
        print("🏗️ إنشاء النافذة الرئيسية...")
        from src.views.main_window import MainWindow
        
        main_window = MainWindow()
        
        # التأكد من تطبيق الثيم على النافذة الرئيسية
        main_window.set_theme("dark")
        
        main_window.show()
        
        print("✅ تم فتح النظام الرئيسي مع الوضع الليلي المحسن!")
        print("\n🎯 يمكنك الآن:")
        print("1. التنقل بين جميع الصفحات")
        print("2. اختبار الوضع الليلي في كل صفحة")
        print("3. الذهاب لصفحة الإعدادات لتجربة:")
        print("   - تبديل الثيم")
        print("   - إعادة تحميل الثيم")
        print("   - حفظ الإعدادات")
        print("4. اختبار البطاقات الإحصائية والجداول")
        print("5. اختبار النماذج والحقول")
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"💥 خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        return 1


def create_quick_test_window():
    """إنشاء نافذة اختبار سريع"""
    
    class QuickTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("🌙 اختبار سريع للوضع الليلي المحسن")
            self.setGeometry(300, 300, 800, 600)
            
            # إنشاء مدير الثيمات
            from src.utils.theme_manager import ThemeManager
            self.theme_manager = ThemeManager()
            
            # تطبيق الوضع الليلي
            self.theme_manager.set_theme("dark")
            
            self.setup_ui()
            
        def setup_ui(self):
            """إعداد الواجهة"""
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            layout = QVBoxLayout(central_widget)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(20)
            
            # العنوان
            title = QLabel("🌙 اختبار الوضع الليلي المحسن")
            title.setObjectName("app_title")
            title.setAlignment(Qt.AlignCenter)
            layout.addWidget(title)
            
            # معلومات الثيم
            self.theme_info = QLabel()
            self.theme_info.setObjectName("window_title")
            self.theme_info.setAlignment(Qt.AlignCenter)
            self.update_theme_info()
            layout.addWidget(self.theme_info)
            
            # أزرار التحكم
            controls_layout = QHBoxLayout()
            
            light_btn = QPushButton("☀️ وضع نهاري")
            light_btn.clicked.connect(lambda: self.set_theme("light"))
            controls_layout.addWidget(light_btn)
            
            dark_btn = QPushButton("🌙 وضع ليلي")
            dark_btn.setObjectName("success_button")
            dark_btn.clicked.connect(lambda: self.set_theme("dark"))
            controls_layout.addWidget(dark_btn)
            
            toggle_btn = QPushButton("🔄 تبديل")
            toggle_btn.setObjectName("info_button")
            toggle_btn.clicked.connect(self.toggle_theme)
            controls_layout.addWidget(toggle_btn)
            
            reload_btn = QPushButton("🔄 إعادة تحميل")
            reload_btn.setObjectName("warning_button")
            reload_btn.clicked.connect(self.force_reload)
            controls_layout.addWidget(reload_btn)
            
            layout.addLayout(controls_layout)
            
            # عناصر الاختبار
            test_group = QGroupBox("عناصر الاختبار")
            test_layout = QVBoxLayout(test_group)
            
            # أزرار مختلفة
            buttons_layout = QHBoxLayout()
            
            normal_btn = QPushButton("زر عادي")
            success_btn = QPushButton("نجاح")
            success_btn.setObjectName("success_button")
            danger_btn = QPushButton("خطر")
            danger_btn.setObjectName("danger_button")
            warning_btn = QPushButton("تحذير")
            warning_btn.setObjectName("warning_button")
            info_btn = QPushButton("معلومات")
            info_btn.setObjectName("info_button")
            
            for btn in [normal_btn, success_btn, danger_btn, warning_btn, info_btn]:
                buttons_layout.addWidget(btn)
            
            test_layout.addLayout(buttons_layout)
            
            # حقول إدخال
            inputs_layout = QFormLayout()
            
            line_edit = QLineEdit("نص تجريبي")
            combo_box = QComboBox()
            combo_box.addItems(["خيار 1", "خيار 2", "خيار 3"])
            date_edit = QDateEdit(QDate.currentDate())
            
            inputs_layout.addRow("حقل نص:", line_edit)
            inputs_layout.addRow("قائمة:", combo_box)
            inputs_layout.addRow("تاريخ:", date_edit)
            
            test_layout.addLayout(inputs_layout)
            
            # خانات اختيار
            checkbox = QCheckBox("خانة اختيار")
            checkbox.setChecked(True)
            radio = QRadioButton("زر راديو")
            radio.setChecked(True)
            
            test_layout.addWidget(checkbox)
            test_layout.addWidget(radio)
            
            layout.addWidget(test_group)
            
            # بطاقات إحصائية تجريبية
            cards_layout = QHBoxLayout()
            
            cards_data = [
                ("الموظفين", "150", "موظف", "employees_card"),
                ("الرواتب", "750,000", "ريال", "salaries_card"),
                ("السلف", "25,000", "ريال", "advances_card")
            ]
            
            for title, value, subtitle, object_name in cards_data:
                card = self.create_stat_card(title, value, subtitle, object_name)
                cards_layout.addWidget(card)
            
            layout.addLayout(cards_layout)
            
            # شريط الحالة
            self.statusBar().showMessage("🌙 الوضع الليلي المحسن نشط")
            
        def create_stat_card(self, title, value, subtitle, object_name):
            """إنشاء بطاقة إحصائية"""
            card = QFrame()
            card.setObjectName(object_name)
            card.setFixedSize(200, 120)
            
            layout = QVBoxLayout(card)
            layout.setAlignment(Qt.AlignCenter)
            
            title_label = QLabel(title)
            title_label.setObjectName("card_title")
            title_label.setAlignment(Qt.AlignCenter)
            
            value_label = QLabel(value)
            value_label.setObjectName("card_value")
            value_label.setAlignment(Qt.AlignCenter)
            
            subtitle_label = QLabel(subtitle)
            subtitle_label.setObjectName("card_subtitle")
            subtitle_label.setAlignment(Qt.AlignCenter)
            
            layout.addWidget(title_label)
            layout.addWidget(value_label)
            layout.addWidget(subtitle_label)
            
            return card
            
        def set_theme(self, theme_name):
            """تعيين ثيم"""
            success = self.theme_manager.set_theme(theme_name)
            if success:
                self.update_theme_info()
                theme_display = "الوضع الليلي" if theme_name == "dark" else "الوضع النهاري"
                self.statusBar().showMessage(f"✅ تم تطبيق {theme_display}")
                print(f"✅ تم تطبيق {theme_display}")
            else:
                self.statusBar().showMessage(f"❌ فشل في تطبيق الثيم: {theme_name}")
                print(f"❌ فشل في تطبيق الثيم: {theme_name}")
        
        def toggle_theme(self):
            """تبديل الثيم"""
            new_theme = self.theme_manager.toggle_theme()
            self.update_theme_info()
            theme_display = "الوضع الليلي" if new_theme == "dark" else "الوضع النهاري"
            self.statusBar().showMessage(f"🔄 تم التبديل إلى {theme_display}")
            print(f"🔄 تم التبديل إلى {theme_display}")
        
        def force_reload(self):
            """إعادة تحميل الثيم بقوة"""
            success = self.theme_manager.force_reload_theme()
            if success:
                self.update_theme_info()
                self.statusBar().showMessage("🔄 تم إعادة تحميل الثيم بنجاح")
                print("🔄 تم إعادة تحميل الثيم بنجاح")
            else:
                self.statusBar().showMessage("❌ فشل في إعادة تحميل الثيم")
                print("❌ فشل في إعادة تحميل الثيم")
        
        def update_theme_info(self):
            """تحديث معلومات الثيم"""
            current_theme = self.theme_manager.get_current_theme()
            if current_theme == "dark":
                self.theme_info.setText("الثيم الحالي: 🌙 الوضع الليلي المحسن")
            else:
                self.theme_info.setText("الثيم الحالي: ☀️ الوضع النهاري")
    
    return QuickTestWindow()


def main():
    """الدالة الرئيسية"""
    print("🌙 اختبار الوضع الليلي المحسن والمتكامل")
    print("=" * 60)
    
    # اختبار مدير الثيمات أولاً
    if not test_optimized_theme_manager():
        print("💥 فشل في اختبار مدير الثيمات")
        return 1
    
    # اختيار نوع الاختبار
    if len(sys.argv) > 1:
        if sys.argv[1] == "--quick":
            # اختبار سريع
            print("\n🔧 تشغيل الاختبار السريع...")
            try:
                app = QApplication(sys.argv)
                app.setLayoutDirection(Qt.RightToLeft)
                
                window = create_quick_test_window()
                window.show()
                
                print("✅ تم فتح نافذة الاختبار السريع")
                return app.exec()
                
            except Exception as e:
                print(f"💥 خطأ في الاختبار السريع: {e}")
                return 1
        elif sys.argv[1] == "--main":
            # اختبار النظام الكامل
            return test_main_system_integration()
    
    # اختبار افتراضي - النظام الكامل
    return test_main_system_integration()


if __name__ == "__main__":
    sys.exit(main())
