"""
ملف التكوين الرئيسي للنظام
Configuration file for the HR Management System
"""

import os
from pathlib import Path
from typing import Dict, Any

# مسارات المشروع
PROJECT_ROOT = Path(__file__).parent.parent
SRC_DIR = PROJECT_ROOT / "src"
ASSETS_DIR = SRC_DIR / "assets"
STYLES_DIR = SRC_DIR / "styles"
REPORTS_DIR = SRC_DIR / "reports"
MIGRATIONS_DIR = PROJECT_ROOT / "migrations"
BACKUP_DIR = PROJECT_ROOT / "backups"

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    "host": os.getenv("DB_HOST", "localhost"),
    "port": os.getenv("DB_PORT", "5432"),
    "database": os.getenv("DB_NAME", "hr_system"),
    "username": os.getenv("DB_USER", "postgres"),
    "password": os.getenv("DB_PASSWORD", "19952010"),
}

# إعدادات التطبيق
APP_CONFIG = {
    "name": "نظام إدارة شؤون الموظفين",
    "version": "1.0.0",
    "window_title": "HR Management System",
    "window_size": (1400, 900),
    "min_window_size": (1200, 700),
    "theme": "light",  # light or dark
    "language": "ar",  # ar or en
    "currency": "د.ع",
    "date_format": "%Y-%m-%d",
    "datetime_format": "%Y-%m-%d %H:%M:%S",
}

# إعدادات الأمان
SECURITY_CONFIG = {
    "password_min_length": 8,
    "session_timeout": 3600,  # seconds
    "max_login_attempts": 3,
    "encryption_key": os.getenv("ENCRYPTION_KEY", "your-secret-key-here"),
}

# إعدادات النسخ الاحتياطي
BACKUP_CONFIG = {
    "auto_backup": True,
    "backup_interval": "daily",  # daily, weekly, monthly
    "backup_time": "02:00",  # HH:MM
    "max_backups": 30,
    "backup_location": str(BACKUP_DIR),
}

# إعدادات التقارير
REPORTS_CONFIG = {
    "default_format": "pdf",
    "page_size": "A4",
    "margin": 20,
    "font_family": "Arial",
    "font_size": 12,
}

# إعدادات الواجهة
UI_CONFIG = {
    "sidebar_width": 250,
    "header_height": 60,
    "animation_duration": 300,
    "notification_duration": 3000,
    "table_row_height": 35,
    "icon_size": 24,
}

def get_database_url() -> str:
    """إنشاء رابط قاعدة البيانات"""
    config = DATABASE_CONFIG
    return f"postgresql://{config['username']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}"

def ensure_directories() -> None:
    """التأكد من وجود جميع المجلدات المطلوبة"""
    directories = [
        ASSETS_DIR,
        STYLES_DIR,
        REPORTS_DIR,
        MIGRATIONS_DIR,
        BACKUP_DIR,
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

def get_config(section: str) -> Dict[str, Any]:
    """الحصول على إعدادات قسم معين"""
    configs = {
        "database": DATABASE_CONFIG,
        "app": APP_CONFIG,
        "security": SECURITY_CONFIG,
        "backup": BACKUP_CONFIG,
        "reports": REPORTS_CONFIG,
        "ui": UI_CONFIG,
    }
    return configs.get(section, {})
