#!/usr/bin/env python3
"""
اختبار الشريط الجانبي المحسن
Test Enhanced Sidebar
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QHBoxLayout, QWidget
from PySide6.QtCore import Qt


def create_sidebar_test_window():
    """إنشاء نافذة اختبار الشريط الجانبي"""
    
    class SidebarTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار الشريط الجانبي المحسن")
            self.setGeometry(100, 100, 1000, 700)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QHBoxLayout(central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            # إنشاء الشريط الجانبي
            from src.views.sidebar import Sidebar
            self.sidebar = Sidebar()
            self.sidebar.setFixedWidth(280)
            
            # منطقة المحتوى
            content_area = QWidget()
            content_area.setStyleSheet("""
                QWidget {
                    background-color: #ffffff;
                    border: 1px solid #dee2e6;
                }
            """)
            
            # إضافة نص في منطقة المحتوى
            from PySide6.QtWidgets import QVBoxLayout, QLabel
            content_layout = QVBoxLayout(content_area)
            
            title = QLabel("🎨 اختبار الأزرار المحسنة")
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet("""
                QLabel {
                    font-size: 32px;
                    font-weight: bold;
                    color: #495057;
                    margin: 50px;
                }
            """)
            
            description = QLabel("""
            ✅ أزرار بحجم خط 24 بكسل
            ✅ نص في الوسط
            ✅ تأثيرات hover متقدمة
            ✅ ألوان متدرجة جميلة
            ✅ حجم أكبر ومميز
            ✅ أيقونات واضحة
            """)
            description.setAlignment(Qt.AlignCenter)
            description.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    color: #6c757d;
                    line-height: 1.6;
                    margin: 20px;
                }
            """)
            
            content_layout.addWidget(title)
            content_layout.addWidget(description)
            content_layout.addStretch()
            
            # إضافة العناصر إلى التخطيط
            main_layout.addWidget(self.sidebar)
            main_layout.addWidget(content_area)
            
            # ربط إشارات الشريط الجانبي
            self.sidebar.page_requested.connect(self.on_page_requested)
        
        def on_page_requested(self, page_id: str):
            """معالج طلب الصفحة"""
            print(f"🔄 تم طلب الصفحة: {page_id}")
            
            # تحديث النافذة لإظهار الصفحة المطلوبة
            self.setWindowTitle(f"اختبار الشريط الجانبي - {page_id}")
    
    return SidebarTestWindow()


def test_sidebar_functionality(app):
    """اختبار وظائف الشريط الجانبي"""
    print("🔍 اختبار وظائف الشريط الجانبي...")

    try:
        from src.views.sidebar import Sidebar, SidebarButton

        # اختبار إنشاء زر
        button = SidebarButton("اختبار", "test", "dashboard")
        print("✅ تم إنشاء زر الشريط الجانبي")

        # اختبار إنشاء الشريط الجانبي
        sidebar = Sidebar()
        print("✅ تم إنشاء الشريط الجانبي")

        # اختبار الأزرار
        buttons = sidebar.buttons
        print(f"✅ تم إنشاء {len(buttons)} زر")

        for page_id, button in buttons.items():
            print(f"   - {page_id}: {button.text()}")

        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار الشريط الجانبي: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الشريط الجانبي المحسن")
    print("=" * 50)

    try:
        # إنشاء التطبيق أولاً
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)

        # اختبار الوظائف بعد إنشاء التطبيق
        if not test_sidebar_functionality(app):
            print("💥 فشل في اختبار وظائف الشريط الجانبي")
            return 1
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    app.setStyleSheet(f.read())
                print("✅ تم تحميل الأنماط المحسنة")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # تهيئة نظام المظهر
        try:
            from src.utils.appearance_manager import get_appearance_manager
            appearance_manager = get_appearance_manager()
            appearance_manager.load_settings()
            print("✅ تم تهيئة نظام المظهر")
        except Exception as e:
            print(f"⚠️ فشل في تهيئة نظام المظهر: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_sidebar_test_window()
        window.show()
        
        print("✅ تم فتح نافذة اختبار الشريط الجانبي!")
        print("\n🎯 الميزات الجديدة:")
        print("• أزرار بحجم خط 24 بكسل")
        print("• نص في الوسط")
        print("• تأثيرات hover متقدمة")
        print("• ألوان متدرجة جميلة")
        print("• حجم أكبر ومميز (80px)")
        print("• عرض شريط جانبي أكبر (280px)")
        print("• أيقونات واضحة مع النص")
        
        print("\n🎨 التأثيرات:")
        print("• hover: لون أزرق مع تكبير")
        print("• checked: لون أخضر مع ظل")
        print("• pressed: لون أحمر مع تصغير")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
