# دليل إعداد قاعدة البيانات
## Database Setup Guide

هذا الدليل يوضح كيفية إعداد قاعدة البيانات لنظام إدارة شؤون الموظفين.

## المتطلبات الأساسية

### 1. PostgreSQL
- إصدار 12 أو أحدث
- تأكد من تشغيل خدمة PostgreSQL
- تأكد من وجود صلاحيات إنشاء قواعد البيانات

### 2. Python Packages
```bash
pip install -r requirements.txt
```

## طرق الإعداد

### الطريقة الأولى: الإعداد السريع (مُوصى به)

```bash
python quick_setup.py
```

هذا الأمر سيقوم بـ:
- ✅ إنشاء قاعدة البيانات إذا لم تكن موجودة
- ✅ إنشاء جميع الجداول المطلوبة
- ✅ إدراج بيانات نموذجية للاختبار

### الطريقة الثانية: الإعداد التفاعلي

```bash
python setup_database.py
```

هذا الأمر يوفر قائمة تفاعلية للخيارات:
1. إعداد قاعدة البيانات (جديدة)
2. إعادة تعيين قاعدة البيانات
3. اختبار الاتصال فقط
4. عرض معلومات التكوين

### الطريقة الثالثة: استخدام أوامر مباشرة

```bash
# إعداد جديد
python setup_database.py setup

# إعادة تعيين (حذف البيانات الموجودة)
python setup_database.py reset

# اختبار الاتصال
python setup_database.py test
```

## إعداد قاعدة البيانات يدوياً

### 1. إنشاء قاعدة البيانات

```sql
-- الاتصال بـ PostgreSQL كمستخدم postgres
psql -U postgres

-- إنشاء قاعدة البيانات
CREATE DATABASE hr_system;

-- إنشاء مستخدم (اختياري)
CREATE USER hr_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE hr_system TO hr_user;

-- الخروج
\q
```

### 2. تحديث إعدادات الاتصال

قم بتحديث الملف `config/config.json` أو استخدم متغيرات البيئة:

```json
{
  "database": {
    "host": "localhost",
    "port": 5432,
    "database": "hr_system",
    "username": "postgres",
    "password": "your_password"
  }
}
```

أو استخدم متغيرات البيئة:
```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=hr_system
export DB_USER=postgres
export DB_PASSWORD=your_password
```

### 3. تشغيل الترحيلات

```python
from src.database.migrations import setup_database
setup_database()
```

## التحقق من الإعداد

### 1. اختبار الاتصال
```bash
python setup_database.py test
```

### 2. التحقق من الجداول
```sql
-- الاتصال بقاعدة البيانات
psql -U postgres -d hr_system

-- عرض الجداول
\dt

-- يجب أن تظهر الجداول التالية:
-- departments
-- job_titles  
-- employees
-- financial_transactions
-- salary_records
-- audit_logs
-- schema_migrations
```

### 3. التحقق من البيانات النموذجية
```sql
-- عدد الأقسام
SELECT COUNT(*) FROM departments;

-- عدد العناوين الوظيفية
SELECT COUNT(*) FROM job_titles;

-- عدد الموظفين
SELECT COUNT(*) FROM employees;
```

## البيانات النموذجية

سيتم إدراج البيانات التالية تلقائياً:

### الأقسام
- قسم تقنية المعلومات
- قسم المحاسبة  
- قسم الموارد البشرية
- قسم المبيعات

### العناوين الوظيفية
- مطور برمجيات
- محاسب
- أخصائي موارد بشرية
- مندوب مبيعات
- مدير قسم

### الموظفين
- 5 موظفين نموذجيين موزعين على الأقسام المختلفة

## استكشاف الأخطاء

### خطأ الاتصال بقاعدة البيانات

```
❌ فشل في الاتصال بقاعدة البيانات
```

**الحلول:**
1. تأكد من تشغيل خدمة PostgreSQL
2. تحقق من إعدادات الاتصال
3. تأكد من صحة اسم المستخدم وكلمة المرور
4. تحقق من إعدادات الجدار الناري

### خطأ في إنشاء قاعدة البيانات

```
❌ فشل في إنشاء قاعدة البيانات
```

**الحلول:**
1. تأكد من وجود صلاحيات إنشاء قواعد البيانات
2. تحقق من عدم وجود قاعدة بيانات بنفس الاسم
3. تأكد من توفر مساحة كافية على القرص

### خطأ في إنشاء الجداول

```
❌ فشل في إنشاء الجداول
```

**الحلول:**
1. تأكد من صحة إعدادات قاعدة البيانات
2. تحقق من وجود صلاحيات إنشاء الجداول
3. تأكد من عدم وجود جداول بنفس الأسماء

### خطأ في المكتبات المطلوبة

```
❌ psycopg2 غير مثبت
```

**الحل:**
```bash
pip install psycopg2-binary
# أو
pip install -r requirements.txt
```

## إعدادات متقدمة

### استخدام Docker

```dockerfile
# docker-compose.yml
version: '3.8'
services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: hr_system
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

```bash
# تشغيل قاعدة البيانات
docker-compose up -d

# إعداد النظام
python quick_setup.py
```

### النسخ الاحتياطي التلقائي

سيتم تفعيل النسخ الاحتياطي التلقائي افتراضياً:
- **التوقيت**: يومياً في الساعة 2:00 صباحاً
- **المكان**: مجلد `backups/`
- **العدد المحفوظ**: 30 نسخة

يمكن تغيير هذه الإعدادات من واجهة النظام أو ملف التكوين.

### الأمان

- تأكد من استخدام كلمة مرور قوية
- قم بتحديث كلمة المرور دورياً
- استخدم اتصال SSL في البيئة الإنتاجية
- قم بعمل نسخ احتياطية دورية

## الدعم

إذا واجهت أي مشاكل:

1. تحقق من ملف السجل: `database_setup.log`
2. راجع هذا الدليل
3. تأكد من المتطلبات الأساسية
4. جرب الإعداد التفاعلي للحصول على معلومات أكثر

---

**ملاحظة**: تأكد من عمل نسخة احتياطية من بياناتك قبل إجراء أي تغييرات على قاعدة البيانات.
