#!/usr/bin/env python3
"""
اختبار النطاق الجديد للخطوط (8-72)
Test New Font Range (8-72)
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_font_manager_range():
    """اختبار نطاق مدير الخطوط"""
    print("🔍 اختبار نطاق مدير الخطوط...")
    
    try:
        from src.utils.font_manager import get_font_manager
        font_manager = get_font_manager()
        
        # اختبار الحدود
        test_sizes = [8, 12, 16, 24, 36, 48, 60, 72]
        invalid_sizes = [7, 73, 100]
        
        print("✅ اختبار الأحجام الصحيحة:")
        for size in test_sizes:
            original_size = font_manager.get_font_size()
            font_manager.set_font_size(size)
            current_size = font_manager.get_font_size()
            
            if current_size == size:
                print(f"   ✅ حجم {size}px: نجح")
            else:
                print(f"   ❌ حجم {size}px: فشل (الحالي: {current_size})")
        
        print("\n⚠️ اختبار الأحجام غير الصحيحة:")
        for size in invalid_sizes:
            original_size = font_manager.get_font_size()
            font_manager.set_font_size(size)
            current_size = font_manager.get_font_size()
            
            if current_size == original_size:
                print(f"   ✅ حجم {size}px: تم رفضه بشكل صحيح")
            else:
                print(f"   ❌ حجم {size}px: تم قبوله خطأً (الحالي: {current_size})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير الخطوط: {e}")
        return False

def test_font_application():
    """اختبار تطبيق الخطوط الكبيرة"""
    print("\n🖥️ اختبار تطبيق الخطوط الكبيرة...")
    
    try:
        from PySide6.QtWidgets import QApplication, QLabel, QWidget, QVBoxLayout
        from PySide6.QtCore import Qt
        from src.utils.font_manager import get_font_manager
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة اختبار
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # إضافة تسميات بأحجام مختلفة
        labels = []
        test_sizes = [12, 24, 36, 48, 60, 72]
        
        font_manager = get_font_manager()
        
        for size in test_sizes:
            label = QLabel(f"اختبار حجم الخط {size}px")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            labels.append(label)
            
            # تطبيق حجم الخط
            font_manager.set_font_size(size)
            
            print(f"   ✅ تم تطبيق حجم {size}px")
        
        print("✅ تم تطبيق جميع الأحجام بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تطبيق الخطوط: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_slider_range():
    """اختبار نطاق منزلق الإعدادات"""
    print("\n⚙️ اختبار نطاق منزلق الإعدادات...")
    
    try:
        from PySide6.QtWidgets import QApplication, QSlider
        from PySide6.QtCore import Qt
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء منزلق مثل الموجود في الإعدادات
        slider = QSlider(Qt.Horizontal)
        slider.setRange(8, 72)
        
        # اختبار النطاق
        min_val = slider.minimum()
        max_val = slider.maximum()
        
        if min_val == 8 and max_val == 72:
            print(f"   ✅ نطاق المنزلق صحيح: {min_val}-{max_val}")
            
            # اختبار القيم
            test_values = [8, 36, 72]
            for val in test_values:
                slider.setValue(val)
                if slider.value() == val:
                    print(f"   ✅ قيمة {val}: صحيحة")
                else:
                    print(f"   ❌ قيمة {val}: خطأ")
            
            return True
        else:
            print(f"   ❌ نطاق المنزلق خطأ: {min_val}-{max_val}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار منزلق الإعدادات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار النطاق الجديد للخطوط (8-72)")
    print("=" * 60)
    
    tests = [
        ("مدير الخطوط", test_font_manager_range),
        ("تطبيق الخطوط", test_font_application),
        ("منزلق الإعدادات", test_settings_slider_range)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ اختبار {test_name}: نجح\n")
            else:
                print(f"❌ اختبار {test_name}: فشل\n")
        except Exception as e:
            print(f"💥 خطأ في اختبار {test_name}: {e}\n")
    
    print("=" * 60)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n✅ النطاق الجديد (8-72) يعمل بشكل مثالي!")
        print("\n🎯 يمكنك الآن:")
        print("• استخدام أحجام خطوط من 8 إلى 72 بكسل")
        print("• الاستفادة من الأحجام الكبيرة للعروض التقديمية")
        print("• تحسين إمكانية الوصول للمستخدمين ضعاف البصر")
        print("• إنشاء واجهات مخصصة بأحجام خطوط متنوعة")
        
        print("\n🚀 لاختبار النظام:")
        print("python test_enhanced_font_system.py")
        print("أو")
        print("python run_hr_system.py")
        
        return 0
    else:
        print(f"⚠️ فشل {total - passed} اختبار")
        print("يرجى مراجعة الأخطاء أعلاه")
        return 1

if __name__ == "__main__":
    sys.exit(main())
