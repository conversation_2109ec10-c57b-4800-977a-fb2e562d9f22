"""
إعداد قاعدة البيانات والبيانات الأولية
Database Setup and Initial Data
"""

import logging
from datetime import date
from ..models import (
    Department, JobTitle, Employee, EmploymentStatus,
    FinancialTransaction, TransactionType, PaymentStatus,
    AuditLog, ActionType
)
from .connection import db_manager, get_db_session_context

logger = logging.getLogger(__name__)


def setup_database():
    """إعداد قاعدة البيانات الكامل"""
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        # إنشاء الجداول
        db_manager.create_tables()
        
        # إدراج البيانات الأولية
        insert_initial_data()
        
        logger.info("تم إعداد قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"خطأ في إعداد قاعدة البيانات: {e}")
        return False


def insert_initial_data():
    """إدراج البيانات الأولية"""
    with get_db_session_context() as session:
        # التحقق من وجود بيانات
        if session.query(Department).count() > 0:
            logger.info("البيانات الأولية موجودة مسبقاً")
            return
        
        # إدراج الأقسام
        departments = [
            Department(name="الإدارة العامة", code="ADM", description="قسم الإدارة العامة"),
            Department(name="الموارد البشرية", code="HR", description="قسم الموارد البشرية"),
            Department(name="المحاسبة", code="ACC", description="قسم المحاسبة"),
            Department(name="تقنية المعلومات", code="IT", description="قسم تقنية المعلومات"),
            Department(name="المبيعات", code="SAL", description="قسم المبيعات"),
            Department(name="التسويق", code="MKT", description="قسم التسويق"),
            Department(name="الإنتاج", code="PRD", description="قسم الإنتاج"),
            Department(name="الجودة", code="QUA", description="قسم ضمان الجودة"),
        ]
        
        for dept in departments:
            session.add(dept)
        
        # إدراج العناوين الوظيفية
        job_titles = [
            JobTitle(title="مدير عام", code="GM", description="المدير العام للشركة", min_salary=3000000, max_salary=5000000),
            JobTitle(title="مدير قسم", code="DM", description="مدير قسم", min_salary=2000000, max_salary=3000000),
            JobTitle(title="مشرف", code="SUP", description="مشرف", min_salary=1500000, max_salary=2500000),
            JobTitle(title="موظف أول", code="SE", description="موظف أول", min_salary=1000000, max_salary=1800000),
            JobTitle(title="موظف", code="EMP", description="موظف", min_salary=700000, max_salary=1200000),
            JobTitle(title="محاسب", code="ACC", description="محاسب", min_salary=800000, max_salary=1500000),
            JobTitle(title="مبرمج", code="DEV", description="مبرمج", min_salary=1000000, max_salary=2000000),
            JobTitle(title="مصمم", code="DES", description="مصمم", min_salary=800000, max_salary=1500000),
            JobTitle(title="مندوب مبيعات", code="SR", description="مندوب مبيعات", min_salary=600000, max_salary=1000000),
            JobTitle(title="سكرتير", code="SEC", description="سكرتير", min_salary=500000, max_salary=800000),
        ]
        
        for job_title in job_titles:
            session.add(job_title)
        
        session.commit()
        
        # تسجيل في سجل التدقيق
        AuditLog.log_action(
            session=session,
            action_type=ActionType.CREATE,
            description="إدراج البيانات الأولية للأقسام والعناوين الوظيفية",
            user_name="النظام"
        )
        
        logger.info("تم إدراج البيانات الأولية بنجاح")


def create_sample_employees():
    """إنشاء موظفين تجريبيين للاختبار"""
    with get_db_session_context() as session:
        # التحقق من وجود موظفين
        if session.query(Employee).count() > 0:
            logger.info("الموظفون التجريبيون موجودون مسبقاً")
            return
        
        # الحصول على الأقسام والعناوين الوظيفية
        admin_dept = session.query(Department).filter_by(code="ADM").first()
        hr_dept = session.query(Department).filter_by(code="HR").first()
        it_dept = session.query(Department).filter_by(code="IT").first()
        
        gm_title = session.query(JobTitle).filter_by(code="GM").first()
        dm_title = session.query(JobTitle).filter_by(code="DM").first()
        dev_title = session.query(JobTitle).filter_by(code="DEV").first()
        emp_title = session.query(JobTitle).filter_by(code="EMP").first()
        
        # إنشاء موظفين تجريبيين
        employees = [
            Employee(
                employee_number="EMP001",
                full_name="أحمد محمد علي",
                birth_date=date(1985, 5, 15),
                nationality="عراقي",
                phone="07901234567",
                address="بغداد - الكرادة",
                hire_date=date(2020, 1, 1),
                basic_salary=4000000,
                employment_status=EmploymentStatus.ACTIVE,
                department=admin_dept,
                job_title=gm_title
            ),
            Employee(
                employee_number="EMP002",
                full_name="فاطمة حسن محمود",
                birth_date=date(1990, 8, 22),
                nationality="عراقية",
                phone="07801234567",
                address="بغداد - الجادرية",
                hire_date=date(2021, 3, 15),
                basic_salary=2500000,
                employment_status=EmploymentStatus.ACTIVE,
                department=hr_dept,
                job_title=dm_title
            ),
            Employee(
                employee_number="EMP003",
                full_name="محمد عبد الله أحمد",
                birth_date=date(1988, 12, 10),
                nationality="عراقي",
                phone="07701234567",
                address="بغداد - المنصور",
                hire_date=date(2019, 6, 1),
                basic_salary=1800000,
                employment_status=EmploymentStatus.ACTIVE,
                department=it_dept,
                job_title=dev_title
            ),
            Employee(
                employee_number="EMP004",
                full_name="زينب علي حسن",
                birth_date=date(1992, 3, 8),
                nationality="عراقية",
                phone="07601234567",
                address="بغداد - الدورة",
                hire_date=date(2022, 1, 10),
                basic_salary=1000000,
                employment_status=EmploymentStatus.ACTIVE,
                department=hr_dept,
                job_title=emp_title
            ),
        ]
        
        for employee in employees:
            session.add(employee)
        
        session.commit()
        
        # تسجيل في سجل التدقيق
        AuditLog.log_action(
            session=session,
            action_type=ActionType.CREATE,
            description="إنشاء موظفين تجريبيين للاختبار",
            user_name="النظام"
        )
        
        logger.info("تم إنشاء الموظفين التجريبيين بنجاح")


if __name__ == "__main__":
    # إعداد نظام التسجيل
    logging.basicConfig(level=logging.INFO)
    
    # إعداد قاعدة البيانات
    if setup_database():
        print("تم إعداد قاعدة البيانات بنجاح!")
        
        # إنشاء موظفين تجريبيين
        create_sample_employees()
        print("تم إنشاء البيانات التجريبية بنجاح!")
    else:
        print("فشل في إعداد قاعدة البيانات!")
