# متغيرات البيئة لنظام إدارة شؤون الموظفين
# Environment Variables for HR Management System

# انسخ هذا الملف إلى .env وعدّل القيم حسب بيئتك
# Copy this file to .env and modify values for your environment

# =============================================================================
# إعدادات قاعدة البيانات
# Database Settings
# =============================================================================

# نوع قاعدة البيانات (postgresql, sqlite)
DB_TYPE=postgresql

# عنوان خادم قاعدة البيانات
DB_HOST=localhost

# منفذ قاعدة البيانات
DB_PORT=5432

# اسم قاعدة البيانات
DB_NAME=hr_system

# اسم مستخدم قاعدة البيانات
DB_USER=postgres

# كلمة مرور قاعدة البيانات
DB_PASSWORD=password

# إعدادات مجموعة الاتصالات
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# إظهار استعلامات SQL (true/false)
DB_ECHO=false

# =============================================================================
# إعدادات التطبيق
# Application Settings
# =============================================================================

# اسم التطبيق
APP_NAME=نظام إدارة شؤون الموظفين

# إصدار التطبيق
APP_VERSION=1.0.0

# وضع التطوير (true/false)
DEBUG=false

# اللغة الافتراضية (ar, en)
DEFAULT_LANGUAGE=ar

# الثيم الافتراضي (light, dark)
DEFAULT_THEME=light

# حجم الخط الافتراضي
DEFAULT_FONT_SIZE=10

# حجم النافذة الافتراضي (العرض x الارتفاع)
WINDOW_WIDTH=1200
WINDOW_HEIGHT=800

# =============================================================================
# إعدادات الأمان
# Security Settings
# =============================================================================

# مفتاح التشفير (يجب تغييره في الإنتاج)
SECRET_KEY=your-secret-key-here

# مدة انتهاء الجلسة (بالثواني)
SESSION_TIMEOUT=3600

# الحد الأدنى لطول كلمة المرور
PASSWORD_MIN_LENGTH=8

# عدد محاولات تسجيل الدخول المسموحة
MAX_LOGIN_ATTEMPTS=5

# مدة القفل بعد المحاولات الفاشلة (بالثواني)
LOCKOUT_DURATION=900

# =============================================================================
# إعدادات النسخ الاحتياطي
# Backup Settings
# =============================================================================

# تفعيل النسخ الاحتياطي التلقائي (true/false)
AUTO_BACKUP=true

# فترة النسخ الاحتياطي (daily, weekly, monthly)
BACKUP_INTERVAL=daily

# وقت النسخ الاحتياطي (HH:MM)
BACKUP_TIME=02:00

# عدد النسخ الاحتياطية المحفوظة
MAX_BACKUPS=30

# مسار حفظ النسخ الاحتياطية
BACKUP_PATH=./backups

# ضغط النسخ الاحتياطية (true/false)
BACKUP_COMPRESS=true

# =============================================================================
# إعدادات التسجيل
# Logging Settings
# =============================================================================

# مستوى التسجيل (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# مسار ملف السجل
LOG_FILE=logs/hr_system.log

# الحد الأقصى لحجم ملف السجل (بالميجابايت)
LOG_MAX_SIZE=10

# عدد ملفات السجل المحفوظة
LOG_BACKUP_COUNT=5

# تنسيق رسائل السجل
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# =============================================================================
# إعدادات التقارير
# Reports Settings
# =============================================================================

# صيغة التقرير الافتراضية (pdf, excel)
DEFAULT_REPORT_FORMAT=pdf

# حجم صفحة التقرير (A4, A3, Letter)
REPORT_PAGE_SIZE=A4

# اتجاه الصفحة (portrait, landscape)
REPORT_ORIENTATION=portrait

# خط التقرير
REPORT_FONT=Arial

# حجم خط التقرير
REPORT_FONT_SIZE=10

# اسم الشركة (يظهر في التقارير)
COMPANY_NAME=اسم الشركة

# عنوان الشركة
COMPANY_ADDRESS=عنوان الشركة

# مسار شعار الشركة
COMPANY_LOGO=assets/logo.png

# =============================================================================
# إعدادات البريد الإلكتروني (اختياري)
# Email Settings (Optional)
# =============================================================================

# خادم SMTP
SMTP_HOST=smtp.gmail.com

# منفذ SMTP
SMTP_PORT=587

# اسم المستخدم
SMTP_USER=<EMAIL>

# كلمة المرور
SMTP_PASSWORD=your-app-password

# استخدام TLS (true/false)
SMTP_USE_TLS=true

# البريد الإلكتروني للمرسل
FROM_EMAIL=<EMAIL>

# =============================================================================
# إعدادات التطوير
# Development Settings
# =============================================================================

# تفعيل وضع التطوير (true/false)
DEVELOPMENT_MODE=false

# إظهار أدوات التطوير (true/false)
SHOW_DEV_TOOLS=false

# إعادة تحميل تلقائية (true/false)
AUTO_RELOAD=false

# تفعيل التصحيح (true/false)
ENABLE_DEBUGGING=false

# =============================================================================
# إعدادات الأداء
# Performance Settings
# =============================================================================

# حجم ذاكرة التخزين المؤقت (بالميجابايت)
CACHE_SIZE=100

# مدة انتهاء صلاحية التخزين المؤقت (بالثواني)
CACHE_TIMEOUT=3600

# عدد العمليات المتوازية
MAX_WORKERS=4

# مهلة انتظار العمليات (بالثواني)
OPERATION_TIMEOUT=30

# =============================================================================
# إعدادات واجهة المستخدم
# UI Settings
# =============================================================================

# تفعيل الرسوم المتحركة (true/false)
ENABLE_ANIMATIONS=true

# إظهار النصائح (true/false)
SHOW_TOOLTIPS=true

# تفعيل الأصوات (true/false)
ENABLE_SOUNDS=false

# شفافية النوافذ (0-100)
WINDOW_OPACITY=100

# =============================================================================
# إعدادات التكامل (اختياري)
# Integration Settings (Optional)
# =============================================================================

# API مفتاح خدمة خارجية
EXTERNAL_API_KEY=your-api-key

# رابط API خارجي
EXTERNAL_API_URL=https://api.example.com

# مهلة انتظار API (بالثواني)
API_TIMEOUT=30

# =============================================================================
# ملاحظات مهمة
# Important Notes
# =============================================================================

# 1. لا تشارك ملف .env مع أي شخص
# 2. استخدم كلمات مرور قوية
# 3. غيّر SECRET_KEY في البيئة الإنتاجية
# 4. قم بعمل نسخة احتياطية من إعداداتك
# 5. راجع الإعدادات دورياً للتأكد من أمانها

# =============================================================================
# أمثلة للبيئات المختلفة
# Examples for Different Environments
# =============================================================================

# للتطوير (Development):
# DEBUG=true
# LOG_LEVEL=DEBUG
# DB_ECHO=true

# للاختبار (Testing):
# DB_NAME=hr_system_test
# LOG_LEVEL=WARNING
# AUTO_BACKUP=false

# للإنتاج (Production):
# DEBUG=false
# LOG_LEVEL=ERROR
# SESSION_TIMEOUT=1800
# AUTO_BACKUP=true
