#!/usr/bin/env python3
"""
اختبار نافذة تفاصيل الموظف المصلحة
Test Fixed Employee Details Dialog
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.database import db_manager, get_db_session_context
from src.models import Employee


def test_employee_details_data():
    """اختبار بيانات تفاصيل الموظف"""
    print("🧪 اختبار بيانات تفاصيل الموظف...")
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        # البحث عن موظف
        with get_db_session_context() as session:
            employee = session.query(Employee).filter_by(is_active=True).first()
            
            if not employee:
                print("❌ لا يوجد موظفين في النظام")
                return False
            
            print(f"✅ الموظف: {employee.full_name}")
            
            # اختبار الحقول الأساسية
            print("\n📋 المعلومات الأساسية:")
            print(f"   الرقم الوظيفي: {employee.employee_number}")
            print(f"   الاسم الكامل: {employee.full_name}")
            
            # اختبار تاريخ الميلاد
            if hasattr(employee, 'birth_date') and employee.birth_date:
                print(f"   تاريخ الميلاد: {employee.birth_date}")
                print(f"   العمر: {employee.age} سنة")
            else:
                print("   تاريخ الميلاد: غير محدد")
                print("   العمر: غير محدد")
            
            # اختبار الجنسية
            if hasattr(employee, 'nationality'):
                print(f"   الجنسية: {employee.nationality or 'غير محدد'}")
            else:
                print("   الجنسية: غير متاح")
            
            # اختبار رقم الهاتف
            if hasattr(employee, 'phone'):
                print(f"   رقم الهاتف: {employee.phone or 'غير محدد'}")
            else:
                print("   رقم الهاتف: غير متاح")
            
            # اختبار العنوان
            if hasattr(employee, 'address'):
                print(f"   العنوان: {employee.address or 'غير محدد'}")
            else:
                print("   العنوان: غير متاح")
            
            print("\n💼 المعلومات الوظيفية:")
            print(f"   تاريخ المباشرة: {employee.hire_date}")
            print(f"   سنوات الخدمة: {employee.years_of_service} سنة")
            print(f"   الراتب الأساسي: {employee.basic_salary:,.0f} د.ع")
            print(f"   الراتب اليومي: {employee.daily_salary:,.0f} د.ع")
            print(f"   حالة التوظيف: {employee.employment_status.value}")
            
            # اختبار العلاقات
            if employee.department:
                print(f"   القسم: {employee.department.name}")
            else:
                print("   القسم: غير محدد")
            
            if employee.job_title:
                print(f"   العنوان الوظيفي: {employee.job_title.title}")
            else:
                print("   العنوان الوظيفي: غير محدد")
            
            print("\n✅ جميع البيانات متاحة وصحيحة!")
            return True
            
    except Exception as e:
        print(f"\n❌ خطأ في اختبار البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_employee_details_dialog():
    """اختبار نافذة تفاصيل الموظف"""
    print("\n🖥️ اختبار نافذة تفاصيل الموظف...")

    try:
        # إنشاء QApplication
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # البحث عن موظف
        with get_db_session_context() as session:
            employee = session.query(Employee).filter_by(is_active=True).first()

            if not employee:
                print("❌ لا يوجد موظفين في النظام")
                return False

            print(f"✅ سيتم فتح تفاصيل الموظف: {employee.full_name}")

            # محاولة إنشاء النافذة
            from src.views.employee_details import EmployeeDetailsDialog

            print("🔧 إنشاء نافذة التفاصيل...")
            dialog = EmployeeDetailsDialog(employee.id)

            print("✅ تم إنشاء النافذة بنجاح!")
            print("📊 تحميل البيانات...")

            # اختبار تحميل البيانات
            if hasattr(dialog, 'employee') and dialog.employee:
                print(f"✅ تم تحميل بيانات الموظف: {dialog.employee.full_name}")
            else:
                print("⚠️ لم يتم تحميل بيانات الموظف")

            return True

    except Exception as e:
        print(f"\n❌ خطأ في اختبار النافذة: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار نافذة تفاصيل الموظف المصلحة")
    print("=" * 60)
    
    # اختبار البيانات أولاً
    if not test_employee_details_data():
        print("💥 فشل اختبار البيانات!")
        return 1
    
    # اختبار النافذة
    if not test_employee_details_dialog():
        print("💥 فشل اختبار النافذة!")
        return 1
    
    print("\n🎉 جميع الاختبارات نجحت!")
    print("\n✅ الإصلاحات المطبقة:")
    print("• تم إصلاح مشكلة national_id (استبدال بـ nationality)")
    print("• تم إزالة حقل email غير الموجود")
    print("• تم إضافة حقل العمر")
    print("• تم التأكد من جميع الحقول الموجودة")
    
    print("\n🚀 يمكنك الآن:")
    print("1. تشغيل النظام: python run_hr_system.py")
    print("2. الذهاب إلى قسم الموظفين")
    print("3. تحديد موظف والنقر على 'عرض التفاصيل'")
    print("4. مشاهدة جميع التفاصيل بدون أخطاء")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
