/* أنماط لوحة التحكم المحسنة */
/* Enhanced Dashboard Styles */

/* الإطار الرئيسي */
QWidget#enhanced_dashboard {
    background-color: #f8f9fa;
    border: none;
}

/* إطار العنوان الرئيسي */
QFrame#main_header_frame {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                              stop: 0 rgba(255, 255, 255, 0.95),
                              stop: 1 rgba(248, 249, 250, 0.95));
    border: 2px solid rgba(52, 152, 219, 0.2);
    border-radius: 15px;
    margin: 10px;
}

/* العنوان الرئيسي */
QLabel#main_dashboard_title {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 15px;
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                              stop: 0 rgba(52, 152, 219, 0.1), 
                              stop: 1 rgba(155, 89, 182, 0.1));
    border-radius: 12px;
    border: 2px solid rgba(52, 152, 219, 0.2);
}

/* العنوان الفرعي */
QLabel#dashboard_subtitle {
    font-size: 16px;
    font-weight: 500;
    color: #7f8c8d;
    letter-spacing: 1px;
    margin-top: 5px;
}

/* عناوين الأقسام */
QLabel#section_title {
    font-size: 20px;
    font-weight: bold;
    color: #34495e;
    margin: 15px 0;
    padding: 12px;
    letter-spacing: 1px;
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                              stop: 0 rgba(52, 73, 94, 0.05),
                              stop: 1 rgba(44, 62, 80, 0.05));
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

/* بطاقات البيانات */
QFrame#data_card {
    border-radius: 12px;
    margin: 5px;
}

QFrame#data_card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* عناوين البطاقات */
QLabel#card_title {
    color: rgba(255, 255, 255, 0.95);
    font-size: 14px;
    font-weight: bold;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* قيم البطاقات */
QLabel#card_value {
    color: white;
    font-size: 28px;
    font-weight: 900;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* عناوين فرعية للبطاقات */
QLabel#card_subtitle {
    color: rgba(255, 255, 255, 0.85);
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0.3px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* إطار الرسوم البيانية */
QFrame#charts_frame {
    background-color: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    margin: 10px;
    padding: 20px;
}

/* أزرار الإجراءات السريعة */
QPushButton {
    font-size: 14px;
    font-weight: bold;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

QPushButton:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

QPushButton:pressed {
    transform: translateY(0px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

/* منطقة التمرير */
QScrollArea {
    border: none;
    background-color: transparent;
}

QScrollBar:vertical {
    background-color: #f1f2f6;
    width: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background-color: #3498db;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #2980b9;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* تأثيرات الرسوم المتحركة */
QFrame#data_card {
    transition: all 0.3s ease;
}

QPushButton {
    transition: all 0.2s ease;
}

/* ألوان مخصصة للبطاقات */
QFrame#employees_card {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                              stop: 0 #3498db, stop: 1 #2980b9);
    border: 2px solid #2471a3;
}

QFrame#salaries_card {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                              stop: 0 #27ae60, stop: 1 #229954);
    border: 2px solid #1e8449;
}

QFrame#advances_card {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                              stop: 0 #f39c12, stop: 1 #e67e22);
    border: 2px solid #d68910;
}

QFrame#debts_card {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                              stop: 0 #e74c3c, stop: 1 #c0392b);
    border: 2px solid #a93226;
}

QFrame#bonuses_card {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                              stop: 0 #9b59b6, stop: 1 #8e44ad);
    border: 2px solid #7d3c98;
}

QFrame#departments_card {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                              stop: 0 #1abc9c, stop: 1 #16a085);
    border: 2px solid #138d75;
}
