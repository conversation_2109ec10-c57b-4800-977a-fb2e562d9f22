#!/usr/bin/env python3
"""
اختبار الرسومات البيانية المصححة والمرتبة
Test Fixed and Reorganized Charts
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QHBoxLayout
from PySide6.QtCore import Qt, QTimer


def create_fixed_charts_test():
    """إنشاء نافذة اختبار الرسومات المصححة"""
    
    class FixedChartsTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار الرسومات البيانية المصححة والمرتبة")
            self.setGeometry(100, 100, 1400, 900)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # عنوان الاختبار
            test_title = QLabel("🔧 اختبار الرسومات البيانية المصححة")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 28px;
                    font-weight: bold;
                    color: white;
                    margin: 20px;
                    padding: 20px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #27ae60, stop: 1 #229954);
                    border-radius: 15px;
                }
            """)
            main_layout.addWidget(test_title)
            
            # إنشاء مكون الرسومات البيانية
            from src.widgets import StatisticsChartWidget
            self.charts_widget = StatisticsChartWidget()
            main_layout.addWidget(self.charts_widget)
            
            # أزرار التحكم
            controls_layout = QHBoxLayout()
            
            # زر تحديث البيانات
            update_btn = QPushButton("🔄 تحديث البيانات")
            update_btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px;
                    font-weight: bold;
                    padding: 12px 24px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #3498db, stop: 1 #2980b9);
                    color: white;
                    border: none;
                    border-radius: 8px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #5dade2, stop: 1 #3498db);
                }
            """)
            update_btn.clicked.connect(self.update_test_data)
            
            # زر بيانات عشوائية
            random_btn = QPushButton("🎲 بيانات عشوائية")
            random_btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px;
                    font-weight: bold;
                    padding: 12px 24px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #e74c3c, stop: 1 #c0392b);
                    color: white;
                    border: none;
                    border-radius: 8px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #ec7063, stop: 1 #e74c3c);
                }
            """)
            random_btn.clicked.connect(self.update_random_data)
            
            # زر إعادة تعيين
            reset_btn = QPushButton("🔄 إعادة تعيين")
            reset_btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px;
                    font-weight: bold;
                    padding: 12px 24px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #95a5a6, stop: 1 #7f8c8d);
                    color: white;
                    border: none;
                    border-radius: 8px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #bdc3c7, stop: 1 #95a5a6);
                }
            """)
            reset_btn.clicked.connect(self.reset_data)
            
            controls_layout.addWidget(update_btn)
            controls_layout.addWidget(random_btn)
            controls_layout.addWidget(reset_btn)
            controls_layout.addStretch()
            
            main_layout.addLayout(controls_layout)
            
            # معلومات الإصلاحات
            fixes_label = QLabel("""
            ✅ الإصلاحات المطبقة:
            
            🔗 ربط البيانات:
            • إصلاح ربط البيانات في الرسم البياني
            • تحديث تلقائي للقيم الحالية
            • إعادة إنشاء الرسوم المتحركة عند التحديث
            • تحديث الحد الأقصى للحلقات تلقائياً
            
            📐 إعادة الترتيب:
            • الحلقات في صف واحد مع عناوين فرعية
            • الرسم البياني في قسم منفصل
            • تباعد محسن بين المكونات
            • تنظيم هرمي واضح
            
            🎯 التحسينات:
            • تحديث فوري عند بدء التشغيل
            • رسائل تشخيصية للمطورين
            • معالجة أفضل للأخطاء
            • واجهة أكثر تنظيماً
            """)
            fixes_label.setAlignment(Qt.AlignLeft)
            fixes_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #2c3e50;
                    background-color: #ecf0f1;
                    padding: 20px;
                    border-radius: 10px;
                    border-left: 5px solid #27ae60;
                    line-height: 1.6;
                }
            """)
            main_layout.addWidget(fixes_label)
            
            # تحديث البيانات الأولية
            QTimer.singleShot(1000, self.update_test_data)
            
        def update_test_data(self):
            """تحديث البيانات التجريبية"""
            print("🔄 تحديث البيانات التجريبية...")
            self.charts_widget.update_data(
                employee_count=52,
                department_count=9,
                total_salary=920000,
                advance_count=18,
                job_title_count=16
            )
            
        def update_random_data(self):
            """تحديث بيانات عشوائية"""
            import random
            print("🎲 تحديث بيانات عشوائية...")
            self.charts_widget.update_data(
                employee_count=random.randint(20, 80),
                department_count=random.randint(5, 15),
                total_salary=random.randint(500000, 1500000),
                advance_count=random.randint(5, 30),
                job_title_count=random.randint(8, 25)
            )
            
        def reset_data(self):
            """إعادة تعيين البيانات"""
            print("🔄 إعادة تعيين البيانات...")
            self.charts_widget.update_data(
                employee_count=0,
                department_count=0,
                total_salary=0,
                advance_count=0,
                job_title_count=0
            )
    
    return FixedChartsTest()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الرسومات البيانية المصححة والمرتبة")
    print("=" * 70)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    app.setStyleSheet(f.read())
                print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # تهيئة نظام المظهر
        try:
            from src.utils.appearance_manager import get_appearance_manager
            appearance_manager = get_appearance_manager()
            appearance_manager.load_settings()
            print("✅ تم تهيئة نظام المظهر")
        except Exception as e:
            print(f"⚠️ فشل في تهيئة نظام المظهر: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_fixed_charts_test()
        window.show()
        
        print("✅ تم فتح نافذة اختبار الرسومات المصححة!")
        print("\n🔧 الإصلاحات المطبقة:")
        print("• ربط البيانات في الرسم البياني")
        print("• إعادة ترتيب المخططات")
        print("• تحديث تلقائي للحدود القصوى")
        print("• تنظيم هرمي واضح")
        
        print("\n📐 الترتيب الجديد:")
        print("• القسم الأول: المؤشرات الرئيسية (3 حلقات)")
        print("• القسم الثاني: التوزيع التفصيلي (رسم بياني)")
        print("• عناوين فرعية لكل قسم")
        print("• تباعد محسن ومتوازن")
        
        print("\n🎮 أزرار التحكم:")
        print("• تحديث البيانات: بيانات تجريبية ثابتة")
        print("• بيانات عشوائية: قيم متغيرة")
        print("• إعادة تعيين: قيم صفر")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
