#!/usr/bin/env python3
"""
اختبار النافذة المصلحة
Test Fixed Dialog
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.database import db_manager, get_db_session_context
from src.models import SalaryRecord


def test_fixed_calculation():
    """اختبار الحساب المصلح"""
    print("🧪 اختبار الحساب المصلح...")
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        with get_db_session_context() as session:
            # البحث عن سجل راتب
            salary_record = session.query(SalaryRecord).first()
            
            if not salary_record:
                print("❌ لا يوجد سجل راتب")
                return False
            
            print(f"👤 الموظف: {salary_record.employee.full_name}")
            print(f"📅 الفترة: {salary_record.month}/{salary_record.year}")
            
            # عرض القيم الحالية
            print(f"\n📊 القيم الحالية:")
            print(f"   أيام الدوام: {salary_record.working_days}")
            print(f"   الراتب اليومي: {salary_record.daily_salary}")
            print(f"   المكافآت: {salary_record.total_bonuses}")
            print(f"   السلف: {salary_record.total_advances}")
            print(f"   الخصومات: {salary_record.total_deductions}")
            print(f"   الديون: {salary_record.total_market_debts}")
            print(f"   إجمالي الراتب: {salary_record.gross_salary}")
            print(f"   صافي الراتب: {salary_record.net_salary}")
            
            # اختبار إعادة الحساب
            print(f"\n🧮 اختبار إعادة الحساب...")
            old_net = salary_record.net_salary
            salary_record.calculate_salary()
            new_net = salary_record.net_salary
            
            print(f"   صافي الراتب قبل: {old_net}")
            print(f"   صافي الراتب بعد: {new_net}")
            print(f"   الفرق: {new_net - old_net}")
            
            if abs(new_net - old_net) < 0.01:  # تقريباً نفس القيمة
                print("✅ إعادة الحساب تعمل بشكل صحيح")
            else:
                print("⚠️ هناك اختلاف في إعادة الحساب")
            
            # اختبار تغيير أيام الدوام
            print(f"\n🔄 اختبار تغيير أيام الدوام...")
            original_days = salary_record.working_days
            salary_record.working_days = 25
            salary_record.calculate_salary()
            
            print(f"   أيام الدوام: {original_days} -> {salary_record.working_days}")
            print(f"   صافي الراتب الجديد: {salary_record.net_salary}")
            
            # إعادة القيم الأصلية
            salary_record.working_days = original_days
            salary_record.calculate_salary()
            
            session.commit()
            print("✅ الاختبار نجح!")
            
            return True
            
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """الدالة الرئيسية"""
    success = test_fixed_calculation()
    
    if success:
        print("\n🎉 جميع الإصلاحات تعمل بشكل صحيح!")
        print("\nيمكنك الآن:")
        print("1. تشغيل النظام: python run_hr_system.py")
        print("2. تعديل تفاصيل الراتب بدون أخطاء")
        return 0
    else:
        print("💥 لا تزال هناك مشاكل!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
