#!/usr/bin/env python3
"""
اختبار حذف النص المكرر من مخطط توزيع الموظفين
Test Removing Duplicate Title from Employee Distribution Chart
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QFrame, QHBoxLayout
from PySide6.QtCore import Qt


def create_no_duplicate_test():
    """إنشاء نافذة اختبار حذف النص المكرر"""
    
    class NoDuplicateTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار حذف النص المكرر من مخطط توزيع الموظفين")
            self.setGeometry(100, 100, 1000, 700)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(30, 30, 30, 30)
            main_layout.setSpacing(25)
            
            # عنوان الاختبار
            test_title = QLabel("🗑️ اختبار حذف النص المكرر")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 15px;
                    padding: 20px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #27ae60, stop: 1 #229954);
                    color: white;
                    border-radius: 12px;
                }
            """)
            main_layout.addWidget(test_title)
            
            # إطار التوضيح
            explanation_frame = QFrame()
            explanation_frame.setStyleSheet("""
                QFrame {
                    background-color: #d5f4e6;
                    border: 2px solid #27ae60;
                    border-radius: 10px;
                    padding: 20px;
                }
            """)
            
            explanation_layout = QVBoxLayout(explanation_frame)
            
            explanation_title = QLabel("✅ تم حذف النص المكرر بنجاح!")
            explanation_title.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: #27ae60;
                    margin-bottom: 15px;
                }
            """)
            explanation_layout.addWidget(explanation_title)
            
            explanation_text = QLabel("""
            المشكلة التي تم حلها:
            • كان هناك عنوان مكرر "📊 توزيع الموظفين على الأقسام" داخل الرسم البياني
            • هذا العنوان كان يظهر فوق العنوان الرئيسي للقسم
            • تم حذف العنوان المكرر من داخل الرسم البياني
            • الآن يظهر عنوان واحد فقط: "👥 توزيع الموظفين على الأقسام"
            
            التحسينات المطبقة:
            • حذف العنوان المكرر من paintEvent
            • تقليل الهامش العلوي للرسم البياني
            • استغلال أفضل للمساحة المتاحة
            • تنظيف الكود من التكرار غير المرغوب
            """)
            explanation_text.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #1e8449;
                    line-height: 1.6;
                    padding: 10px;
                }
            """)
            explanation_layout.addWidget(explanation_text)
            
            main_layout.addWidget(explanation_frame)
            
            # إطار الرسم البياني المحسن
            chart_frame = QFrame()
            chart_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 3px solid #bdc3c7;
                    border-radius: 15px;
                    margin: 10px;
                    padding: 20px;
                }
            """)
            
            chart_layout = QVBoxLayout(chart_frame)
            chart_layout.setContentsMargins(30, 20, 30, 20)
            chart_layout.setSpacing(15)
            
            # العنوان الوحيد (غير مكرر)
            section_title = QLabel("👥 توزيع الموظفين على الأقسام")
            section_title.setAlignment(Qt.AlignCenter)
            section_title.setStyleSheet("""
                QLabel {
                    font-size: 20px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 12px 0;
                    padding: 15px;
                    letter-spacing: 1.5px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                              stop: 0 rgba(52, 152, 219, 0.1),
                                              stop: 1 rgba(155, 89, 182, 0.1));
                    border-radius: 10px;
                    border: 2px solid rgba(52, 152, 219, 0.3);
                    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
                }
            """)
            chart_layout.addWidget(section_title)
            
            # حاوية توسيط الرسم البياني
            chart_container = QHBoxLayout()
            chart_container.addStretch(1)
            
            # الرسم البياني (بدون عنوان مكرر)
            from src.widgets.enhanced_dashboard import DepartmentDistributionChart
            self.dept_chart = DepartmentDistributionChart()
            chart_container.addWidget(self.dept_chart)
            
            chart_container.addStretch(1)
            chart_layout.addLayout(chart_container)
            
            # وصف توضيحي
            description = QLabel("📈 الآن يظهر عنوان واحد فقط بدون تكرار - تنظيف وتحسين للواجهة")
            description.setAlignment(Qt.AlignCenter)
            description.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #5d6d7e;
                    font-style: italic;
                    font-weight: 500;
                    margin-top: 15px;
                    padding: 12px;
                    background-color: rgba(248, 249, 250, 0.9);
                    border-radius: 8px;
                    border: 1px solid rgba(189, 195, 199, 0.4);
                }
            """)
            chart_layout.addWidget(description)
            
            main_layout.addWidget(chart_frame)
            
            # معلومات التحسين
            improvement_info = QLabel("""
            🎯 فوائد حذف النص المكرر:
            
            ✅ تنظيف الواجهة: إزالة التكرار غير المرغوب
            ✅ استغلال أفضل للمساحة: مساحة أكبر للرسم البياني
            ✅ وضوح أكبر: عنوان واحد واضح ومحدد
            ✅ تجربة مستخدم أفضل: عدم الإرباك بالعناوين المكررة
            ✅ كود أنظف: إزالة الكود غير الضروري
            ✅ تصميم احترافي: تنظيم أفضل للعناصر
            """)
            improvement_info.setAlignment(Qt.AlignLeft)
            improvement_info.setStyleSheet("""
                QLabel {
                    font-size: 13px;
                    color: #2c3e50;
                    background-color: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    border-left: 5px solid #27ae60;
                    line-height: 1.6;
                }
            """)
            main_layout.addWidget(improvement_info)
            
            # تحميل بيانات تجريبية
            self.load_sample_data()
            
        def load_sample_data(self):
            """تحميل بيانات تجريبية"""
            sample_data = [
                ("الإدارة العامة", 12),
                ("المحاسبة", 8),
                ("التسويق", 15),
                ("المبيعات", 20),
                ("تقنية المعلومات", 7),
                ("خدمة العملاء", 9)
            ]
            self.dept_chart.update_data(sample_data)
    
    return NoDuplicateTest()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار حذف النص المكرر من مخطط توزيع الموظفين")
    print("=" * 70)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            enhanced_style_file = styles_dir / "enhanced_dashboard.qss"
            
            combined_styles = ""
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read() + "\n"
                    
            if enhanced_style_file.exists():
                with open(enhanced_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read()
                    
            app.setStyleSheet(combined_styles)
            print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_no_duplicate_test()
        window.show()
        
        print("✅ تم فتح نافذة اختبار حذف النص المكرر!")
        print("\n🗑️ المشكلة التي تم حلها:")
        print("• كان هناك عنوان مكرر داخل الرسم البياني")
        print("• العنوان كان يظهر فوق العنوان الرئيسي للقسم")
        print("• تم حذف العنوان المكرر من paintEvent")
        
        print("\n✅ التحسينات المطبقة:")
        print("• حذف العنوان المكرر")
        print("• تقليل الهامش العلوي")
        print("• استغلال أفضل للمساحة")
        print("• تنظيف الكود")
        print("• تحسين تجربة المستخدم")
        
        print("\n🎯 النتيجة:")
        print("• عنوان واحد فقط: '👥 توزيع الموظفين على الأقسام'")
        print("• مساحة أكبر للرسم البياني")
        print("• واجهة أنظف وأكثر تنظيماً")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
