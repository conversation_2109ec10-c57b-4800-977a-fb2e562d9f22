#!/usr/bin/env python3
"""
اختبار الشريط العلوي المحسن
Test Enhanced Header
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QFrame, QHBoxLayout, QPushButton
from PySide6.QtCore import Qt


def create_enhanced_header_test():
    """إنشاء نافذة اختبار الشريط العلوي المحسن"""
    
    class EnhancedHeaderTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار الشريط العلوي المحسن")
            self.setGeometry(100, 100, 1200, 800)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            # إنشاء الشريط العلوي المحسن
            self.create_enhanced_header()
            main_layout.addWidget(self.header)
            
            # محتوى الاختبار
            content_widget = QWidget()
            content_layout = QVBoxLayout(content_widget)
            content_layout.setContentsMargins(30, 30, 30, 30)
            content_layout.setSpacing(20)
            
            # عنوان الاختبار
            test_title = QLabel("🧪 اختبار الشريط العلوي المحسن")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 20px;
                    padding: 20px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #3498db, stop: 1 #2980b9);
                    color: white;
                    border-radius: 12px;
                }
            """)
            content_layout.addWidget(test_title)
            
            # معلومات التحسينات
            improvements_info = QLabel("""
            ✅ التحسينات المطبقة على الشريط العلوي:
            
            🎨 التصميم الجديد:
            • ارتفاع أكبر (90px بدلاً من 80px)
            • خلفية متدرجة جميلة
            • حدود سفلية ملونة (3px أزرق)
            • هوامش محسنة (40px من الجانبين)
            
            📝 العناوين المحسنة:
            • العنوان الرئيسي: "🏢 نظام إدارة الموارد البشرية" (24px)
            • العنوان الفرعي: "نظام متقدم لإدارة الموظفين..." (14px)
            • تباعد أحرف محسن (2px للرئيسي، 1px للفرعي)
            • ظلال نصية للعمق البصري
            
            👤 بطاقة المستخدم المحسنة:
            • تصميم بطاقة منفصلة (200×50px)
            • خلفية متدرجة مع حدود ملونة
            • أيقونة مستخدم محسنة مع خلفية دائرية
            • "مرحباً بك" + "المدير العام"
            • تنظيم عمودي للنصوص
            
            ⚙️ زر الإعدادات المحسن:
            • حجم أكبر (50×50px)
            • تصميم دائري مع تدرجات
            • تأثيرات hover وpress
            • ظلال نصية
            """)
            improvements_info.setAlignment(Qt.AlignLeft)
            improvements_info.setStyleSheet("""
                QLabel {
                    font-size: 13px;
                    color: #2c3e50;
                    background-color: #f8f9fa;
                    padding: 25px;
                    border-radius: 12px;
                    border-left: 5px solid #3498db;
                    line-height: 1.6;
                }
            """)
            content_layout.addWidget(improvements_info)
            
            # مقارنة قبل وبعد
            comparison_frame = QFrame()
            comparison_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 2px solid #bdc3c7;
                    border-radius: 12px;
                    padding: 20px;
                }
            """)
            
            comparison_layout = QVBoxLayout(comparison_frame)
            
            comparison_title = QLabel("📊 مقارنة قبل وبعد التحسين")
            comparison_title.setAlignment(Qt.AlignCenter)
            comparison_title.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 15px;
                }
            """)
            comparison_layout.addWidget(comparison_title)
            
            comparison_text = QLabel("""
            ❌ قبل التحسين:
            • عنوان بسيط: "نظام إدارة الموارد البشرية"
            • نص "مرحباً بك" بسيط
            • زر إعدادات صغير (40×40)
            • تصميم أساسي بدون تأثيرات
            
            ✅ بعد التحسين:
            • عنوان رئيسي + فرعي مع أيقونة
            • بطاقة مستخدم احترافية
            • زر إعدادات محسن مع تأثيرات
            • تصميم متدرج مع ظلال وحدود
            • تنظيم أفضل ومساحات محسنة
            """)
            comparison_text.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #34495e;
                    line-height: 1.6;
                    padding: 15px;
                }
            """)
            comparison_layout.addWidget(comparison_text)
            
            content_layout.addWidget(comparison_frame)
            content_layout.addStretch()
            
            main_layout.addWidget(content_widget)
            
        def create_enhanced_header(self):
            """إنشاء الشريط العلوي المحسن"""
            self.header = QFrame()
            self.header.setObjectName("enhanced_header")
            self.header.setFixedHeight(90)

            header_layout = QHBoxLayout(self.header)
            header_layout.setContentsMargins(40, 20, 40, 20)
            header_layout.setSpacing(30)

            # قسم العنوان الرئيسي المحسن
            title_section = QVBoxLayout()
            title_section.setSpacing(5)

            # العنوان الرئيسي
            main_title = QLabel("🏢 نظام إدارة الموارد البشرية")
            main_title.setObjectName("main_header_title")
            main_title.setAlignment(Qt.AlignLeft)

            # العنوان الفرعي
            subtitle = QLabel("نظام متقدم لإدارة الموظفين والمعاملات المالية")
            subtitle.setObjectName("header_subtitle")
            subtitle.setAlignment(Qt.AlignLeft)

            title_section.addWidget(main_title)
            title_section.addWidget(subtitle)

            # قسم معلومات المستخدم المحسن
            user_section = QHBoxLayout()
            user_section.setSpacing(15)

            # بطاقة المستخدم
            user_card = QFrame()
            user_card.setObjectName("user_card")
            user_card.setFixedSize(200, 50)
            
            user_card_layout = QHBoxLayout(user_card)
            user_card_layout.setContentsMargins(15, 8, 15, 8)
            user_card_layout.setSpacing(10)

            # أيقونة المستخدم
            user_icon = QLabel("👤")
            user_icon.setObjectName("user_icon")
            user_icon.setFixedSize(30, 30)
            user_icon.setAlignment(Qt.AlignCenter)

            # معلومات المستخدم
            user_info_layout = QVBoxLayout()
            user_info_layout.setSpacing(2)
            
            welcome_label = QLabel("مرحباً بك")
            welcome_label.setObjectName("welcome_label")
            
            user_name = QLabel("المدير العام")
            user_name.setObjectName("user_name")

            user_info_layout.addWidget(welcome_label)
            user_info_layout.addWidget(user_name)

            user_card_layout.addWidget(user_icon)
            user_card_layout.addLayout(user_info_layout)
            user_card_layout.addStretch()

            # زر الإعدادات المحسن
            settings_btn = QPushButton("⚙️")
            settings_btn.setObjectName("enhanced_settings_btn")
            settings_btn.setFixedSize(50, 50)
            settings_btn.setToolTip("إعدادات النظام")
            settings_btn.clicked.connect(lambda: print("🔄 تم النقر على زر الإعدادات"))

            user_section.addWidget(user_card)
            user_section.addWidget(settings_btn)

            # تجميع الأقسام
            header_layout.addLayout(title_section)
            header_layout.addStretch()
            header_layout.addLayout(user_section)
    
    return EnhancedHeaderTest()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الشريط العلوي المحسن")
    print("=" * 50)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            enhanced_style_file = styles_dir / "enhanced_dashboard.qss"
            
            combined_styles = ""
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read() + "\n"
                    
            if enhanced_style_file.exists():
                with open(enhanced_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read()
                    
            app.setStyleSheet(combined_styles)
            print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_enhanced_header_test()
        window.show()
        
        print("✅ تم فتح نافذة اختبار الشريط العلوي المحسن!")
        print("\n🎨 التحسينات المطبقة:")
        print("• ارتفاع أكبر (90px)")
        print("• عنوان رئيسي + فرعي")
        print("• بطاقة مستخدم احترافية")
        print("• زر إعدادات محسن")
        print("• خلفية متدرجة وحدود ملونة")
        
        print("\n📝 العناوين الجديدة:")
        print("• الرئيسي: '🏢 نظام إدارة الموارد البشرية' (24px)")
        print("• الفرعي: 'نظام متقدم لإدارة الموظفين...' (14px)")
        
        print("\n👤 بطاقة المستخدم:")
        print("• حجم: 200×50 بكسل")
        print("• أيقونة دائرية مع خلفية")
        print("• 'مرحباً بك' + 'المدير العام'")
        print("• تصميم متدرج مع حدود")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
