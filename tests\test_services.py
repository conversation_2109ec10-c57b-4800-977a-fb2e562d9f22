"""
اختبارات الخدمات
Services Tests
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
from datetime import date, datetime
from decimal import Decimal

from src.services.report_service import ReportService
from src.services.backup_service import BackupService


class TestReportService:
    """اختبارات خدمة التقارير"""
    
    @pytest.fixture
    def report_service(self):
        """إنشاء خدمة التقارير للاختبار"""
        return ReportService()
        
    def test_report_service_creation(self, report_service):
        """اختبار إنشاء خدمة التقارير"""
        assert report_service is not None
        
    @patch('src.services.report_service.get_db_session_context')
    def test_generate_employees_report(self, mock_db_context, report_service):
        """اختبار توليد تقرير الموظفين"""
        # إعداد البيانات الوهمية
        mock_session = Mock()
        mock_db_context.return_value.__enter__.return_value = mock_session
        
        # إعداد موظف وهمي
        mock_employee = Mock()
        mock_employee.employee_number = "EMP001"
        mock_employee.full_name = "محمد أحمد"
        mock_employee.department.name = "تقنية المعلومات"
        mock_employee.job_title.title = "مطور"
        mock_employee.basic_salary = Decimal("5000.00")
        mock_employee.employment_status.name = "ACTIVE"
        
        mock_session.query.return_value.filter_by.return_value.all.return_value = [mock_employee]
        
        # معاملات التقرير
        parameters = {
            "subtype": "list",
            "department_id": None,
            "job_title_id": None,
            "employment_status": None
        }
        
        # توليد التقرير
        result = report_service.generate_employees_report(parameters)
        
        # التأكد من النتيجة
        assert result is not None
        assert isinstance(result, bytes)
        assert len(result) > 0
        
    @patch('src.services.report_service.get_db_session_context')
    def test_generate_salaries_report(self, mock_db_context, report_service):
        """اختبار توليد تقرير الرواتب"""
        mock_session = Mock()
        mock_db_context.return_value.__enter__.return_value = mock_session
        
        # إعداد سجل راتب وهمي
        mock_salary_record = Mock()
        mock_salary_record.employee.full_name = "محمد أحمد"
        mock_salary_record.basic_salary = Decimal("5000.00")
        mock_salary_record.gross_salary = Decimal("5500.00")
        mock_salary_record.net_salary = Decimal("5200.00")
        mock_salary_record.is_paid = True
        
        mock_session.query.return_value.filter.return_value.all.return_value = [mock_salary_record]
        
        parameters = {
            "subtype": "summary",
            "month": 1,
            "year": 2024,
            "department_id": None
        }
        
        result = report_service.generate_salaries_report(parameters)
        
        assert result is not None
        assert isinstance(result, bytes)
        
    @patch('src.services.report_service.get_db_session_context')
    def test_generate_financial_report(self, mock_db_context, report_service):
        """اختبار توليد تقرير المعاملات المالية"""
        mock_session = Mock()
        mock_db_context.return_value.__enter__.return_value = mock_session
        
        # إعداد معاملة مالية وهمية
        mock_transaction = Mock()
        mock_transaction.employee.full_name = "محمد أحمد"
        mock_transaction.transaction_type.name = "ADVANCE"
        mock_transaction.amount = Decimal("1000.00")
        mock_transaction.transaction_date = date.today()
        mock_transaction.payment_status.value = "معلق"
        mock_transaction.remaining_amount = Decimal("500.00")
        
        mock_session.query.return_value.filter.return_value.all.return_value = [mock_transaction]
        
        parameters = {
            "subtype": "summary",
            "from_date": date.today(),
            "to_date": date.today(),
            "transaction_type": None
        }
        
        result = report_service.generate_financial_report(parameters)
        
        assert result is not None
        assert isinstance(result, bytes)
        
    @patch('src.services.report_service.get_db_session_context')
    def test_generate_employee_account_report(self, mock_db_context, report_service):
        """اختبار توليد تقرير كشف حساب الموظف"""
        mock_session = Mock()
        mock_db_context.return_value.__enter__.return_value = mock_session
        
        # إعداد موظف وهمي
        mock_employee = Mock()
        mock_employee.full_name = "محمد أحمد"
        mock_employee.employee_number = "EMP001"
        mock_employee.department.name = "تقنية المعلومات"
        mock_employee.job_title.title = "مطور"
        mock_employee.basic_salary = Decimal("5000.00")
        
        mock_session.query.return_value.filter_by.return_value.first.return_value = mock_employee
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = []
        
        parameters = {
            "subtype": "statement",
            "employee_id": 1,
            "from_date": date.today(),
            "to_date": date.today()
        }
        
        result = report_service.generate_employee_account_report(parameters)
        
        assert result is not None
        assert isinstance(result, bytes)
        
    def test_export_to_excel(self, report_service):
        """اختبار تصدير البيانات إلى Excel"""
        headers = ["الاسم", "القسم", "الراتب"]
        data = [
            ["محمد أحمد", "تقنية المعلومات", "5000"],
            ["فاطمة علي", "المحاسبة", "4500"]
        ]
        
        result = report_service.export_to_excel(data, headers, "test_report")
        
        assert result is not None
        assert isinstance(result, bytes)
        assert len(result) > 0


class TestBackupService:
    """اختبارات خدمة النسخ الاحتياطي"""
    
    @pytest.fixture
    def backup_service(self):
        """إنشاء خدمة النسخ الاحتياطي للاختبار"""
        return BackupService()
        
    def test_backup_service_creation(self, backup_service):
        """اختبار إنشاء خدمة النسخ الاحتياطي"""
        assert backup_service is not None
        assert backup_service.backup_dir is not None
        
    @patch('src.services.backup_service.subprocess.run')
    @patch('src.services.backup_service.get_config')
    def test_backup_database(self, mock_get_config, mock_subprocess, backup_service):
        """اختبار نسخ قاعدة البيانات"""
        # إعداد التكوين الوهمي
        mock_get_config.return_value = {
            "host": "localhost",
            "port": 5432,
            "username": "test_user",
            "password": "test_pass",
            "database": "test_db"
        }
        
        # إعداد نتيجة subprocess وهمية
        mock_result = Mock()
        mock_result.returncode = 0
        mock_subprocess.return_value = mock_result
        
        # إنشاء مجلد مؤقت
        temp_dir = Path("/tmp/test_backup")
        temp_dir.mkdir(exist_ok=True)
        
        try:
            result = backup_service._backup_database(temp_dir)
            
            # التأكد من استدعاء pg_dump
            assert mock_subprocess.called
            
            # التأكد من النتيجة
            if result:
                assert result.exists() or result is None
                
        finally:
            # تنظيف
            if temp_dir.exists():
                import shutil
                shutil.rmtree(temp_dir)
                
    @patch('src.services.backup_service.get_config')
    def test_backup_configuration(self, mock_get_config, backup_service):
        """اختبار نسخ ملفات التكوين"""
        # إعداد التكوين الوهمي
        mock_get_config.side_effect = lambda key: {
            "app": {"name": "HR System"},
            "ui": {"theme": "light"},
            "reports": {"format": "pdf"},
            "backup": {"auto": True}
        }.get(key, {})
        
        # إنشاء مجلد مؤقت
        temp_dir = Path("/tmp/test_config")
        temp_dir.mkdir(exist_ok=True)
        
        try:
            result = backup_service._backup_configuration(temp_dir)
            
            # التأكد من النتيجة
            if result:
                assert result.exists()
                assert result.suffix == ".json"
                
        finally:
            # تنظيف
            if temp_dir.exists():
                import shutil
                shutil.rmtree(temp_dir)
                
    def test_list_backups(self, backup_service):
        """اختبار قائمة النسخ الاحتياطية"""
        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        backup_service.backup_dir.mkdir(exist_ok=True)
        
        backups = backup_service.list_backups()
        
        # التأكد من النتيجة
        assert isinstance(backups, list)
        
    @patch('src.services.backup_service.zipfile.ZipFile')
    def test_create_zip_archive(self, mock_zipfile, backup_service):
        """اختبار إنشاء أرشيف مضغوط"""
        # إعداد ZipFile وهمي
        mock_zip = Mock()
        mock_zipfile.return_value.__enter__.return_value = mock_zip
        
        # إنشاء مجلدات مؤقتة
        source_dir = Path("/tmp/test_source")
        archive_path = Path("/tmp/test_archive.zip")
        
        source_dir.mkdir(exist_ok=True)
        
        # إنشاء ملف اختبار
        test_file = source_dir / "test.txt"
        test_file.write_text("test content")
        
        try:
            backup_service._create_zip_archive(source_dir, archive_path)
            
            # التأكد من استدعاء ZipFile
            assert mock_zipfile.called
            
        finally:
            # تنظيف
            if source_dir.exists():
                import shutil
                shutil.rmtree(source_dir)
            if archive_path.exists():
                archive_path.unlink()
                
    def test_cleanup_old_backups(self, backup_service):
        """اختبار تنظيف النسخ الاحتياطية القديمة"""
        # إنشاء مجلد النسخ الاحتياطية
        backup_service.backup_dir.mkdir(exist_ok=True)
        
        # محاولة تنظيف النسخ القديمة
        try:
            backup_service._cleanup_old_backups()
            # إذا لم يحدث خطأ، فالاختبار نجح
            assert True
        except Exception as e:
            # إذا حدث خطأ، يجب أن يكون مُتوقعاً
            assert "config" in str(e).lower() or "backup" in str(e).lower()
            
    @patch('src.services.backup_service.schedule')
    def test_start_scheduler(self, mock_schedule, backup_service):
        """اختبار بدء جدولة النسخ الاحتياطي"""
        with patch('src.services.backup_service.get_config') as mock_get_config:
            mock_get_config.return_value = {
                "auto_backup": True,
                "backup_interval": "daily",
                "backup_time": "02:00"
            }
            
            backup_service.start_scheduler()
            
            # التأكد من إعداد الجدولة
            assert mock_schedule.every.called
            
    def test_stop_scheduler(self, backup_service):
        """اختبار إيقاف جدولة النسخ الاحتياطي"""
        backup_service.scheduler_running = True
        
        backup_service.stop_scheduler()
        
        assert not backup_service.scheduler_running
