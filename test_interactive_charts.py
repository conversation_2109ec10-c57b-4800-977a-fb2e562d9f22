#!/usr/bin/env python3
"""
اختبار الرسومات البيانية التفاعلية
Test Interactive Charts
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import Qt


def create_charts_test_window():
    """إنشاء نافذة اختبار الرسومات البيانية"""
    
    class ChartsTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار الرسومات البيانية التفاعلية")
            self.setGeometry(100, 100, 1400, 900)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # عنوان الاختبار
            test_title = QLabel("📊 اختبار الرسومات البيانية التفاعلية")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 28px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 20px;
                    padding: 20px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #3498db, stop: 1 #2980b9);
                    color: white;
                    border-radius: 15px;
                }
            """)
            main_layout.addWidget(test_title)
            
            # إنشاء مكون الرسومات البيانية
            from src.widgets import StatisticsChartWidget
            self.charts_widget = StatisticsChartWidget()
            main_layout.addWidget(self.charts_widget)
            
            # معلومات الميزات
            features_label = QLabel("""
            🎯 الميزات الجديدة:
            
            📈 حلقات التقدم المتحركة:
            • رسوم متحركة ناعمة مع تأثير Cubic
            • تفاعل عند التمرير (hover)
            • عرض النسبة المئوية والقيمة
            • ألوان متدرجة جميلة
            
            📊 الرسم البياني بالأعمدة:
            • رسوم متحركة مع تأثير Bounce
            • تأخير متدرج للأعمدة
            • تفاعل عند التمرير
            • عرض القيم فوق الأعمدة
            
            🎨 التصميم التفاعلي:
            • ألوان متناسقة ومتدرجة
            • تأثيرات بصرية متقدمة
            • استجابة للماوس
            • تحديث تلقائي للبيانات
            """)
            features_label.setAlignment(Qt.AlignLeft)
            features_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #34495e;
                    background-color: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    border-left: 5px solid #3498db;
                    line-height: 1.6;
                }
            """)
            main_layout.addWidget(features_label)
            
            # تحديث البيانات التجريبية
            self.update_test_data()
            
        def update_test_data(self):
            """تحديث البيانات التجريبية"""
            self.charts_widget.update_data(
                employee_count=45,
                department_count=8,
                total_salary=850000,
                advance_count=12,
                job_title_count=15
            )
    
    return ChartsTestWindow()


def test_chart_components(app):
    """اختبار مكونات الرسومات البيانية"""
    print("🔍 اختبار مكونات الرسومات البيانية...")

    try:
        from src.widgets import AnimatedProgressRing, AnimatedBarChart, StatisticsChartWidget

        # اختبار حلقة التقدم
        ring = AnimatedProgressRing("اختبار", 75, 100, "#3498db")
        print("✅ تم إنشاء حلقة التقدم المتحركة")

        # اختبار الرسم البياني
        data = [("اختبار 1", 50), ("اختبار 2", 30)]
        colors = ["#3498db", "#27ae60"]
        bar_chart = AnimatedBarChart("اختبار الأعمدة", data, colors)
        print("✅ تم إنشاء الرسم البياني بالأعمدة")

        # اختبار المكون الرئيسي
        charts_widget = StatisticsChartWidget()
        print("✅ تم إنشاء مكون الرسومات البيانية")

        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار المكونات: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الرسومات البيانية التفاعلية")
    print("=" * 60)

    try:
        # إنشاء التطبيق أولاً
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)

        # اختبار المكونات بعد إنشاء التطبيق
        if not test_chart_components(app):
            print("💥 فشل في اختبار مكونات الرسومات البيانية")
            return 1
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    app.setStyleSheet(f.read())
                print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # تهيئة نظام المظهر
        try:
            from src.utils.appearance_manager import get_appearance_manager
            appearance_manager = get_appearance_manager()
            appearance_manager.load_settings()
            print("✅ تم تهيئة نظام المظهر")
        except Exception as e:
            print(f"⚠️ فشل في تهيئة نظام المظهر: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_charts_test_window()
        window.show()
        
        print("✅ تم فتح نافذة اختبار الرسومات البيانية!")
        print("\n🎯 الرسومات البيانية الجديدة:")
        print("• حلقات تقدم متحركة (3 حلقات)")
        print("• رسم بياني بالأعمدة تفاعلي")
        print("• رسوم متحركة ناعمة")
        print("• تفاعل مع الماوس")
        print("• ألوان متدرجة جميلة")
        
        print("\n🎨 الميزات التفاعلية:")
        print("• تأثيرات hover للحلقات")
        print("• تأثيرات hover للأعمدة")
        print("• رسوم متحركة عند التحديث")
        print("• عرض القيم والنسب المئوية")
        print("• تصميم حديث ومتجاوب")
        
        print("\n📊 البيانات المعروضة:")
        print("• إجمالي الموظفين: 45")
        print("• الأقسام: 8")
        print("• الرواتب: 850 ألف")
        print("• السلف: 12")
        print("• المناصب: 15")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
