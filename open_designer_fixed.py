#!/usr/bin/env python3
"""
سكريبت لفتح Qt Designer مع ملف الواجهة المصحح
Script to open Qt Designer with fixed main window UI file
"""

import subprocess
import sys
import os

def open_qt_designer():
    """فتح Qt Designer مع ملف الواجهة المصحح"""
    ui_file = "ui_files/main_window_fixed.ui"
    
    print("🎨 فتح Qt Designer مع الملف المصحح...")
    print("=" * 50)
    
    # التحقق من وجود الملف
    if not os.path.exists(ui_file):
        print(f"❌ الملف غير موجود: {ui_file}")
        return False
    
    try:
        # محاولة فتح Qt Designer مع الملف
        print(f"📂 فتح الملف: {ui_file}")
        
        # تشغيل Qt Designer
        subprocess.Popen([
            'pyside6-designer', 
            ui_file
        ])
        
        print("✅ تم فتح Qt Designer بنجاح!")
        print("\n🎯 الملف المصحح يحتوي على:")
        print("- ✅ هيكل صحيح بدون أخطاء")
        print("- 🎨 شريط جانبي مع 5 أزرار قوائم")
        print("- 📱 منطقة محتوى قابلة للتخصيص")
        print("- 🎛️ شريط قوائم وشريط حالة")
        
        print("\n📝 يمكنك الآن:")
        print("- تعديل الألوان والخطوط")
        print("- إضافة عناصر جديدة")
        print("- تغيير ترتيب القوائم")
        print("- تخصيص التصميم")
        
        print("\n💾 بعد التعديل:")
        print("1. احفظ بـ Ctrl+S")
        print("2. شغّل: python convert_ui.py")
        
        return True
        
    except FileNotFoundError:
        print("❌ Qt Designer غير مثبت")
        print("\n🔧 حلول:")
        print("1. pip install PySide6")
        print("2. أو: pyside6-designer")
        return False
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 مشغل Qt Designer - الإصدار المصحح")
    print("=" * 50)
    
    # عرض معلومات الملف
    ui_file = "ui_files/main_window_fixed.ui"
    if os.path.exists(ui_file):
        file_size = os.path.getsize(ui_file)
        print(f"📄 الملف: {ui_file}")
        print(f"📏 الحجم: {file_size:,} بايت")
        print(f"✅ الحالة: جاهز للتعديل")
    
    print()
    
    # فتح Qt Designer
    success = open_qt_designer()
    
    if not success:
        print("\n💡 بدائل:")
        print("- Terminal: pyside6-designer ui_files/main_window_fixed.ui")
        print("- VS Code: Ctrl+` ثم الأمر أعلاه")

if __name__ == "__main__":
    main()
