"""
نموذج المعاملات المالية
Financial Transaction model
"""

from datetime import date
from enum import Enum
from sqlalchemy import Column, String, Date, Numeric, ForeignKey, Text, Boolean, Enum as SQLEnum, Integer
from sqlalchemy.orm import relationship
from .base import BaseModel


class TransactionType(Enum):
    """نوع المعاملة المالية"""
    ADVANCE = "سلفة"
    DEDUCTION = "خصم"
    BONUS = "مكافأة"
    MARKET_DEBT = "دين الماركت"


class PaymentStatus(Enum):
    """حالة الدفع"""
    PENDING = "معلق"
    PAID = "مدفوع"
    PARTIALLY_PAID = "مدفوع جزئياً"
    CANCELLED = "ملغي"


class FinancialTransaction(BaseModel):
    """نموذج المعاملات المالية"""
    
    __tablename__ = "financial_transactions"
    
    # معلومات المعاملة
    transaction_type = Column(
        SQLEnum(TransactionType), 
        nullable=False,
        comment="نوع المعاملة"
    )
    amount = Column(Numeric(12, 2), nullable=False, comment="المبلغ")
    transaction_date = Column(Date, nullable=False, default=date.today, comment="تاريخ المعاملة")
    description = Column(Text, nullable=True, comment="الوصف/الملاحظات")
    
    # حالة الدفع (للسلف والديون)
    payment_status = Column(
        SQLEnum(PaymentStatus),
        nullable=False,
        default=PaymentStatus.PENDING,
        comment="حالة الدفع"
    )
    paid_amount = Column(Numeric(12, 2), nullable=False, default=0, comment="المبلغ المدفوع")
    payment_date = Column(Date, nullable=True, comment="تاريخ الدفع")
    
    # المفاتيح الخارجية
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=False)
    
    # العلاقات
    employee = relationship("Employee", back_populates="financial_transactions")
    
    def __repr__(self) -> str:
        return f"<FinancialTransaction(id={self.id}, type='{self.transaction_type.value}', amount={self.amount})>"
    
    def __str__(self) -> str:
        return f"{self.transaction_type.value} - {self.amount} - {self.employee.full_name}"
    
    @property
    def remaining_amount(self) -> float:
        """المبلغ المتبقي"""
        return float(self.amount) - float(self.paid_amount)
    
    @property
    def is_fully_paid(self) -> bool:
        """هل تم دفع المبلغ كاملاً"""
        return self.payment_status == PaymentStatus.PAID or self.remaining_amount <= 0
    
    @property
    def is_debt_type(self) -> bool:
        """هل هذه المعاملة من نوع الديون (سلفة أو دين ماركت)"""
        return self.transaction_type in [TransactionType.ADVANCE, TransactionType.MARKET_DEBT]
    
    def make_payment(self, amount: float, payment_date: date = None) -> bool:
        """تسجيل دفعة"""
        if not self.is_debt_type:
            return False
        
        if amount <= 0 or amount > self.remaining_amount:
            return False
        
        self.paid_amount = float(self.paid_amount) + amount
        self.payment_date = payment_date or date.today()
        
        # تحديث حالة الدفع
        if self.remaining_amount <= 0:
            self.payment_status = PaymentStatus.PAID
        elif self.paid_amount > 0:
            self.payment_status = PaymentStatus.PARTIALLY_PAID
        
        return True
    
    def cancel_transaction(self) -> None:
        """إلغاء المعاملة"""
        self.payment_status = PaymentStatus.CANCELLED
        self.soft_delete()
    
    @classmethod
    def get_employee_transactions(cls, session, employee_id: int, transaction_type: TransactionType = None):
        """الحصول على معاملات موظف معين"""
        query = session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.is_active == True
        )
        
        if transaction_type:
            query = query.filter(cls.transaction_type == transaction_type)
        
        return query.order_by(cls.transaction_date.desc()).all()
    
    @classmethod
    def get_pending_debts(cls, session, employee_id: int = None):
        """الحصول على الديون المعلقة"""
        query = session.query(cls).filter(
            cls.transaction_type.in_([TransactionType.ADVANCE, TransactionType.MARKET_DEBT]),
            cls.payment_status.in_([PaymentStatus.PENDING, PaymentStatus.PARTIALLY_PAID]),
            cls.is_active == True
        )
        
        if employee_id:
            query = query.filter(cls.employee_id == employee_id)
        
        return query.order_by(cls.transaction_date.desc()).all()
