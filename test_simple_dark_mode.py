#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط للوضع الليلي المحسن
Simple Enhanced Dark Mode Test
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def create_simple_test_window():
    """إنشاء نافذة اختبار بسيطة"""
    
    class SimpleTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("🌙 اختبار الوضع الليلي البسيط")
            self.setGeometry(200, 200, 800, 600)
            
            # تطبيق الوضع الليلي
            self.apply_dark_mode()
            
            # إعداد الواجهة
            self.setup_ui()
            
        def apply_dark_mode(self):
            """تطبيق الوضع الليلي"""
            try:
                styles_dir = Path("src/styles")
                dark_file = styles_dir / "dark.qss"
                
                if dark_file.exists():
                    with open(dark_file, 'r', encoding='utf-8') as f:
                        stylesheet = f.read()
                    
                    # إزالة المتغيرات غير المدعومة
                    stylesheet = self.fix_css_variables(stylesheet)
                    
                    self.setStyleSheet(stylesheet)
                    print("✅ تم تطبيق الوضع الليلي")
                else:
                    print("❌ ملف الوضع الليلي غير موجود")
                    
            except Exception as e:
                print(f"❌ خطأ في تطبيق الوضع الليلي: {e}")
        
        def fix_css_variables(self, stylesheet):
            """إصلاح متغيرات CSS غير المدعومة"""
            # استبدال المتغيرات بالقيم الفعلية
            replacements = {
                'var(--bg-primary)': '#0f0f23',
                'var(--bg-secondary)': '#1a1a2e',
                'var(--bg-tertiary)': '#16213e',
                'var(--bg-surface)': '#1e2139',
                'var(--bg-elevated)': '#252545',
                'var(--text-primary)': '#e2e8f0',
                'var(--text-secondary)': '#a0aec0',
                'var(--text-muted)': '#718096',
                'var(--text-accent)': '#90cdf4',
                'var(--accent-primary)': '#667eea',
                'var(--accent-secondary)': '#764ba2',
                'var(--accent-success)': '#48bb78',
                'var(--accent-warning)': '#ed8936',
                'var(--accent-danger)': '#f56565',
                'var(--accent-info)': '#4299e1',
                'var(--border-primary)': '#2d3748',
                'var(--border-secondary)': '#4a5568',
                'var(--border-accent)': '#667eea',
                'var(--shadow-sm)': '0 1px 3px rgba(0, 0, 0, 0.4)',
                'var(--shadow-md)': '0 4px 6px rgba(0, 0, 0, 0.3)',
                'var(--shadow-lg)': '0 10px 15px rgba(0, 0, 0, 0.2)',
                'var(--shadow-xl)': '0 20px 25px rgba(0, 0, 0, 0.15)'
            }
            
            for var, value in replacements.items():
                stylesheet = stylesheet.replace(var, value)
            
            return stylesheet
        
        def setup_ui(self):
            """إعداد واجهة الاختبار"""
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            layout = QVBoxLayout(central_widget)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(20)
            
            # العنوان
            title = QLabel("🌙 اختبار الوضع الليلي المحسن")
            title.setObjectName("app_title")
            title.setAlignment(Qt.AlignCenter)
            layout.addWidget(title)
            
            # أزرار التحكم
            controls_layout = QHBoxLayout()
            
            toggle_btn = QPushButton("🔄 تبديل للوضع النهاري")
            toggle_btn.clicked.connect(self.toggle_theme)
            controls_layout.addWidget(toggle_btn)
            
            reload_btn = QPushButton("🔄 إعادة تحميل")
            reload_btn.setObjectName("success_button")
            reload_btn.clicked.connect(self.apply_dark_mode)
            controls_layout.addWidget(reload_btn)
            
            test_btn = QPushButton("✨ اختبار")
            test_btn.setObjectName("info_button")
            test_btn.clicked.connect(self.show_test_message)
            controls_layout.addWidget(test_btn)
            
            layout.addLayout(controls_layout)
            
            # مجموعة العناصر
            elements_group = QGroupBox("عناصر الاختبار")
            elements_layout = QVBoxLayout(elements_group)
            
            # أزرار مختلفة
            buttons_layout = QHBoxLayout()
            
            primary_btn = QPushButton("زر أساسي")
            success_btn = QPushButton("نجاح")
            success_btn.setObjectName("success_button")
            danger_btn = QPushButton("خطر")
            danger_btn.setObjectName("danger_button")
            
            buttons_layout.addWidget(primary_btn)
            buttons_layout.addWidget(success_btn)
            buttons_layout.addWidget(danger_btn)
            
            elements_layout.addLayout(buttons_layout)
            
            # حقول إدخال
            inputs_layout = QFormLayout()
            
            line_edit = QLineEdit("نص تجريبي")
            combo_box = QComboBox()
            combo_box.addItems(["خيار 1", "خيار 2", "خيار 3"])
            
            inputs_layout.addRow("حقل نص:", line_edit)
            inputs_layout.addRow("قائمة:", combo_box)
            
            elements_layout.addLayout(inputs_layout)
            
            # خانات اختيار
            checkbox = QCheckBox("خانة اختيار")
            checkbox.setChecked(True)
            radio = QRadioButton("زر راديو")
            radio.setChecked(True)
            
            elements_layout.addWidget(checkbox)
            elements_layout.addWidget(radio)
            
            layout.addWidget(elements_group)
            
            # جدول بسيط
            table = QTableWidget(3, 3)
            table.setHorizontalHeaderLabels(["العمود 1", "العمود 2", "العمود 3"])
            
            for row in range(3):
                for col in range(3):
                    table.setItem(row, col, QTableWidgetItem(f"خلية {row+1}-{col+1}"))
            
            table.resizeColumnsToContents()
            layout.addWidget(table)
            
            # شريط الحالة
            self.statusBar().showMessage("🌙 الوضع الليلي المحسن نشط")
        
        def toggle_theme(self):
            """تبديل للوضع النهاري"""
            try:
                styles_dir = Path("src/styles")
                light_file = styles_dir / "main.qss"
                
                if light_file.exists():
                    with open(light_file, 'r', encoding='utf-8') as f:
                        stylesheet = f.read()
                    
                    self.setStyleSheet(stylesheet)
                    self.statusBar().showMessage("☀️ الوضع النهاري نشط")
                    print("☀️ تم التبديل للوضع النهاري")
                    
            except Exception as e:
                print(f"❌ خطأ في التبديل: {e}")
        
        def show_test_message(self):
            """عرض رسالة اختبار"""
            QMessageBox.information(self, "✨ اختبار", 
                                   "تم اختبار الوضع الليلي المحسن بنجاح!\n"
                                   "جميع العناصر تعمل بشكل صحيح.")
    
    return SimpleTestWindow()


def main():
    """الدالة الرئيسية"""
    print("🌙 اختبار الوضع الليلي البسيط")
    print("=" * 40)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء نافذة الاختبار
        window = create_simple_test_window()
        window.show()
        
        print("✅ تم فتح نافذة الاختبار!")
        print("🎯 اختبر العناصر المختلفة والتبديل بين الأوضاع")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"💥 خطأ: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
