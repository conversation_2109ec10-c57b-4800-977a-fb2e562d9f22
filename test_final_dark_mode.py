#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نهائي للوضع الليلي النظيف والمتكامل
Final Clean Dark Mode Integration Test
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))


def setup_database():
    """إعداد قاعدة البيانات للاختبار"""
    try:
        from src.database.connection import get_db_manager
        from src.database.models import create_tables
        
        print("🗄️ إعداد قاعدة البيانات...")
        
        # إنشاء مدير قاعدة البيانات
        db_manager = get_db_manager()
        
        # تهيئة قاعدة البيانات
        db_manager.initialize_database()
        
        # إنشاء الجداول
        create_tables()
        
        print("✅ تم إعداد قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        print(f"⚠️ تحذير: لم يتم إعداد قاعدة البيانات: {e}")
        print("📝 ملاحظة: سيعمل النظام بدون بيانات حقيقية")
        return False


def test_clean_theme_manager():
    """اختبار مدير الثيمات النظيف"""
    print("🔧 اختبار مدير الثيمات النظيف...")
    
    try:
        from src.utils.theme_manager import ThemeManager
        
        theme_manager = ThemeManager()
        
        # اختبار الثيمات المتاحة
        themes = theme_manager.get_available_themes()
        print(f"✅ الثيمات المتاحة: {themes}")
        
        # اختبار تطبيق الوضع الليلي النظيف
        result = theme_manager.set_theme("dark")
        print(f"✅ تطبيق الوضع الليلي النظيف: {'نجح' if result else 'فشل'}")
        
        # اختبار التبديل
        new_theme = theme_manager.toggle_theme()
        print(f"✅ تبديل الثيم: {new_theme}")
        
        # العودة للوضع الليلي
        theme_manager.set_theme("dark")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير الثيمات: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_standalone_test_window():
    """إنشاء نافذة اختبار مستقلة"""
    
    class StandaloneTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("🌙 اختبار الوضع الليلي النظيف والمتكامل")
            self.setGeometry(200, 200, 1000, 700)
            
            # إنشاء مدير الثيمات
            from src.utils.theme_manager import ThemeManager
            self.theme_manager = ThemeManager()
            
            # تطبيق الوضع الليلي
            self.theme_manager.set_theme("dark")
            
            self.setup_ui()
            
        def setup_ui(self):
            """إعداد الواجهة"""
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            layout = QVBoxLayout(central_widget)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(20)
            
            # العنوان الرئيسي
            title = QLabel("🌙 اختبار الوضع الليلي النظيف والمتكامل")
            title.setObjectName("app_title")
            title.setAlignment(Qt.AlignCenter)
            layout.addWidget(title)
            
            # معلومات الثيم
            self.theme_info = QLabel()
            self.theme_info.setObjectName("window_title")
            self.theme_info.setAlignment(Qt.AlignCenter)
            self.update_theme_info()
            layout.addWidget(self.theme_info)
            
            # أزرار التحكم في الثيم
            controls_frame = self.create_theme_controls()
            layout.addWidget(controls_frame)
            
            # التبويبات
            tabs = self.create_test_tabs()
            layout.addWidget(tabs)
            
            # شريط الحالة
            self.statusBar().showMessage("🌙 الوضع الليلي النظيف نشط - جاهز للاختبار")
            
        def create_theme_controls(self):
            """إنشاء أزرار التحكم في الثيم"""
            group = QGroupBox("التحكم في الثيم")
            layout = QHBoxLayout(group)
            
            # زر الوضع النهاري
            light_btn = QPushButton("☀️ الوضع النهاري")
            light_btn.clicked.connect(lambda: self.set_theme("light"))
            layout.addWidget(light_btn)
            
            # زر الوضع الليلي
            dark_btn = QPushButton("🌙 الوضع الليلي")
            dark_btn.setObjectName("success_button")
            dark_btn.clicked.connect(lambda: self.set_theme("dark"))
            layout.addWidget(dark_btn)
            
            # زر التبديل السريع
            toggle_btn = QPushButton("🔄 تبديل سريع")
            toggle_btn.setObjectName("info_button")
            toggle_btn.clicked.connect(self.toggle_theme)
            layout.addWidget(toggle_btn)
            
            # زر إعادة التحميل
            reload_btn = QPushButton("🔄 إعادة تحميل")
            reload_btn.setObjectName("warning_button")
            reload_btn.clicked.connect(self.force_reload)
            layout.addWidget(reload_btn)
            
            # زر فتح النظام الرئيسي
            main_system_btn = QPushButton("🏗️ فتح النظام الرئيسي")
            main_system_btn.setObjectName("dashboard_action_button")
            main_system_btn.clicked.connect(self.open_main_system)
            layout.addWidget(main_system_btn)
            
            return group
            
        def create_test_tabs(self):
            """إنشاء تبويبات الاختبار"""
            tabs = QTabWidget()
            
            # تبويب العناصر الأساسية
            basic_tab = self.create_basic_elements_tab()
            tabs.addTab(basic_tab, "العناصر الأساسية")
            
            # تبويب الجداول
            tables_tab = self.create_tables_tab()
            tabs.addTab(tables_tab, "الجداول")
            
            # تبويب البطاقات
            cards_tab = self.create_cards_tab()
            tabs.addTab(cards_tab, "البطاقات الإحصائية")
            
            return tabs
            
        def create_basic_elements_tab(self):
            """إنشاء تبويب العناصر الأساسية"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # مجموعة الأزرار
            buttons_group = QGroupBox("أنواع الأزرار")
            buttons_layout = QHBoxLayout(buttons_group)
            
            buttons = [
                ("زر عادي", ""),
                ("زر نجاح", "success_button"),
                ("زر خطر", "danger_button"),
                ("زر تحذير", "warning_button"),
                ("زر معلومات", "info_button")
            ]
            
            for text, object_name in buttons:
                btn = QPushButton(text)
                if object_name:
                    btn.setObjectName(object_name)
                btn.clicked.connect(lambda checked, t=text: self.show_message(f"تم النقر على {t}"))
                buttons_layout.addWidget(btn)
            
            layout.addWidget(buttons_group)
            
            # مجموعة الحقول
            inputs_group = QGroupBox("حقول الإدخال")
            inputs_layout = QFormLayout(inputs_group)
            
            line_edit = QLineEdit("نص تجريبي")
            combo_box = QComboBox()
            combo_box.addItems(["خيار 1", "خيار 2", "خيار 3"])
            date_edit = QDateEdit(QDate.currentDate())
            text_edit = QTextEdit("نص متعدد الأسطر\nللاختبار")
            text_edit.setMaximumHeight(100)
            
            inputs_layout.addRow("حقل نص:", line_edit)
            inputs_layout.addRow("قائمة منسدلة:", combo_box)
            inputs_layout.addRow("تاريخ:", date_edit)
            inputs_layout.addRow("نص متعدد:", text_edit)
            
            layout.addWidget(inputs_group)
            
            # مجموعة خانات الاختيار
            checkboxes_group = QGroupBox("خانات الاختيار")
            checkboxes_layout = QVBoxLayout(checkboxes_group)
            
            checkbox1 = QCheckBox("خيار أول")
            checkbox1.setChecked(True)
            checkbox2 = QCheckBox("خيار ثاني")
            checkbox3 = QCheckBox("خيار ثالث معطل")
            checkbox3.setEnabled(False)
            
            radio1 = QRadioButton("راديو أول")
            radio1.setChecked(True)
            radio2 = QRadioButton("راديو ثاني")
            
            for widget in [checkbox1, checkbox2, checkbox3, radio1, radio2]:
                checkboxes_layout.addWidget(widget)
            
            layout.addWidget(checkboxes_group)
            layout.addStretch()
            
            return widget
            
        def create_tables_tab(self):
            """إنشاء تبويب الجداول"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # جدول تجريبي
            table = QTableWidget(5, 4)
            table.setHorizontalHeaderLabels(["الاسم", "القسم", "الراتب", "الحالة"])
            
            # بيانات تجريبية
            data = [
                ["أحمد محمد", "تقنية المعلومات", "5000", "نشط"],
                ["فاطمة علي", "الموارد البشرية", "4500", "نشط"],
                ["محمد أحمد", "المحاسبة", "4000", "معطل"],
                ["سارة محمود", "التسويق", "3500", "نشط"],
                ["علي حسن", "المبيعات", "3000", "نشط"]
            ]
            
            for row, row_data in enumerate(data):
                for col, value in enumerate(row_data):
                    table.setItem(row, col, QTableWidgetItem(value))
            
            # تنسيق الجدول
            table.resizeColumnsToContents()
            table.setAlternatingRowColors(True)
            table.setSelectionBehavior(QAbstractItemView.SelectRows)
            
            layout.addWidget(QLabel("جدول تجريبي:"))
            layout.addWidget(table)
            
            return widget
            
        def create_cards_tab(self):
            """إنشاء تبويب البطاقات الإحصائية"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # شبكة البطاقات
            cards_layout = QGridLayout()
            
            # بيانات البطاقات
            cards_data = [
                ("الموظفين", "150", "موظف", "employees_card"),
                ("الرواتب", "750,000", "ريال", "salaries_card"),
                ("السلف", "25,000", "ريال", "advances_card"),
                ("الديون", "15,000", "ريال", "debts_card"),
                ("المكافآت", "50,000", "ريال", "bonuses_card"),
                ("الأقسام", "8", "قسم", "departments_card")
            ]
            
            for i, (title, value, subtitle, object_name) in enumerate(cards_data):
                card = self.create_stat_card(title, value, subtitle, object_name)
                row = i // 3
                col = i % 3
                cards_layout.addWidget(card, row, col)
            
            layout.addLayout(cards_layout)
            layout.addStretch()
            
            return widget
            
        def create_stat_card(self, title, value, subtitle, object_name):
            """إنشاء بطاقة إحصائية"""
            card = QFrame()
            card.setObjectName(object_name)
            card.setFixedSize(250, 150)
            
            layout = QVBoxLayout(card)
            layout.setAlignment(Qt.AlignCenter)
            
            title_label = QLabel(title)
            title_label.setObjectName("card_title")
            title_label.setAlignment(Qt.AlignCenter)
            
            value_label = QLabel(value)
            value_label.setObjectName("card_value")
            value_label.setAlignment(Qt.AlignCenter)
            
            subtitle_label = QLabel(subtitle)
            subtitle_label.setObjectName("card_subtitle")
            subtitle_label.setAlignment(Qt.AlignCenter)
            
            layout.addWidget(title_label)
            layout.addWidget(value_label)
            layout.addWidget(subtitle_label)
            
            return card
            
        def set_theme(self, theme_name):
            """تعيين ثيم"""
            success = self.theme_manager.set_theme(theme_name)
            if success:
                self.update_theme_info()
                theme_display = "الوضع الليلي" if theme_name == "dark" else "الوضع النهاري"
                self.statusBar().showMessage(f"✅ تم تطبيق {theme_display}")
                print(f"✅ تم تطبيق {theme_display}")
            else:
                self.statusBar().showMessage(f"❌ فشل في تطبيق الثيم: {theme_name}")
                print(f"❌ فشل في تطبيق الثيم: {theme_name}")
        
        def toggle_theme(self):
            """تبديل الثيم"""
            new_theme = self.theme_manager.toggle_theme()
            self.update_theme_info()
            theme_display = "الوضع الليلي" if new_theme == "dark" else "الوضع النهاري"
            self.statusBar().showMessage(f"🔄 تم التبديل إلى {theme_display}")
            print(f"🔄 تم التبديل إلى {theme_display}")
        
        def force_reload(self):
            """إعادة تحميل الثيم بقوة"""
            success = self.theme_manager.force_reload_theme()
            if success:
                self.update_theme_info()
                self.statusBar().showMessage("🔄 تم إعادة تحميل الثيم بنجاح")
                print("🔄 تم إعادة تحميل الثيم بنجاح")
            else:
                self.statusBar().showMessage("❌ فشل في إعادة تحميل الثيم")
                print("❌ فشل في إعادة تحميل الثيم")
        
        def open_main_system(self):
            """فتح النظام الرئيسي"""
            try:
                from src.views.main_window import MainWindow
                
                # إنشاء النافذة الرئيسية
                self.main_window = MainWindow()
                
                # تطبيق الثيم الحالي على النافذة الرئيسية
                current_theme = self.theme_manager.get_current_theme()
                self.main_window.set_theme(current_theme)
                
                self.main_window.show()
                
                self.statusBar().showMessage("🏗️ تم فتح النظام الرئيسي")
                print("🏗️ تم فتح النظام الرئيسي مع الوضع الليلي")
                
            except Exception as e:
                self.show_message(f"❌ خطأ في فتح النظام الرئيسي: {e}")
                print(f"❌ خطأ في فتح النظام الرئيسي: {e}")
        
        def update_theme_info(self):
            """تحديث معلومات الثيم"""
            current_theme = self.theme_manager.get_current_theme()
            if current_theme == "dark":
                self.theme_info.setText("الثيم الحالي: 🌙 الوضع الليلي النظيف")
            else:
                self.theme_info.setText("الثيم الحالي: ☀️ الوضع النهاري")
        
        def show_message(self, message):
            """عرض رسالة"""
            QMessageBox.information(self, "رسالة", message)
    
    return StandaloneTestWindow()


def main():
    """الدالة الرئيسية"""
    print("🌙 اختبار الوضع الليلي النظيف والمتكامل")
    print("=" * 60)
    
    # إعداد قاعدة البيانات (اختياري)
    setup_database()
    
    # اختبار مدير الثيمات
    if not test_clean_theme_manager():
        print("💥 فشل في اختبار مدير الثيمات")
        return 1
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء نافذة الاختبار
        window = create_standalone_test_window()
        window.show()
        
        print("✅ تم فتح نافذة الاختبار النهائية!")
        print("\n🎯 يمكنك الآن:")
        print("1. اختبار جميع العناصر في الوضع الليلي النظيف")
        print("2. تبديل بين الأوضاع بسلاسة")
        print("3. فتح النظام الرئيسي من داخل نافذة الاختبار")
        print("4. اختبار البطاقات الإحصائية والجداول")
        print("5. التأكد من عدم وجود أخطاء CSS")
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"💥 خطأ في تشغيل الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
