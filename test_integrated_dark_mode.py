#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التكامل الكامل للوضع الليلي مع النظام الرئيسي
Integrated Dark Mode System Test
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))


def test_theme_manager():
    """اختبار مدير الثيمات"""
    print("🔧 اختبار مدير الثيمات...")
    
    try:
        from src.utils.theme_manager import ThemeManager
        
        theme_manager = ThemeManager()
        
        # اختبار الثيمات المتاحة
        themes = theme_manager.get_available_themes()
        print(f"✅ الثيمات المتاحة: {themes}")
        
        # اختبار تطبيق الوضع الليلي
        result = theme_manager.set_theme("dark")
        print(f"✅ تطبيق الوضع الليلي: {'نجح' if result else 'فشل'}")
        
        # اختبار تطبيق الوضع النهاري
        result = theme_manager.set_theme("light")
        print(f"✅ تطبيق الوضع النهاري: {'نجح' if result else 'فشل'}")
        
        # اختبار التبديل
        new_theme = theme_manager.toggle_theme()
        print(f"✅ تبديل الثيم: {new_theme}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير الثيمات: {e}")
        return False


def create_integrated_test_window():
    """إنشاء نافذة اختبار متكاملة"""
    
    class IntegratedTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("🌙 اختبار التكامل الكامل للوضع الليلي")
            self.setGeometry(150, 150, 1000, 700)
            
            # إنشاء مدير الثيمات
            from src.utils.theme_manager import ThemeManager
            self.theme_manager = ThemeManager()
            
            # تطبيق الثيم الحالي
            self.theme_manager.apply_current_theme()
            
            # إعداد الواجهة
            self.setup_ui()
            
            # ربط الإشارات
            self.theme_manager.theme_changed.connect(self.on_theme_changed)
            
        def setup_ui(self):
            """إعداد واجهة الاختبار"""
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            layout = QVBoxLayout(central_widget)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(20)
            
            # العنوان الرئيسي
            title = QLabel("🌙 اختبار التكامل الكامل للوضع الليلي")
            title.setObjectName("app_title")
            title.setAlignment(Qt.AlignCenter)
            layout.addWidget(title)
            
            # معلومات الثيم الحالي
            self.theme_info = QLabel()
            self.theme_info.setObjectName("window_title")
            self.theme_info.setAlignment(Qt.AlignCenter)
            self.update_theme_info()
            layout.addWidget(self.theme_info)
            
            # أزرار التحكم في الثيم
            theme_controls = self.create_theme_controls()
            layout.addWidget(theme_controls)
            
            # التبويبات
            tabs = self.create_test_tabs()
            layout.addWidget(tabs)
            
            # شريط الحالة
            self.create_status_bar()
        
        def create_theme_controls(self):
            """إنشاء أزرار التحكم في الثيم"""
            group = QGroupBox("التحكم في الثيم")
            layout = QHBoxLayout(group)
            
            # زر الوضع النهاري
            light_btn = QPushButton("☀️ الوضع النهاري")
            light_btn.clicked.connect(lambda: self.set_theme("light"))
            layout.addWidget(light_btn)
            
            # زر الوضع الليلي
            dark_btn = QPushButton("🌙 الوضع الليلي")
            dark_btn.setObjectName("success_button")
            dark_btn.clicked.connect(lambda: self.set_theme("dark"))
            layout.addWidget(dark_btn)
            
            # زر التبديل السريع
            toggle_btn = QPushButton("🔄 تبديل سريع")
            toggle_btn.setObjectName("info_button")
            toggle_btn.clicked.connect(self.toggle_theme)
            layout.addWidget(toggle_btn)
            
            # زر إعادة التحميل
            reload_btn = QPushButton("🔄 إعادة تحميل")
            reload_btn.setObjectName("warning_button")
            reload_btn.clicked.connect(self.reload_theme)
            layout.addWidget(reload_btn)
            
            return group
        
        def create_test_tabs(self):
            """إنشاء تبويبات الاختبار"""
            tabs = QTabWidget()
            
            # تبويب العناصر الأساسية
            basic_tab = self.create_basic_elements_tab()
            tabs.addTab(basic_tab, "العناصر الأساسية")
            
            # تبويب الجداول والقوائم
            tables_tab = self.create_tables_tab()
            tabs.addTab(tables_tab, "الجداول والقوائم")
            
            # تبويب البطاقات
            cards_tab = self.create_cards_tab()
            tabs.addTab(cards_tab, "البطاقات الإحصائية")
            
            return tabs
        
        def create_basic_elements_tab(self):
            """إنشاء تبويب العناصر الأساسية"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # مجموعة الأزرار
            buttons_group = QGroupBox("أنواع الأزرار")
            buttons_layout = QHBoxLayout(buttons_group)
            
            buttons = [
                ("زر عادي", ""),
                ("زر نجاح", "success_button"),
                ("زر خطر", "danger_button"),
                ("زر تحذير", "warning_button"),
                ("زر معلومات", "info_button")
            ]
            
            for text, object_name in buttons:
                btn = QPushButton(text)
                if object_name:
                    btn.setObjectName(object_name)
                buttons_layout.addWidget(btn)
            
            layout.addWidget(buttons_group)
            
            # مجموعة الحقول
            inputs_group = QGroupBox("حقول الإدخال")
            inputs_layout = QFormLayout(inputs_group)
            
            line_edit = QLineEdit("نص تجريبي")
            combo_box = QComboBox()
            combo_box.addItems(["خيار 1", "خيار 2", "خيار 3"])
            date_edit = QDateEdit(QDate.currentDate())
            
            inputs_layout.addRow("حقل نص:", line_edit)
            inputs_layout.addRow("قائمة منسدلة:", combo_box)
            inputs_layout.addRow("تاريخ:", date_edit)
            
            layout.addWidget(inputs_group)
            
            # مجموعة خانات الاختيار
            checkboxes_group = QGroupBox("خانات الاختيار")
            checkboxes_layout = QVBoxLayout(checkboxes_group)
            
            checkbox1 = QCheckBox("خيار أول")
            checkbox1.setChecked(True)
            checkbox2 = QCheckBox("خيار ثاني")
            radio1 = QRadioButton("راديو أول")
            radio1.setChecked(True)
            radio2 = QRadioButton("راديو ثاني")
            
            for widget in [checkbox1, checkbox2, radio1, radio2]:
                checkboxes_layout.addWidget(widget)
            
            layout.addWidget(checkboxes_group)
            layout.addStretch()
            
            return widget
        
        def create_tables_tab(self):
            """إنشاء تبويب الجداول"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # جدول تجريبي
            table = QTableWidget(4, 3)
            table.setHorizontalHeaderLabels(["الاسم", "القسم", "الراتب"])
            
            data = [
                ["أحمد محمد", "تقنية المعلومات", "5000"],
                ["فاطمة علي", "الموارد البشرية", "4500"],
                ["محمد أحمد", "المحاسبة", "4000"],
                ["سارة محمود", "التسويق", "3500"]
            ]
            
            for row, row_data in enumerate(data):
                for col, value in enumerate(row_data):
                    table.setItem(row, col, QTableWidgetItem(value))
            
            table.resizeColumnsToContents()
            table.setAlternatingRowColors(True)
            
            layout.addWidget(QLabel("جدول تجريبي:"))
            layout.addWidget(table)
            
            return widget
        
        def create_cards_tab(self):
            """إنشاء تبويب البطاقات"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            cards_layout = QGridLayout()
            
            cards_data = [
                ("الموظفين", "150", "موظف", "employees_card"),
                ("الرواتب", "750,000", "ريال", "salaries_card"),
                ("السلف", "25,000", "ريال", "advances_card"),
                ("الديون", "15,000", "ريال", "debts_card")
            ]
            
            for i, (title, value, subtitle, object_name) in enumerate(cards_data):
                card = self.create_stat_card(title, value, subtitle, object_name)
                row = i // 2
                col = i % 2
                cards_layout.addWidget(card, row, col)
            
            layout.addLayout(cards_layout)
            layout.addStretch()
            
            return widget
        
        def create_stat_card(self, title, value, subtitle, object_name):
            """إنشاء بطاقة إحصائية"""
            card = QFrame()
            card.setObjectName(object_name)
            card.setFixedSize(250, 150)
            
            layout = QVBoxLayout(card)
            layout.setAlignment(Qt.AlignCenter)
            
            title_label = QLabel(title)
            title_label.setObjectName("card_title")
            title_label.setAlignment(Qt.AlignCenter)
            
            value_label = QLabel(value)
            value_label.setObjectName("card_value")
            value_label.setAlignment(Qt.AlignCenter)
            
            subtitle_label = QLabel(subtitle)
            subtitle_label.setObjectName("card_subtitle")
            subtitle_label.setAlignment(Qt.AlignCenter)
            
            layout.addWidget(title_label)
            layout.addWidget(value_label)
            layout.addWidget(subtitle_label)
            
            return card
        
        def create_status_bar(self):
            """إنشاء شريط الحالة"""
            status_bar = self.statusBar()
            status_bar.showMessage("🌙 نظام الوضع الليلي المتكامل جاهز للاختبار")
        
        def set_theme(self, theme_name):
            """تعيين ثيم محدد"""
            success = self.theme_manager.set_theme(theme_name)
            if success:
                print(f"✅ تم تطبيق الثيم: {theme_name}")
            else:
                print(f"❌ فشل في تطبيق الثيم: {theme_name}")
        
        def toggle_theme(self):
            """تبديل الثيم"""
            new_theme = self.theme_manager.toggle_theme()
            print(f"🔄 تم التبديل إلى: {new_theme}")
        
        def reload_theme(self):
            """إعادة تحميل الثيم الحالي"""
            current_theme = self.theme_manager.get_current_theme()
            self.theme_manager.apply_current_theme()
            print(f"🔄 تم إعادة تحميل الثيم: {current_theme}")
        
        def on_theme_changed(self, theme_name):
            """معالج تغيير الثيم"""
            self.update_theme_info()
            theme_display = "الوضع الليلي" if theme_name == "dark" else "الوضع النهاري"
            self.statusBar().showMessage(f"✅ تم تطبيق {theme_display}")
        
        def update_theme_info(self):
            """تحديث معلومات الثيم"""
            current_theme = self.theme_manager.get_current_theme()
            theme_display = "الوضع الليلي 🌙" if current_theme == "dark" else "الوضع النهاري ☀️"
            self.theme_info.setText(f"الثيم الحالي: {theme_display}")
    
    return IntegratedTestWindow()


def main():
    """الدالة الرئيسية"""
    print("🌙 اختبار التكامل الكامل للوضع الليلي")
    print("=" * 60)
    
    # اختبار مدير الثيمات أولاً
    if not test_theme_manager():
        print("💥 فشل في اختبار مدير الثيمات")
        return 1
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء نافذة الاختبار
        window = create_integrated_test_window()
        window.show()
        
        print("✅ تم فتح نافذة الاختبار المتكاملة!")
        print("\n🎯 يمكنك الآن:")
        print("1. اختبار تبديل الثيمات")
        print("2. مراجعة جميع العناصر في كلا الوضعين")
        print("3. اختبار التكامل مع النظام")
        print("4. التأكد من حفظ الإعدادات")
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"💥 خطأ في تشغيل الاختبار: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
