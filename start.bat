@echo off
chcp 65001 >nul
title نظام إدارة شؤون الموظفين

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة شؤون الموظفين                    ║
echo ║                   HR Management System                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص المتطلبات...

REM فحص Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير متوفر في PATH
    echo 💡 يرجى تثبيت Python 3.8+ من https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM فحص PySide6
python -c "import PySide6" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PySide6 غير مثبت
    echo 💡 جاري تثبيت PySide6...
    pip install PySide6
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت PySide6
        pause
        exit /b 1
    )
)

echo ✅ PySide6 متوفر

echo.
echo 🚀 تشغيل نظام إدارة شؤون الموظفين...
echo Starting HR Management System...

python run_hr_system.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo Error occurred while running the system
    echo.
    echo � تأكد من:
    echo - تثبيت جميع المتطلبات: pip install -r requirements.txt
    echo - وجود قاعدة البيانات أو إعداد SQLite
    echo.
)

echo.
echo ✅ انتهى التشغيل
pause

