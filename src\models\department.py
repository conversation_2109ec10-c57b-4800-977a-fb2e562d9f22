"""
نموذج الأقسام
Department model
"""

from sqlalchemy import Column, String, Text
from sqlalchemy.orm import relationship
from .base import BaseModel


class Department(BaseModel):
    """نموذج الأقسام"""
    
    __tablename__ = "departments"
    
    name = Column(String(100), nullable=False, unique=True, comment="اسم القسم")
    description = Column(Text, nullable=True, comment="وصف القسم")
    code = Column(String(10), nullable=False, unique=True, comment="رمز القسم")
    
    # العلاقات
    employees = relationship("Employee", back_populates="department", lazy="dynamic")
    
    def __repr__(self) -> str:
        return f"<Department(id={self.id}, name='{self.name}', code='{self.code}')>"
    
    def __str__(self) -> str:
        return self.name
    
    @property
    def employee_count(self) -> int:
        """عدد الموظفين في القسم"""
        return self.employees.filter_by(is_active=True).count()
    
    def get_active_employees(self):
        """الحصول على الموظفين النشطين في القسم"""
        return self.employees.filter_by(is_active=True).all()
