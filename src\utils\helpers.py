"""
دوال مساعدة عامة
General Helper Functions
"""

import re
from datetime import datetime, date
from typing import Any, Optional, Union
from decimal import Decimal


def format_currency(amount: Union[int, float, Decimal], currency: str = "د.ع") -> str:
    """تنسيق المبلغ مع فواصل الآلاف والعملة العراقية"""
    if amount is None:
        return f"0 {currency}"

    # تحويل إلى رقم
    if isinstance(amount, str):
        try:
            amount = float(amount)
        except ValueError:
            return f"0 {currency}"

    # تنسيق الرقم مع فواصل الآلاف (استخدام النقطة كفاصل آلاف للعملة العراقية)
    formatted = f"{amount:,.0f}"
    # استبدال الفاصلة بالنقطة للتنسيق العراقي
    formatted = formatted.replace(",", ".")
    return f"{formatted} {currency}"


def format_date(date_obj: Union[date, datetime, str], format_str: str = "%Y-%m-%d") -> str:
    """تنسيق التاريخ"""
    if date_obj is None:
        return ""
    
    if isinstance(date_obj, str):
        try:
            date_obj = datetime.strptime(date_obj, "%Y-%m-%d").date()
        except ValueError:
            return date_obj
    
    if isinstance(date_obj, datetime):
        date_obj = date_obj.date()
    
    return date_obj.strftime(format_str)


def format_datetime(datetime_obj: Union[datetime, str], format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """تنسيق التاريخ والوقت"""
    if datetime_obj is None:
        return ""
    
    if isinstance(datetime_obj, str):
        try:
            datetime_obj = datetime.fromisoformat(datetime_obj)
        except ValueError:
            return datetime_obj
    
    return datetime_obj.strftime(format_str)


def validate_phone(phone: str) -> bool:
    """التحقق من صحة رقم الهاتف العراقي"""
    if not phone:
        return True  # اختياري
    
    # نمط رقم الهاتف العراقي
    pattern = r'^(07[3-9]\d{8}|964[0-9]\d{8})$'
    return bool(re.match(pattern, phone.replace(" ", "").replace("-", "")))


def validate_employee_number(employee_number: str) -> bool:
    """التحقق من صحة الرقم الوظيفي"""
    if not employee_number:
        return False
    
    # يجب أن يكون بين 3-20 حرف ويحتوي على أرقام وحروف فقط
    pattern = r'^[A-Za-z0-9]{3,20}$'
    return bool(re.match(pattern, employee_number))


def validate_email(email: str) -> bool:
    """التحقق من صحة البريد الإلكتروني"""
    if not email:
        return True  # اختياري
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def clean_numeric_input(value: str) -> Optional[float]:
    """تنظيف المدخلات الرقمية"""
    if not value:
        return None

    # إزالة الفواصل والنقاط (فواصل الآلاف) والمسافات والعملة
    cleaned = value.replace(",", "").replace(".", "").replace(" ", "").replace("د.ع", "")

    # إذا كان هناك نقطة عشرية، نحتفظ بها
    if "." in value and value.count(".") == 1:
        # التحقق من أن النقطة هي للكسور العشرية وليس فاصل آلاف
        parts = value.split(".")
        if len(parts[1]) <= 2:  # كسور عشرية (سنتات)
            cleaned = value.replace(",", "").replace(" ", "").replace("د.ع", "")

    try:
        return float(cleaned)
    except ValueError:
        return None


def generate_employee_number(last_number: str = None) -> str:
    """توليد رقم وظيفي جديد"""
    if last_number:
        # استخراج الرقم من آخر رقم وظيفي
        match = re.search(r'(\d+)$', last_number)
        if match:
            number = int(match.group(1)) + 1
            prefix = last_number[:match.start()]
            return f"{prefix}{number:03d}"
    
    # رقم افتراضي
    return f"EMP{datetime.now().year}{datetime.now().month:02d}001"


def calculate_age(birth_date: date) -> int:
    """حساب العمر"""
    if not birth_date:
        return 0
    
    today = date.today()
    return today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))


def calculate_years_of_service(hire_date: date) -> int:
    """حساب سنوات الخدمة"""
    if not hire_date:
        return 0
    
    today = date.today()
    return today.year - hire_date.year - ((today.month, today.day) < (hire_date.month, hire_date.day))


def get_month_name(month: int) -> str:
    """الحصول على اسم الشهر بالعربية"""
    months = [
        "", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
        "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
    ]
    
    if 1 <= month <= 12:
        return months[month]
    return ""


def get_employment_status_display(status: str) -> str:
    """الحصول على عرض حالة التوظيف"""
    status_map = {
        "ACTIVE": "نشط",
        "TERMINATED": "مفصول",
        "SUSPENDED": "موقوف",
        "RESIGNED": "مستقيل"
    }
    return status_map.get(status, status)


def get_transaction_type_display(transaction_type: str) -> str:
    """الحصول على عرض نوع المعاملة"""
    type_map = {
        "ADVANCE": "سلفة",
        "DEDUCTION": "خصم",
        "BONUS": "مكافأة",
        "MARKET_DEBT": "دين الماركت"
    }
    return type_map.get(transaction_type, transaction_type)


def get_payment_status_display(status: str) -> str:
    """الحصول على عرض حالة الدفع"""
    status_map = {
        "PENDING": "معلق",
        "PAID": "مدفوع",
        "PARTIALLY_PAID": "مدفوع جزئياً",
        "CANCELLED": "ملغي"
    }
    return status_map.get(status, status)


def safe_divide(numerator: Union[int, float], denominator: Union[int, float]) -> float:
    """قسمة آمنة تتجنب القسمة على صفر"""
    try:
        if denominator == 0:
            return 0.0
        return float(numerator) / float(denominator)
    except (TypeError, ValueError):
        return 0.0


def truncate_text(text: str, max_length: int = 50) -> str:
    """اقتطاع النص مع إضافة نقاط"""
    if not text:
        return ""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length-3] + "..."
