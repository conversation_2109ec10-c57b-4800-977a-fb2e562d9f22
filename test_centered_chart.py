#!/usr/bin/env python3
"""
اختبار الرسم البياني المتوسط والمحسن
Test Centered and Enhanced Chart
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout, QPushButton, QLabel
from PySide6.QtCore import Qt


def create_centered_chart_test():
    """إنشاء نافذة اختبار الرسم البياني المتوسط"""
    
    class CenteredChartTest(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار الرسم البياني المتوسط والمحسن")
            self.setGeometry(50, 50, 1200, 800)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(30, 30, 30, 30)
            main_layout.setSpacing(25)
            
            # عنوان الاختبار
            test_title = QLabel("🎯 اختبار الرسم البياني المتوسط والمحسن")
            test_title.setAlignment(Qt.AlignCenter)
            test_title.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 15px;
                    padding: 20px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 #3498db, stop: 1 #2980b9);
                    color: white;
                    border-radius: 12px;
                }
            """)
            main_layout.addWidget(test_title)
            
            # إطار الرسم البياني
            chart_frame = QWidget()
            chart_frame.setStyleSheet("""
                QWidget {
                    background-color: white;
                    border: 3px solid #bdc3c7;
                    border-radius: 15px;
                    margin: 10px;
                    padding: 20px;
                }
            """)
            
            chart_layout = QVBoxLayout(chart_frame)
            chart_layout.setContentsMargins(40, 30, 40, 30)
            chart_layout.setSpacing(20)
            
            # عنوان القسم المحسن
            section_title = QLabel("👥 توزيع الموظفين على الأقسام")
            section_title.setAlignment(Qt.AlignCenter)
            section_title.setStyleSheet("""
                QLabel {
                    font-size: 20px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 12px 0;
                    padding: 15px;
                    letter-spacing: 1.5px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                              stop: 0 rgba(52, 152, 219, 0.1),
                                              stop: 1 rgba(155, 89, 182, 0.1));
                    border-radius: 10px;
                    border: 2px solid rgba(52, 152, 219, 0.3);
                    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
                }
            """)
            chart_layout.addWidget(section_title)
            
            # حاوية توسيط الرسم البياني
            chart_container = QHBoxLayout()
            chart_container.addStretch(1)  # مساحة مرنة يسار
            
            # إنشاء الرسم البياني
            from src.widgets.enhanced_dashboard import DepartmentDistributionChart
            self.dept_chart = DepartmentDistributionChart()
            chart_container.addWidget(self.dept_chart)
            
            chart_container.addStretch(1)  # مساحة مرنة يمين
            
            chart_layout.addLayout(chart_container)
            
            # وصف تحت الرسم البياني
            description = QLabel("📈 عرض تفصيلي لتوزيع الموظفين النشطين على جميع الأقسام مع النسب المئوية والإحصائيات")
            description.setAlignment(Qt.AlignCenter)
            description.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #5d6d7e;
                    font-style: italic;
                    font-weight: 500;
                    margin-top: 15px;
                    padding: 12px;
                    background-color: rgba(248, 249, 250, 0.9);
                    border-radius: 8px;
                    border: 1px solid rgba(189, 195, 199, 0.4);
                }
            """)
            chart_layout.addWidget(description)
            
            main_layout.addWidget(chart_frame)
            
            # أزرار التحكم
            controls_frame = QWidget()
            controls_layout = QHBoxLayout(controls_frame)
            controls_layout.setSpacing(15)
            
            # زر البيانات الكبيرة
            large_data_btn = QPushButton("📊 بيانات كبيرة (8 أقسام)")
            large_data_btn.setStyleSheet(self.get_button_style("#3498db"))
            large_data_btn.clicked.connect(self.load_large_data)
            
            # زر البيانات المتوسطة
            medium_data_btn = QPushButton("📈 بيانات متوسطة (5 أقسام)")
            medium_data_btn.setStyleSheet(self.get_button_style("#27ae60"))
            medium_data_btn.clicked.connect(self.load_medium_data)
            
            # زر البيانات الصغيرة
            small_data_btn = QPushButton("📉 بيانات صغيرة (3 أقسام)")
            small_data_btn.setStyleSheet(self.get_button_style("#f39c12"))
            small_data_btn.clicked.connect(self.load_small_data)
            
            # زر مسح البيانات
            clear_btn = QPushButton("🗑️ مسح البيانات")
            clear_btn.setStyleSheet(self.get_button_style("#e74c3c"))
            clear_btn.clicked.connect(self.clear_data)
            
            controls_layout.addWidget(large_data_btn)
            controls_layout.addWidget(medium_data_btn)
            controls_layout.addWidget(small_data_btn)
            controls_layout.addWidget(clear_btn)
            controls_layout.addStretch()
            
            main_layout.addWidget(controls_frame)
            
            # معلومات التحسينات
            info_label = QLabel("""
            ✅ التحسينات المطبقة:
            • عنوان محدد: "👥 توزيع الموظفين على الأقسام"
            • توسيط الرسم البياني في الصفحة
            • حجم أكبر (600×400 بكسل)
            • تباعد محسن بين الأعمدة (15 بكسل)
            • خطوط أكبر وأوضح (14px للقيم، 11px للأسماء)
            • هوامش محسنة (50 بكسل من كل جانب)
            • خط تحت العنوان للتمييز
            • وصف توضيحي محسن تحت الرسم البياني
            • إطار محسن مع ظلال وحدود ملونة
            • تنسيق أكثر احترافية ووضوحاً
            """)
            info_label.setAlignment(Qt.AlignLeft)
            info_label.setStyleSheet("""
                QLabel {
                    font-size: 13px;
                    color: #2c3e50;
                    background-color: #ecf0f1;
                    padding: 20px;
                    border-radius: 10px;
                    border-left: 5px solid #3498db;
                    line-height: 1.6;
                }
            """)
            main_layout.addWidget(info_label)
            
            # تحميل البيانات الافتراضية
            self.load_medium_data()
            
        def get_button_style(self, color):
            """الحصول على نمط الزر"""
            return f"""
                QPushButton {{
                    font-size: 14px;
                    font-weight: bold;
                    padding: 12px 20px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 {color}, stop: 1 {self.darken_color(color)});
                    color: white;
                    border: none;
                    border-radius: 8px;
                    min-width: 180px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                              stop: 0 {self.lighten_color(color)}, stop: 1 {color});
                }}
                QPushButton:pressed {{
                    background: {self.darken_color(color, 20)};
                }}
            """
            
        def darken_color(self, color, amount=15):
            """تغميق اللون"""
            color = color.lstrip('#')
            r = max(0, int(color[0:2], 16) - amount)
            g = max(0, int(color[2:4], 16) - amount)
            b = max(0, int(color[4:6], 16) - amount)
            return f"#{r:02x}{g:02x}{b:02x}"
            
        def lighten_color(self, color, amount=20):
            """تفتيح اللون"""
            color = color.lstrip('#')
            r = min(255, int(color[0:2], 16) + amount)
            g = min(255, int(color[2:4], 16) + amount)
            b = min(255, int(color[4:6], 16) + amount)
            return f"#{r:02x}{g:02x}{b:02x}"
            
        def load_large_data(self):
            """تحميل بيانات كبيرة"""
            data = [
                ("الإدارة العامة", 15),
                ("المحاسبة", 12),
                ("الموارد البشرية", 8),
                ("التسويق", 18),
                ("المبيعات", 25),
                ("تقنية المعلومات", 10),
                ("خدمة العملاء", 14),
                ("الإنتاج", 22)
            ]
            self.dept_chart.update_data(data)
            print("📊 تم تحميل البيانات الكبيرة (8 أقسام)")
            
        def load_medium_data(self):
            """تحميل بيانات متوسطة"""
            data = [
                ("الإدارة العامة", 12),
                ("المحاسبة", 8),
                ("التسويق", 15),
                ("المبيعات", 20),
                ("تقنية المعلومات", 7)
            ]
            self.dept_chart.update_data(data)
            print("📈 تم تحميل البيانات المتوسطة (5 أقسام)")
            
        def load_small_data(self):
            """تحميل بيانات صغيرة"""
            data = [
                ("الإدارة", 10),
                ("المبيعات", 18),
                ("التقنية", 6)
            ]
            self.dept_chart.update_data(data)
            print("📉 تم تحميل البيانات الصغيرة (3 أقسام)")
            
        def clear_data(self):
            """مسح البيانات"""
            self.dept_chart.update_data([])
            print("🗑️ تم مسح البيانات")
    
    return CenteredChartTest()


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الرسم البياني المتوسط والمحسن")
    print("=" * 60)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            enhanced_style_file = styles_dir / "enhanced_dashboard.qss"
            
            combined_styles = ""
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read() + "\n"
                    
            if enhanced_style_file.exists():
                with open(enhanced_style_file, 'r', encoding='utf-8') as f:
                    combined_styles += f.read()
                    
            app.setStyleSheet(combined_styles)
            print("✅ تم تحميل الأنماط")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الأنماط: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_centered_chart_test()
        window.show()
        
        print("✅ تم فتح نافذة اختبار الرسم البياني المتوسط!")
        print("\n🎯 التحسينات المطبقة:")
        print("• توسيط الرسم البياني في الصفحة")
        print("• حجم أكبر (600×400 بكسل)")
        print("• تباعد محسن بين الأعمدة")
        print("• خطوط أكبر وأوضح")
        print("• هوامش محسنة")
        print("• خط تحت العنوان")
        print("• وصف توضيحي")
        print("• إطار محسن مع ظلال")
        
        print("\n🎮 أزرار التحكم:")
        print("• بيانات كبيرة: 8 أقسام")
        print("• بيانات متوسطة: 5 أقسام")
        print("• بيانات صغيرة: 3 أقسام")
        print("• مسح البيانات: عرض رسالة فارغة")
        
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
