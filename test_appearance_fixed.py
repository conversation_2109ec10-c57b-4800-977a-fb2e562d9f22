#!/usr/bin/env python3
"""
اختبار نظام المظهر المصلح
Test Fixed Appearance System
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget
from PySide6.QtWidgets import QPushButton, QLabel, QLineEdit, QComboBox, QTableWidget
from PySide6.QtWidgets import QGroupBox, QCheckBox, QTableWidgetItem
from PySide6.QtCore import Qt


def create_simple_test_window():
    """إنشاء نافذة اختبار مبسطة"""
    
    class SimpleTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار نظام المظهر المصلح")
            self.setGeometry(100, 100, 800, 600)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            
            # العنوان
            title = QLabel("🎨 اختبار نظام المظهر المصلح")
            title.setObjectName("app_title")
            title.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(title)
            
            # شريط التحكم
            control_panel = self.create_control_panel()
            main_layout.addWidget(control_panel)
            
            # مجموعة الأزرار
            buttons_group = QGroupBox("الأزرار")
            buttons_layout = QHBoxLayout(buttons_group)
            
            primary_btn = QPushButton("زر أساسي")
            secondary_btn = QPushButton("زر ثانوي")
            secondary_btn.setObjectName("sidebar_button")
            disabled_btn = QPushButton("زر معطل")
            disabled_btn.setEnabled(False)
            
            buttons_layout.addWidget(primary_btn)
            buttons_layout.addWidget(secondary_btn)
            buttons_layout.addWidget(disabled_btn)
            main_layout.addWidget(buttons_group)
            
            # مجموعة الحقول
            inputs_group = QGroupBox("حقول الإدخال")
            inputs_layout = QVBoxLayout(inputs_group)
            
            line_edit = QLineEdit("نص تجريبي")
            combo_box = QComboBox()
            combo_box.addItems(["خيار 1", "خيار 2", "خيار 3"])
            
            inputs_layout.addWidget(QLabel("حقل نص:"))
            inputs_layout.addWidget(line_edit)
            inputs_layout.addWidget(QLabel("قائمة منسدلة:"))
            inputs_layout.addWidget(combo_box)
            main_layout.addWidget(inputs_group)
            
            # جدول بسيط
            table_group = QGroupBox("جدول البيانات")
            table_layout = QVBoxLayout(table_group)
            
            table = QTableWidget(3, 3)
            table.setHorizontalHeaderLabels(["العمود 1", "العمود 2", "العمود 3"])
            
            # ملء الجدول
            for row in range(3):
                for col in range(3):
                    item = QTableWidgetItem(f"خلية {row+1}-{col+1}")
                    table.setItem(row, col, item)
            
            table.setMaximumHeight(150)
            table_layout.addWidget(table)
            main_layout.addWidget(table_group)
            
            # مربعات اختيار
            checkbox_group = QGroupBox("مربعات الاختيار")
            checkbox_layout = QVBoxLayout(checkbox_group)
            
            checkbox1 = QCheckBox("خيار أول")
            checkbox2 = QCheckBox("خيار ثاني")
            checkbox1.setChecked(True)
            
            checkbox_layout.addWidget(checkbox1)
            checkbox_layout.addWidget(checkbox2)
            main_layout.addWidget(checkbox_group)
        
        def create_control_panel(self) -> QWidget:
            """إنشاء لوحة التحكم"""
            panel = QGroupBox("التحكم في المظهر")
            layout = QHBoxLayout(panel)
            
            # أزرار تغيير المظهر
            light_btn = QPushButton("مظهر فاتح")
            dark_btn = QPushButton("مظهر داكن")
            settings_btn = QPushButton("إعدادات متقدمة")
            
            light_btn.clicked.connect(lambda: self.change_theme('light'))
            dark_btn.clicked.connect(lambda: self.change_theme('dark'))
            settings_btn.clicked.connect(self.open_appearance_settings)
            
            layout.addWidget(light_btn)
            layout.addWidget(dark_btn)
            layout.addWidget(settings_btn)
            
            # أزرار تغيير الألوان
            layout.addWidget(QLabel("الألوان:"))
            
            colors = [("أزرق", "blue"), ("أخضر", "green"), ("بنفسجي", "purple")]
            for name, scheme in colors:
                btn = QPushButton(name)
                btn.clicked.connect(lambda checked, s=scheme: self.change_color_scheme(s))
                layout.addWidget(btn)
            
            layout.addStretch()
            return panel
        
        def change_theme(self, theme_mode: str):
            """تغيير وضع المظهر"""
            try:
                from src.utils.appearance_manager import get_appearance_manager
                appearance_manager = get_appearance_manager()
                appearance_manager.set_theme_mode(theme_mode)
                print(f"✅ تم تغيير المظهر إلى: {theme_mode}")
            except Exception as e:
                print(f"❌ خطأ في تغيير المظهر: {e}")
        
        def change_color_scheme(self, color_scheme: str):
            """تغيير مخطط الألوان"""
            try:
                from src.utils.appearance_manager import get_appearance_manager
                appearance_manager = get_appearance_manager()
                appearance_manager.set_color_scheme(color_scheme)
                print(f"✅ تم تغيير مخطط الألوان إلى: {color_scheme}")
            except Exception as e:
                print(f"❌ خطأ في تغيير مخطط الألوان: {e}")
        
        def open_appearance_settings(self):
            """فتح نافذة إعدادات المظهر"""
            try:
                from src.views.appearance_settings import AppearanceSettingsDialog
                dialog = AppearanceSettingsDialog(self)
                result = dialog.exec()
                if result == AppearanceSettingsDialog.Accepted:
                    print("✅ تم حفظ إعدادات المظهر")
                else:
                    print("ℹ️ تم إلغاء إعدادات المظهر")
            except Exception as e:
                print(f"❌ خطأ في فتح إعدادات المظهر: {e}")
                import traceback
                traceback.print_exc()
    
    return SimpleTestWindow()


def test_appearance_manager():
    """اختبار مدير المظهر"""
    print("🔍 اختبار مدير المظهر...")
    
    try:
        from src.utils.appearance_manager import get_appearance_manager
        appearance_manager = get_appearance_manager()
        
        # اختبار تغيير الخط
        print("📝 اختبار تغيير الخط...")
        appearance_manager.set_font_family("Arial")
        appearance_manager.set_font_size(14)
        print("✅ تم تغيير الخط بنجاح")
        
        # اختبار تغيير المظهر
        print("🌙 اختبار تغيير المظهر...")
        appearance_manager.set_theme_mode("dark")
        appearance_manager.set_theme_mode("light")
        print("✅ تم تغيير المظهر بنجاح")
        
        # اختبار تغيير الألوان
        print("🎨 اختبار تغيير الألوان...")
        appearance_manager.set_color_scheme("blue")
        appearance_manager.set_color_scheme("green")
        print("✅ تم تغيير الألوان بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير المظهر: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نظام المظهر المصلح")
    print("=" * 50)
    
    # اختبار مدير المظهر أولاً
    if not test_appearance_manager():
        print("💥 فشل في اختبار مدير المظهر")
        return 1
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تهيئة نظام المظهر
        try:
            from src.utils.appearance_manager import get_appearance_manager
            appearance_manager = get_appearance_manager()
            appearance_manager.load_settings()
            print("✅ تم تهيئة نظام المظهر")
        except Exception as e:
            print(f"⚠️ فشل في تهيئة نظام المظهر: {e}")
        
        # إنشاء نافذة الاختبار
        window = create_simple_test_window()
        window.show()
        
        print("✅ تم فتح نافذة الاختبار!")
        print("\n🎯 يمكنك الآن:")
        print("1. اختبار تغيير المظهر (فاتح/داكن)")
        print("2. اختبار تغيير مخططات الألوان")
        print("3. فتح إعدادات المظهر المتقدمة")
        print("4. مشاهدة التطبيق الفوري على العناصر")
        print("\nلا يجب أن تظهر رسائل 'Unknown property transform'")
        print("اضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
