"""
اختبار نظام تدقيق الأنشطة
Test Audit System
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from src.database.connection import get_db_session_context, db_manager
from src.models.audit_log import AuditLog, ActionType
from src.models.employee import Employee
from src.models.department import Department


def test_audit_logging():
    """اختبار تسجيل الأنشطة"""
    print("🔍 اختبار نظام تدقيق الأنشطة...")
    print("=" * 50)
    
    try:
        with get_db_session_context() as session:
            # 1. اختبار تسجيل عملية إنشاء
            print("\n1️⃣ اختبار تسجيل عملية إنشاء...")
            log_entry = AuditLog.log_action(
                session=session,
                action_type=ActionType.CREATE,
                table_name="employees",
                record_id="TEST_001",
                description="إنشاء موظف تجريبي للاختبار",
                old_values=None,
                new_values={
                    "name": "موظف تجريبي",
                    "employee_number": "TEST_001",
                    "department": "قسم الاختبار"
                },
                user_name="مدير النظام",
                ip_address="*************"
            )
            print(f"✅ تم تسجيل عملية الإنشاء: {log_entry.id}")
            
            # 2. اختبار تسجيل عملية تعديل
            print("\n2️⃣ اختبار تسجيل عملية تعديل...")
            log_entry = AuditLog.log_action(
                session=session,
                action_type=ActionType.UPDATE,
                table_name="employees",
                record_id="TEST_001",
                description="تعديل بيانات الموظف التجريبي",
                old_values={
                    "name": "موظف تجريبي",
                    "salary": 1000000
                },
                new_values={
                    "name": "موظف تجريبي محدث",
                    "salary": 1200000
                },
                user_name="مدير الموارد البشرية",
                ip_address="*************"
            )
            print(f"✅ تم تسجيل عملية التعديل: {log_entry.id}")
            
            # 3. اختبار تسجيل عملية حذف
            print("\n3️⃣ اختبار تسجيل عملية حذف...")
            log_entry = AuditLog.log_action(
                session=session,
                action_type=ActionType.DELETE,
                table_name="employees",
                record_id="TEST_001",
                description="حذف الموظف التجريبي",
                old_values={
                    "name": "موظف تجريبي محدث",
                    "employee_number": "TEST_001"
                },
                new_values=None,
                user_name="مدير النظام",
                ip_address="*************"
            )
            print(f"✅ تم تسجيل عملية الحذف: {log_entry.id}")
            
            # 4. اختبار تسجيل عمليات مالية
            print("\n4️⃣ اختبار تسجيل العمليات المالية...")
            log_entry = AuditLog.log_action(
                session=session,
                action_type=ActionType.FINANCIAL_TRANSACTION,
                table_name="financial_transactions",
                record_id="FT_001",
                description="صرف سلفة للموظف أحمد محمد",
                new_values={
                    "transaction_type": "ADVANCE",
                    "amount": 500000,
                    "employee_id": 1
                },
                user_name="مدير المالية",
                ip_address="*************"
            )
            print(f"✅ تم تسجيل العملية المالية: {log_entry.id}")
            
            # 5. اختبار تسجيل صرف راتب
            print("\n5️⃣ اختبار تسجيل صرف راتب...")
            log_entry = AuditLog.log_action(
                session=session,
                action_type=ActionType.SALARY_PAYMENT,
                table_name="salary_records",
                record_id="SAL_001",
                description="صرف راتب شهر ديسمبر 2024 للموظف أحمد محمد",
                new_values={
                    "employee_id": 1,
                    "month": 12,
                    "year": 2024,
                    "net_salary": 1500000
                },
                user_name="مدير المالية",
                ip_address="*************"
            )
            print(f"✅ تم تسجيل صرف الراتب: {log_entry.id}")
            
            # 6. اختبار تسجيل تسجيل دخول/خروج
            print("\n6️⃣ اختبار تسجيل دخول/خروج المستخدمين...")
            
            # تسجيل دخول
            log_entry = AuditLog.log_action(
                session=session,
                action_type=ActionType.LOGIN,
                description="تسجيل دخول المستخدم إلى النظام",
                user_name="مدير النظام",
                ip_address="*************"
            )
            print(f"✅ تم تسجيل الدخول: {log_entry.id}")
            
            # تسجيل خروج
            log_entry = AuditLog.log_action(
                session=session,
                action_type=ActionType.LOGOUT,
                description="تسجيل خروج المستخدم من النظام",
                user_name="مدير النظام",
                ip_address="*************"
            )
            print(f"✅ تم تسجيل الخروج: {log_entry.id}")
            
            # 7. اختبار تسجيل توليد التقارير
            print("\n7️⃣ اختبار تسجيل توليد التقارير...")
            log_entry = AuditLog.log_action(
                session=session,
                action_type=ActionType.REPORT_GENERATION,
                description="توليد تقرير الموظفين الشهري",
                new_values={
                    "report_type": "employees_monthly",
                    "filters": {"department": "جميع الأقسام", "month": 12, "year": 2024}
                },
                user_name="مدير الموارد البشرية",
                ip_address="*************"
            )
            print(f"✅ تم تسجيل توليد التقرير: {log_entry.id}")
            
            print(f"\n✅ تم إنشاء {7} سجل تدقيق تجريبي بنجاح!")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الأنشطة: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


def test_audit_retrieval():
    """اختبار استرجاع سجلات التدقيق"""
    print("\n🔍 اختبار استرجاع سجلات التدقيق...")
    print("=" * 50)
    
    try:
        with get_db_session_context() as session:
            # 1. اختبار الحصول على أحدث السجلات
            print("\n1️⃣ اختبار الحصول على أحدث السجلات...")
            recent_logs = AuditLog.get_recent_logs(session, limit=10)
            print(f"✅ تم العثور على {len(recent_logs)} سجل حديث")
            
            for i, log in enumerate(recent_logs[:3], 1):
                print(f"   {i}. {log.action_type.value} - {log.description[:50]}...")
            
            # 2. اختبار البحث حسب نوع العملية
            print("\n2️⃣ اختبار البحث حسب نوع العملية...")
            create_logs = AuditLog.get_logs_by_action(session, ActionType.CREATE, limit=5)
            print(f"✅ تم العثور على {len(create_logs)} سجل إنشاء")
            
            # 3. اختبار البحث حسب المستخدم
            print("\n3️⃣ اختبار البحث حسب المستخدم...")
            user_logs = AuditLog.get_logs_by_user(session, "مدير النظام", limit=5)
            print(f"✅ تم العثور على {len(user_logs)} سجل لمدير النظام")
            
            # 4. اختبار البحث حسب الجدول
            print("\n4️⃣ اختبار البحث حسب الجدول...")
            table_logs = AuditLog.get_logs_by_table(session, "employees", limit=5)
            print(f"✅ تم العثور على {len(table_logs)} سجل لجدول الموظفين")
            
            # 5. اختبار البحث حسب نطاق التاريخ
            print("\n5️⃣ اختبار البحث حسب نطاق التاريخ...")
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            date_logs = AuditLog.get_logs_by_date_range(session, start_date, end_date, limit=10)
            print(f"✅ تم العثور على {len(date_logs)} سجل في آخر 7 أيام")
            
            # 6. اختبار ملخص الأنشطة
            print("\n6️⃣ اختبار ملخص الأنشطة...")
            summary = AuditLog.get_activity_summary(session, days=7)
            print(f"✅ إجمالي الأنشطة: {summary['total_activities']}")
            print(f"✅ عدد أنواع العمليات: {len(summary['action_stats'])}")
            print(f"✅ عدد المستخدمين النشطين: {len(summary['user_stats'])}")
            print(f"✅ عدد الجداول المتأثرة: {len(summary['table_stats'])}")
            
            print("\n📊 إحصائيات أنواع العمليات:")
            for action, count in summary['action_stats'][:5]:
                print(f"   • {action}: {count} عملية")
            
            print("\n👥 أكثر المستخدمين نشاطاً:")
            for user, count in summary['user_stats'][:3]:
                print(f"   • {user}: {count} عملية")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار استرجاع السجلات: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


def test_audit_analytics():
    """اختبار التحليلات المتقدمة"""
    print("\n📊 اختبار التحليلات المتقدمة...")
    print("=" * 50)
    
    try:
        with get_db_session_context() as session:
            # إحصائيات شاملة
            total_logs = session.query(AuditLog).count()
            print(f"✅ إجمالي سجلات التدقيق في النظام: {total_logs}")
            
            # إحصائيات حسب نوع العملية
            from sqlalchemy import func
            action_stats = session.query(
                AuditLog.action_type,
                func.count(AuditLog.id).label('count')
            ).group_by(AuditLog.action_type).all()
            
            print(f"\n📈 توزيع العمليات حسب النوع:")
            for action, count in action_stats:
                percentage = (count / total_logs * 100) if total_logs > 0 else 0
                print(f"   • {action.value}: {count} ({percentage:.1f}%)")
            
            # إحصائيات المستخدمين
            user_stats = session.query(
                AuditLog.user_name,
                func.count(AuditLog.id).label('count')
            ).filter(
                AuditLog.user_name.isnot(None)
            ).group_by(AuditLog.user_name).order_by(
                func.count(AuditLog.id).desc()
            ).limit(5).all()
            
            print(f"\n👥 أكثر المستخدمين نشاطاً:")
            for user, count in user_stats:
                percentage = (count / total_logs * 100) if total_logs > 0 else 0
                print(f"   • {user}: {count} عملية ({percentage:.1f}%)")
            
            # إحصائيات الجداول
            table_stats = session.query(
                AuditLog.table_name,
                func.count(AuditLog.id).label('count')
            ).filter(
                AuditLog.table_name.isnot(None)
            ).group_by(AuditLog.table_name).order_by(
                func.count(AuditLog.id).desc()
            ).limit(5).all()
            
            print(f"\n🗃️ أكثر الجداول تأثراً:")
            for table, count in table_stats:
                percentage = (count / total_logs * 100) if total_logs > 0 else 0
                print(f"   • {table}: {count} عملية ({percentage:.1f}%)")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التحليلات: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نظام تدقيق الأنشطة المتقدم")
    print("=" * 60)

    # تهيئة قاعدة البيانات
    print("\n🔧 تهيئة قاعدة البيانات...")
    try:
        db_manager.initialize()
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
    except Exception as e:
        print(f"❌ فشل في تهيئة قاعدة البيانات: {e}")
        return False
    
    # تشغيل الاختبارات
    tests = [
        ("تسجيل الأنشطة", test_audit_logging),
        ("استرجاع السجلات", test_audit_retrieval),
        ("التحليلات المتقدمة", test_audit_analytics)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 اختبار: {test_name}")
        print("-" * 40)
        
        if test_func():
            print(f"✅ نجح اختبار: {test_name}")
            passed += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار النهائية:")
    print(f"✅ نجح: {passed}/{total}")
    print(f"❌ فشل: {total - passed}/{total}")
    print(f"📈 معدل النجاح: {(passed/total*100):.1f}%")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت! نظام تدقيق الأنشطة يعمل بشكل مثالي!")
    else:
        print(f"\n⚠️ فشل {total - passed} اختبار. يرجى مراجعة الأخطاء أعلاه.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
