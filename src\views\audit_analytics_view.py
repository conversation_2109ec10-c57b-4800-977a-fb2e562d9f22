"""
واجهة إحصائيات تدقيق الأنشطة المتقدمة
Advanced Audit Analytics View
"""

from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, 
    QComboBox, QPushButton, QTableWidget, QTableWidgetItem,
    QTabWidget, QGridLayout
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor

from ..utils import apply_rtl_layout, show_message
from ..database.connection import get_db_session_context
from ..models.audit_log import AuditLog, ActionType


class AuditAnalyticsView(QWidget):
    """واجهة إحصائيات تدقيق الأنشطة المتقدمة"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_analytics()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setObjectName("audit_analytics_view")
        apply_rtl_layout(self)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # إنشاء رأس الصفحة
        self.create_header(main_layout)
        
        # إنشاء التبويبات
        self.create_tabs(main_layout)
        
    def create_header(self, layout: QVBoxLayout):
        """إنشاء رأس الصفحة المتناسق"""
        # إنشاء إطار العنوان الرئيسي المدمج
        header_frame = QFrame()
        header_frame.setObjectName("compact_header_frame")
        header_frame.setFixedHeight(120)
        header_frame.setStyleSheet("""
            QFrame#compact_header_frame {
                background-color: #e8f5e8;
                border: 2px solid #4caf50;
                border-radius: 10px;
                margin: 5px;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 8, 15, 8)
        header_layout.setSpacing(5)

        # إضافة مساحة مرنة في الأعلى لتوسيط المحتوى
        header_layout.addStretch()

        # إنشاء حاوي للتوسيط الأفقي للعنوان الرئيسي
        main_title_container = QHBoxLayout()
        main_title_container.addStretch()
        
        main_title = QLabel("📊 إحصائيات تدقيق الأنشطة")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 22px;
                font-weight: bold;
                color: #2c3e50;
                background-color: transparent;
                padding: 5px;
                margin: 0px;
            }
        """)
        
        main_title_container.addWidget(main_title)
        main_title_container.addStretch()

        # إنشاء حاوي للتوسيط الأفقي للعنوان الفرعي
        subtitle_container = QHBoxLayout()
        subtitle_container.addStretch()
        
        subtitle = QLabel("📈 تحليل متقدم لأنشطة النظام والمستخدمين")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #34495e;
                background-color: #f1f8e9;
                padding: 6px 15px;
                border-radius: 6px;
                border-left: 3px solid #4caf50;
                margin: 2px;
            }
        """)
        
        subtitle_container.addWidget(subtitle)
        subtitle_container.addStretch()

        # تجميع العناصر
        header_layout.addLayout(main_title_container)
        header_layout.addLayout(subtitle_container)
        
        # إضافة مساحة مرنة في الأسفل لتوسيط المحتوى
        header_layout.addStretch()

        layout.addWidget(header_frame)
        
    def create_tabs(self, layout: QVBoxLayout):
        """إنشاء التبويبات"""
        self.tabs_widget = QTabWidget()
        self.tabs_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #dee2e6;
                border-radius: 10px;
                background-color: white;
                padding: 15px;
                margin-top: 5px;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-bottom-color: #C2C7CB;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                min-width: 140px;
                padding: 10px 16px;
                margin: 2px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-weight: bold;
                font-size: 13px;
                color: #495057;
            }
            QTabBar::tab:selected {
                background-color: #4caf50;
                color: white;
                border-color: #4caf50;
                border-bottom-color: #4caf50;
            }
            QTabBar::tab:hover:!selected {
                background-color: #e8f5e8;
                color: #2e7d32;
                border-color: #81c784;
            }
        """)
        
        # إنشاء التبويبات
        self.create_overview_tab()
        self.create_activity_trends_tab()
        self.create_user_activity_tab()
        self.create_system_health_tab()
        
        layout.addWidget(self.tabs_widget)
        
    def create_overview_tab(self):
        """إنشاء تبويب النظرة العامة"""
        overview_widget = QWidget()
        layout = QVBoxLayout(overview_widget)
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)
        
        stats_layout = QGridLayout(stats_frame)
        
        # بطاقات الإحصائيات
        self.total_activities_label = self.create_stat_card("📊 إجمالي الأنشطة", "0", "#2196F3")
        self.today_activities_label = self.create_stat_card("📅 أنشطة اليوم", "0", "#4CAF50")
        self.active_users_label = self.create_stat_card("👥 المستخدمون النشطون", "0", "#FF9800")
        self.critical_events_label = self.create_stat_card("⚠️ الأحداث الحرجة", "0", "#F44336")
        
        stats_layout.addWidget(self.total_activities_label, 0, 0)
        stats_layout.addWidget(self.today_activities_label, 0, 1)
        stats_layout.addWidget(self.active_users_label, 0, 2)
        stats_layout.addWidget(self.critical_events_label, 0, 3)
        
        layout.addWidget(stats_frame)
        
        # جدول أحدث الأنشطة
        recent_frame = QFrame()
        recent_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)
        
        recent_layout = QVBoxLayout(recent_frame)
        
        recent_title = QLabel("🕒 أحدث الأنشطة")
        recent_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        recent_layout.addWidget(recent_title)
        
        self.recent_activities_table = QTableWidget()
        self.setup_recent_activities_table()
        recent_layout.addWidget(self.recent_activities_table)
        
        layout.addWidget(recent_frame)
        
        self.tabs_widget.addTab(overview_widget, "📊 النظرة العامة")
        
    def create_stat_card(self, title: str, value: str, color: str) -> QFrame:
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(5)
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: {color};
                text-align: center;
            }}
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 24px;
                font-weight: bold;
                color: {color};
                text-align: center;
            }}
        """)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setObjectName("value_label")
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        return card
        
    def setup_recent_activities_table(self):
        """إعداد جدول أحدث الأنشطة"""
        headers = ["الوقت", "نوع العملية", "المستخدم", "الوصف"]
        self.recent_activities_table.setColumnCount(len(headers))
        self.recent_activities_table.setHorizontalHeaderLabels(headers)
        
        # تحديد عرض الأعمدة
        column_widths = [150, 120, 120, 400]
        for i, width in enumerate(column_widths):
            self.recent_activities_table.setColumnWidth(i, width)
            
        self.recent_activities_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                gridline-color: #e9ecef;
            }
            QHeaderView::section {
                background-color: #4caf50;
                color: white;
                font-weight: bold;
                font-size: 12px;
                padding: 8px;
                border: none;
                text-align: center;
            }
        """)
        
    def create_activity_trends_tab(self):
        """إنشاء تبويب اتجاهات الأنشطة"""
        trends_widget = QWidget()
        layout = QVBoxLayout(trends_widget)
        
        # سيتم إضافة الرسوم البيانية هنا
        placeholder = QLabel("📈 رسوم بيانية لاتجاهات الأنشطة\n(قيد التطوير)")
        placeholder.setAlignment(Qt.AlignCenter)
        placeholder.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #666;
                padding: 50px;
            }
        """)
        layout.addWidget(placeholder)
        
        self.tabs_widget.addTab(trends_widget, "📈 اتجاهات الأنشطة")
        
    def create_user_activity_tab(self):
        """إنشاء تبويب نشاط المستخدمين"""
        users_widget = QWidget()
        layout = QVBoxLayout(users_widget)
        
        # جدول نشاط المستخدمين
        self.user_activity_table = QTableWidget()
        self.setup_user_activity_table()
        layout.addWidget(self.user_activity_table)
        
        self.tabs_widget.addTab(users_widget, "👥 نشاط المستخدمين")
        
    def setup_user_activity_table(self):
        """إعداد جدول نشاط المستخدمين"""
        headers = ["المستخدم", "عدد الأنشطة", "آخر نشاط", "أكثر العمليات"]
        self.user_activity_table.setColumnCount(len(headers))
        self.user_activity_table.setHorizontalHeaderLabels(headers)
        
        column_widths = [150, 120, 180, 200]
        for i, width in enumerate(column_widths):
            self.user_activity_table.setColumnWidth(i, width)
            
        self.user_activity_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                gridline-color: #e9ecef;
            }
            QHeaderView::section {
                background-color: #4caf50;
                color: white;
                font-weight: bold;
                font-size: 12px;
                padding: 8px;
                border: none;
                text-align: center;
            }
        """)
        
    def create_system_health_tab(self):
        """إنشاء تبويب صحة النظام"""
        health_widget = QWidget()
        layout = QVBoxLayout(health_widget)
        
        # مؤشرات صحة النظام
        placeholder = QLabel("🏥 مؤشرات صحة النظام\n(قيد التطوير)")
        placeholder.setAlignment(Qt.AlignCenter)
        placeholder.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #666;
                padding: 50px;
            }
        """)
        layout.addWidget(placeholder)
        
        self.tabs_widget.addTab(health_widget, "🏥 صحة النظام")
        
    def load_analytics(self):
        """تحميل البيانات التحليلية"""
        try:
            with get_db_session_context() as session:
                # الحصول على ملخص الأنشطة
                summary = AuditLog.get_activity_summary(session, days=7)
                
                # تحديث الإحصائيات السريعة
                self.update_quick_stats(summary)
                
                # تحميل أحدث الأنشطة
                self.load_recent_activities(session)
                
                # تحميل نشاط المستخدمين
                self.load_user_activities(session, summary)
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل البيانات التحليلية: {e}", "error")
            
    def update_quick_stats(self, summary):
        """تحديث الإحصائيات السريعة"""
        # إجمالي الأنشطة
        total_label = self.total_activities_label.findChild(QLabel, "value_label")
        if total_label:
            total_label.setText(str(summary.get('total_activities', 0)))
        
        # أنشطة اليوم (تقدير)
        today_label = self.today_activities_label.findChild(QLabel, "value_label")
        if today_label:
            today_activities = summary.get('total_activities', 0) // 7  # تقدير يومي
            today_label.setText(str(today_activities))
        
        # المستخدمون النشطون
        active_users_label = self.active_users_label.findChild(QLabel, "value_label")
        if active_users_label:
            active_users_count = len(summary.get('user_stats', []))
            active_users_label.setText(str(active_users_count))
        
        # الأحداث الحرجة (تقدير)
        critical_label = self.critical_events_label.findChild(QLabel, "value_label")
        if critical_label:
            critical_events = len([action for action, count in summary.get('action_stats', []) 
                                 if 'حذف' in action or 'خطأ' in action])
            critical_label.setText(str(critical_events))
            
    def load_recent_activities(self, session):
        """تحميل أحدث الأنشطة"""
        recent_logs = AuditLog.get_recent_logs(session, limit=10)
        
        self.recent_activities_table.setRowCount(len(recent_logs))
        
        for row, log in enumerate(recent_logs):
            # الوقت
            time_item = QTableWidgetItem(log.timestamp.strftime('%Y-%m-%d %H:%M'))
            time_item.setTextAlignment(Qt.AlignCenter)
            self.recent_activities_table.setItem(row, 0, time_item)
            
            # نوع العملية
            action_item = QTableWidgetItem(log.action_type.value)
            action_item.setTextAlignment(Qt.AlignCenter)
            self.recent_activities_table.setItem(row, 1, action_item)
            
            # المستخدم
            user_item = QTableWidgetItem(log.user_name or "غير محدد")
            user_item.setTextAlignment(Qt.AlignCenter)
            self.recent_activities_table.setItem(row, 2, user_item)
            
            # الوصف
            desc_item = QTableWidgetItem(log.description[:100] + "..." if len(log.description) > 100 else log.description)
            desc_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.recent_activities_table.setItem(row, 3, desc_item)
            
    def load_user_activities(self, session, summary):
        """تحميل نشاط المستخدمين"""
        user_stats = summary.get('user_stats', [])
        
        self.user_activity_table.setRowCount(len(user_stats))
        
        for row, (user_name, activity_count) in enumerate(user_stats):
            # اسم المستخدم
            user_item = QTableWidgetItem(user_name)
            user_item.setTextAlignment(Qt.AlignCenter)
            self.user_activity_table.setItem(row, 0, user_item)
            
            # عدد الأنشطة
            count_item = QTableWidgetItem(str(activity_count))
            count_item.setTextAlignment(Qt.AlignCenter)
            self.user_activity_table.setItem(row, 1, count_item)
            
            # آخر نشاط
            last_activity = session.query(AuditLog).filter_by(user_name=user_name).order_by(AuditLog.timestamp.desc()).first()
            last_time = last_activity.timestamp.strftime('%Y-%m-%d %H:%M') if last_activity else "غير محدد"
            time_item = QTableWidgetItem(last_time)
            time_item.setTextAlignment(Qt.AlignCenter)
            self.user_activity_table.setItem(row, 2, time_item)
            
            # أكثر العمليات
            most_common = "متنوع"  # يمكن تحسينه لاحقاً
            common_item = QTableWidgetItem(most_common)
            common_item.setTextAlignment(Qt.AlignCenter)
            self.user_activity_table.setItem(row, 3, common_item)
