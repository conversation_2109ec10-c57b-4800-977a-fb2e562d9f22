"""
واجهة التقارير الجديدة المبسطة
New Simplified Reports View
"""

from datetime import datetime
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

from ..utils import apply_rtl_layout
from .enhanced_reports_view import EnhancedReportsView


class ReportsView(QWidget):
    """واجهة التقارير المحسنة - تحتوي على جميع التقارير في تبويبات منفصلة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # إضافة واجهة التقارير المحسنة مباشرة
        self.enhanced_reports_view = EnhancedReportsView()
        layout.addWidget(self.enhanced_reports_view)
        
        # تطبيق التخطيط العربي
        apply_rtl_layout(self)
