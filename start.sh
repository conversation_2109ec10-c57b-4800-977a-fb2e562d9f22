#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    نظام إدارة شؤون الموظفين                    ║"
echo "║                   HR Management System                       ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

echo -e "${YELLOW}🔍 فحص المتطلبات...${NC}"

# فحص Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ Python غير مثبت${NC}"
        echo -e "${YELLOW}💡 يرجى تثبيت Python 3.8+ من https://python.org${NC}"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo -e "${GREEN}✅ Python متوفر${NC}"

# فحص PySide6
if ! $PYTHON_CMD -c "import PySide6" &> /dev/null; then
    echo -e "${RED}❌ PySide6 غير مثبت${NC}"
    echo -e "${YELLOW}💡 جاري تثبيت PySide6...${NC}"
    
    if command -v pip3 &> /dev/null; then
        pip3 install PySide6
    elif command -v pip &> /dev/null; then
        pip install PySide6
    else
        echo -e "${RED}❌ pip غير متوفر${NC}"
        exit 1
    fi
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ فشل في تثبيت PySide6${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}✅ PySide6 متوفر${NC}"

echo ""
echo -e "${BLUE}🚀 اختر نوع التشغيل:${NC}"
echo "1. النسخة التجريبية (مُوصى به)"
echo "2. النسخة التفاعلية"
echo "3. خروج"
echo ""

read -p "أدخل اختيارك (1-3): " choice

case $choice in
    1)
        echo ""
        echo -e "${GREEN}🎮 تشغيل النسخة التجريبية...${NC}"
        $PYTHON_CMD demo_app.py
        ;;
    2)
        echo ""
        echo -e "${BLUE}🔧 تشغيل النسخة التفاعلية...${NC}"
        $PYTHON_CMD simple_run.py
        ;;
    3)
        echo -e "${YELLOW}👋 وداعاً!${NC}"
        exit 0
        ;;
    *)
        echo -e "${RED}❌ اختيار غير صحيح${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${GREEN}✅ انتهى التشغيل${NC}"
