"""
نموذج إضافة/تعديل القسم
Department Add/Edit Form
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit, QTextEdit,
    QPushButton, QLabel, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from ..utils import apply_rtl_layout, show_message
from ..database import get_db_session_context
from ..models import Department


class DepartmentForm(QDialog):
    """نموذج إضافة/تعديل القسم"""
    
    # إشارات
    department_saved = Signal(int)  # إشارة حفظ القسم
    
    def __init__(self, department_id: int = None, parent=None):
        super().__init__(parent)
        self.department_id = department_id
        self.is_edit_mode = department_id is not None
        self.department = None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_department_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setObjectName("department_form")
        apply_rtl_layout(self)
        
        # إعداد النافذة
        title = "✏️ تعديل القسم" if self.is_edit_mode else "➕ إضافة قسم جديد"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(600, 450)  # تكبير النافذة
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # إنشاء رأس النموذج
        self.create_header(main_layout)
        
        # إنشاء حقول النموذج
        self.create_form_fields(main_layout)
        
        # إنشاء أزرار الإجراءات
        self.create_action_buttons(main_layout)
        
    def create_header(self, layout: QVBoxLayout):
        """إنشاء رأس النموذج المحسن"""
        # إطار العنوان
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f4fd;
                border: 2px solid #007bff;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(8)

        # العنوان الرئيسي
        title = "✏️ تعديل بيانات القسم" if self.is_edit_mode else "➕ إضافة قسم جديد"
        main_title = QLabel(title)
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin: 5px 0px;
            }
        """)

        # العنوان الفرعي
        subtitle = "🏗️ يرجى ملء جميع الحقول المطلوبة بدقة" if not self.is_edit_mode else "📝 تحديث معلومات القسم"
        sub_title = QLabel(subtitle)
        sub_title.setAlignment(Qt.AlignCenter)
        sub_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #6c757d;
                margin: 0px;
            }
        """)

        header_layout.addWidget(main_title)
        header_layout.addWidget(sub_title)
        layout.addWidget(header_frame)
        
    def create_form_fields(self, layout: QVBoxLayout):
        """إنشاء حقول النموذج المحسنة"""
        # إطار النموذج
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 20px;
                margin: 10px 5px;
            }
        """)

        form_layout = QFormLayout(form_frame)
        form_layout.setSpacing(15)
        form_layout.setLabelAlignment(Qt.AlignRight)

        # تنسيق التسميات
        label_style = """
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #495057;
                padding: 5px;
            }
        """

        # رمز القسم
        code_label = QLabel("🏷️ رمز القسم *:")
        code_label.setStyleSheet(label_style)

        code_layout = QHBoxLayout()
        self.code_input = QLineEdit()
        self.code_input.setMaxLength(10)
        self.code_input.setPlaceholderText("سيتم توليده تلقائياً")
        self.code_input.setReadOnly(True)
        self.code_input.setStyleSheet("""
            QLineEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                padding: 10px 12px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #f8f9fa;
                color: #6c757d;
            }
            QLineEdit:focus {
                border-color: #007bff;
                background-color: #ffffff;
                color: #495057;
            }
        """)

        self.generate_code_btn = QPushButton("🔄 توليد تلقائي")
        self.generate_code_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #17a2b8;
                border: none;
                border-radius: 5px;
                padding: 10px 15px;
                min-width: 100px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:pressed {
                background-color: #117a8b;
            }
        """)

        code_layout.addWidget(self.code_input)
        code_layout.addWidget(self.generate_code_btn)
        form_layout.addRow(code_label, code_layout)

        # اسم القسم
        name_label = QLabel("🏢 اسم القسم *:")
        name_label.setStyleSheet(label_style)

        self.name_input = QLineEdit()
        self.name_input.setMaxLength(100)
        self.name_input.setPlaceholderText("مثال: قسم تقنية المعلومات")
        self.name_input.setStyleSheet("""
            QLineEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                padding: 10px 12px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #007bff;
                background-color: #f8f9fa;
            }
            QLineEdit:hover {
                border-color: #adb5bd;
            }
        """)
        form_layout.addRow(name_label, self.name_input)

        # وصف القسم
        desc_label = QLabel("📝 الوصف:")
        desc_label.setStyleSheet(label_style)

        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(120)
        self.description_input.setPlaceholderText("وصف مختصر للقسم ومهامه الأساسية...")
        self.description_input.setStyleSheet("""
            QTextEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                padding: 10px 12px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #ffffff;
            }
            QTextEdit:focus {
                border-color: #007bff;
                background-color: #f8f9fa;
            }
            QTextEdit:hover {
                border-color: #adb5bd;
            }
        """)
        form_layout.addRow(desc_label, self.description_input)

        layout.addWidget(form_frame)
        
    def create_action_buttons(self, layout: QVBoxLayout):
        """إنشاء أزرار الإجراءات المحسنة"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px 5px 5px 5px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)

        # زر الحفظ
        save_text = "💾 تحديث البيانات" if self.is_edit_mode else "💾 حفظ القسم"
        self.save_btn = QPushButton(save_text)
        self.save_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: white;
                background-color: #28a745;
                border: none;
                border-radius: 8px;
                padding: 12px 25px;
                min-width: 140px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #218838;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #1e7e34;
                transform: translateY(0px);
            }
        """)

        # زر الإلغاء
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #6c757d;
                background-color: #ffffff;
                border: 2px solid #6c757d;
                border-radius: 8px;
                padding: 12px 25px;
                min-width: 100px;
                min-height: 45px;
            }
            QPushButton:hover {
                color: white;
                background-color: #6c757d;
                border-color: #6c757d;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #5a6268;
                border-color: #5a6268;
                transform: translateY(0px);
            }
        """)

        # ترتيب الأزرار
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_department)
        self.cancel_btn.clicked.connect(self.reject)

        # ربط زر توليد الرمز
        self.generate_code_btn.clicked.connect(self.generate_department_code)

        # توليد رمز تلقائي عند تغيير اسم القسم
        self.name_input.textChanged.connect(self.auto_generate_code)
        
    def load_department_data(self):
        """تحميل بيانات القسم للتعديل"""
        try:
            with get_db_session_context() as session:
                self.department = session.query(Department).filter_by(id=self.department_id).first()
                
                if not self.department:
                    show_message(self, "خطأ", "القسم غير موجود", "error")
                    self.reject()
                    return
                
                # ملء الحقول
                self.code_input.setText(self.department.code)
                self.name_input.setText(self.department.name)
                
                if self.department.description:
                    self.description_input.setPlainText(self.department.description)
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل بيانات القسم: {e}", "error")
            self.reject()

    def generate_department_code(self):
        """توليد رمز القسم تلقائياً"""
        name = self.name_input.text().strip()
        if not name:
            show_message(self, "تنبيه", "يرجى إدخال اسم القسم أولاً", "warning")
            return

        # توليد رمز من الاسم
        code = self.create_code_from_name(name)

        # التأكد من عدم تكرار الرمز
        unique_code = self.ensure_unique_code(code)

        self.code_input.setText(unique_code)
        self.code_input.setReadOnly(False)

    def auto_generate_code(self):
        """توليد رمز تلقائي عند تغيير الاسم"""
        if self.department_id is None:  # فقط للأقسام الجديدة
            name = self.name_input.text().strip()
            if name and len(name) >= 3:
                code = self.create_code_from_name(name)
                unique_code = self.ensure_unique_code(code)
                self.code_input.setText(unique_code)

    def create_code_from_name(self, name: str) -> str:
        """إنشاء رمز من الاسم"""
        # إزالة الكلمات الشائعة
        common_words = ["قسم", "إدارة", "وحدة", "مكتب", "فرع"]
        words = name.split()
        filtered_words = [word for word in words if word not in common_words]

        if not filtered_words:
            filtered_words = words

        # أخذ الأحرف الأولى من الكلمات
        if len(filtered_words) == 1:
            # كلمة واحدة: أخذ أول 3-4 أحرف
            code = filtered_words[0][:4].upper()
        else:
            # عدة كلمات: أخذ الحرف الأول من كل كلمة
            code = "".join([word[0].upper() for word in filtered_words[:3]])

        # التأكد من أن الرمز يحتوي على أحرف إنجليزية
        if not code.isascii():
            # تحويل الأحرف العربية إلى إنجليزية
            arabic_to_english = {
                'ت': 'T', 'م': 'M', 'ق': 'Q', 'ا': 'A', 'ل': 'L',
                'ر': 'R', 'د': 'D', 'ن': 'N', 'س': 'S', 'ع': 'E',
                'ب': 'B', 'ف': 'F', 'ح': 'H', 'ج': 'J', 'ك': 'K',
                'و': 'W', 'ز': 'Z', 'ط': 'T', 'ي': 'Y', 'ص': 'S',
                'خ': 'KH', 'ذ': 'TH', 'ض': 'DH', 'غ': 'GH', 'ظ': 'ZH'
            }

            english_code = ""
            for char in code:
                english_code += arabic_to_english.get(char, char)
            code = english_code[:4]

        return code

    def ensure_unique_code(self, base_code: str) -> str:
        """التأكد من عدم تكرار الرمز"""
        try:
            with get_db_session_context() as session:
                code = base_code
                counter = 1

                while True:
                    existing = session.query(Department).filter(
                        Department.code == code,
                        Department.id != (self.department_id or 0),
                        Department.is_active == True
                    ).first()

                    if not existing:
                        return code

                    # إضافة رقم للرمز
                    code = f"{base_code}{counter}"
                    counter += 1

                    if counter > 99:  # تجنب الحلقة اللانهائية
                        break

                return f"{base_code}{counter}"

        except Exception:
            return base_code
            
    def validate_form(self) -> tuple[bool, str]:
        """التحقق من صحة النموذج"""
        # التحقق من الحقول المطلوبة
        if not self.code_input.text().strip():
            return False, "رمز القسم مطلوب"
        
        if not self.name_input.text().strip():
            return False, "اسم القسم مطلوب"
        
        # التحقق من صحة رمز القسم
        code = self.code_input.text().strip()
        if len(code) < 2 or len(code) > 10:
            return False, "رمز القسم يجب أن يكون بين 2-10 أحرف"
        
        if not code.isalnum():
            return False, "رمز القسم يجب أن يحتوي على أحرف وأرقام فقط"
        
        return True, ""
        
    def save_department(self):
        """حفظ بيانات القسم"""
        # التحقق من صحة النموذج
        is_valid, error_message = self.validate_form()
        if not is_valid:
            show_message(self, "خطأ في البيانات", error_message, "error")
            return
        
        try:
            with get_db_session_context() as session:
                # التحقق من عدم تكرار رمز القسم
                existing_department = session.query(Department).filter(
                    Department.code == self.code_input.text().strip(),
                    Department.id != (self.department_id or 0),
                    Department.is_active == True
                ).first()
                
                if existing_department:
                    show_message(self, "خطأ", "رمز القسم موجود مسبقاً", "error")
                    return
                
                # التحقق من عدم تكرار اسم القسم
                existing_name = session.query(Department).filter(
                    Department.name == self.name_input.text().strip(),
                    Department.id != (self.department_id or 0),
                    Department.is_active == True
                ).first()
                
                if existing_name:
                    show_message(self, "خطأ", "اسم القسم موجود مسبقاً", "error")
                    return
                
                # إنشاء أو تحديث القسم
                if self.is_edit_mode:
                    department = session.query(Department).filter_by(id=self.department_id).first()
                else:
                    department = Department()
                    session.add(department)
                
                # تعيين البيانات
                department.code = self.code_input.text().strip()
                department.name = self.name_input.text().strip()
                department.description = self.description_input.toPlainText().strip() or None
                
                session.commit()
                
                # إرسال إشارة النجاح
                self.department_saved.emit(department.id)
                
                success_message = "✅ تم تحديث بيانات القسم بنجاح!" if self.is_edit_mode else "✅ تم إضافة القسم بنجاح!"
                show_message(self, "🎉 عملية ناجحة", success_message, "information")
                
                self.accept()
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في حفظ بيانات القسم: {e}", "error")
