<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1200</width>
    <height>800</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>🏢 نظام إدارة الموارد البشرية المتقدم</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>.</normaloff>.</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">QMainWindow {
    background-color: #f8f9fa;
}

/* شريط القوائم */
QMenuBar {
    background-color: #2c3e50;
    color: white;
    border: none;
    padding: 5px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 12px;
    margin: 2px;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background-color: #34495e;
}

QMenuBar::item:pressed {
    background-color: #3498db;
}

/* شريط الحالة */
QStatusBar {
    background-color: #34495e;
    color: white;
    border-top: 2px solid #3498db;
    padding: 5px;
}

QStatusBar::item {
    border: none;
}</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="main_layout">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QFrame" name="sidebar_frame">
      <property name="minimumSize">
       <size>
        <width>250</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>250</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QFrame {
    background-color: #2c3e50;
    border: none;
}</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="sidebar_layout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QFrame" name="logo_frame">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>80</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>80</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QFrame {
    background-color: #34495e;
    border-bottom: 2px solid #3498db;
}</string>
         </property>
         <layout class="QHBoxLayout" name="logo_layout">
          <item>
           <widget class="QLabel" name="logo_label">
            <property name="text">
             <string>🏢 نظام الموارد البشرية</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: white;
    font-size: 16px;
    font-weight: bold;
    padding: 10px;
}</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QScrollArea" name="menu_scroll_area">
         <property name="styleSheet">
          <string notr="true">QScrollArea {
    border: none;
    background-color: transparent;
}
QScrollBar:vertical {
    background-color: #34495e;
    width: 12px;
    border-radius: 6px;
}
QScrollBar::handle:vertical {
    background-color: #3498db;
    border-radius: 6px;
    min-height: 20px;
}
QScrollBar::handle:vertical:hover {
    background-color: #2980b9;
}</string>
         </property>
         <property name="widgetResizable">
          <bool>true</bool>
         </property>
         <widget class="QWidget" name="menu_content">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>248</width>
            <height>718</height>
           </rect>
          </property>
          <layout class="QVBoxLayout" name="menu_layout">
           <property name="spacing">
            <number>5</number>
           </property>
           <property name="leftMargin">
            <number>10</number>
           </property>
           <property name="topMargin">
            <number>20</number>
           </property>
           <property name="rightMargin">
            <number>10</number>
           </property>
           <property name="bottomMargin">
            <number>20</number>
           </property>
           <item>
            <widget class="QPushButton" name="dashboard_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string>📊 لوحة التحكم</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #2980b9;
}
QPushButton:pressed {
    background-color: #21618c;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="employees_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string>👥 إدارة الموظفين</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    border-radius: 8px;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #34495e;
    color: #3498db;
}
QPushButton:pressed {
    background-color: #2c3e50;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="departments_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string>🏢 إدارة الأقسام</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    border-radius: 8px;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #34495e;
    color: #3498db;
}
QPushButton:pressed {
    background-color: #2c3e50;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="job_titles_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string>💼 إدارة المناصب</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    border-radius: 8px;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #34495e;
    color: #3498db;
}
QPushButton:pressed {
    background-color: #2c3e50;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="salaries_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string>💰 إدارة الرواتب</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    border-radius: 8px;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #34495e;
    color: #3498db;
}
QPushButton:pressed {
    background-color: #2c3e50;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="financial_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string>💳 العمليات المالية</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    border-radius: 8px;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #34495e;
    color: #3498db;
}
QPushButton:pressed {
    background-color: #2c3e50;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="reports_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string>📊 التقارير</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    border-radius: 8px;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #34495e;
    color: #3498db;
}
QPushButton:pressed {
    background-color: #2c3e50;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="audit_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string>🔍 تدقيق الأنشطة</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    border-radius: 8px;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #34495e;
    color: #3498db;
}
QPushButton:pressed {
    background-color: #2c3e50;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="menu_spacer">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="settings_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string>⚙️ الإعدادات</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    border-radius: 8px;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #34495e;
    color: #3498db;
}
QPushButton:pressed {
    background-color: #2c3e50;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QFrame" name="sidebar_footer">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>80</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>80</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QFrame {
    background-color: #34495e;
    border-top: 2px solid #3498db;
    border-radius: 0px;
}</string>
             </property>
             <layout class="QVBoxLayout" name="footer_layout">
              <property name="spacing">
               <number>5</number>
              </property>
              <property name="leftMargin">
               <number>10</number>
              </property>
              <property name="topMargin">
               <number>10</number>
              </property>
              <property name="rightMargin">
               <number>10</number>
              </property>
              <property name="bottomMargin">
               <number>10</number>
              </property>
              <item>
               <widget class="QLabel" name="version_label">
                <property name="text">
                 <string>الإصدار 2.0</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
    color: #bdc3c7;
    font-size: 10px;
    font-weight: normal;
}</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="copyright_label">
                <property name="text">
                 <string>© 2024 جميع الحقوق محفوظة</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
    color: #95a5a6;
    font-size: 9px;
    font-weight: normal;
}</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="content_frame">
      <property name="styleSheet">
       <string notr="true">QFrame {
    background-color: #ecf0f1;
    border: none;
}</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="content_layout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QFrame" name="toolbar_frame">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>60</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>60</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QFrame {
    background-color: white;
    border-bottom: 2px solid #e9ecef;
    padding: 10px;
}</string>
         </property>
         <layout class="QHBoxLayout" name="toolbar_layout">
          <property name="spacing">
           <number>15</number>
          </property>
          <property name="leftMargin">
           <number>20</number>
          </property>
          <property name="topMargin">
           <number>10</number>
          </property>
          <property name="rightMargin">
           <number>20</number>
          </property>
          <property name="bottomMargin">
           <number>10</number>
          </property>
          <item>
           <widget class="QLabel" name="page_title_label">
            <property name="text">
             <string>📊 لوحة التحكم الرئيسية</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    font-size: 20px;
    font-weight: bold;
    color: #2c3e50;
}</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="toolbar_spacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="refresh_btn">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>35</height>
             </size>
            </property>
            <property name="text">
             <string>🔄 تحديث</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 15px;
    font-size: 12px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #2980b9;
}
QPushButton:pressed {
    background-color: #21618c;
}</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="search_btn">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>35</height>
             </size>
            </property>
            <property name="text">
             <string>🔍 بحث</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    background-color: #27ae60;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 15px;
    font-size: 12px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #229954;
}
QPushButton:pressed {
    background-color: #1e8449;
}</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="user_info_label">
            <property name="text">
             <string>👤 مدير النظام</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    font-size: 12px;
    color: #7f8c8d;
    padding: 5px 10px;
    background-color: #ecf0f1;
    border-radius: 15px;
}</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QStackedWidget" name="content_stack">
         <property name="currentIndex">
          <number>0</number>
         </property>
         <widget class="QWidget" name="dashboard_page">
          <layout class="QVBoxLayout" name="dashboard_layout">
           <property name="spacing">
            <number>20</number>
           </property>
           <property name="leftMargin">
            <number>20</number>
           </property>
           <property name="topMargin">
            <number>20</number>
           </property>
           <property name="rightMargin">
            <number>20</number>
           </property>
           <property name="bottomMargin">
            <number>20</number>
           </property>
           <item>
            <widget class="QScrollArea" name="dashboard_scroll">
             <property name="styleSheet">
              <string notr="true">QScrollArea {
    border: none;
    background-color: transparent;
}
QScrollBar:vertical {
    background-color: #ecf0f1;
    width: 12px;
    border-radius: 6px;
}
QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-height: 20px;
}
QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}</string>
             </property>
             <property name="widgetResizable">
              <bool>true</bool>
             </property>
             <widget class="QWidget" name="dashboard_content">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>1128</width>
                <height>69</height>
               </rect>
              </property>
              <layout class="QVBoxLayout" name="dashboard_content_layout">
               <item>
                <widget class="QLabel" name="welcome_label">
                 <property name="text">
                  <string>🎉 مرحباً بك في نظام إدارة الموارد البشرية المتقدم</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">QLabel {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    padding: 30px;
    background-color: white;
    border-radius: 10px;
    border: 2px solid #3498db;
    margin: 10px;
}</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="dashboard_spacer">
                 <property name="orientation">
                  <enum>Qt::Vertical</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>20</width>
                   <height>40</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="employees_page">
          <layout class="QVBoxLayout" name="employees_layout">
           <item>
            <widget class="QLabel" name="employees_label">
             <property name="text">
              <string>👥 إدارة الموظفين</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
             <property name="styleSheet">
              <string notr="true">QLabel {
    font-size: 20px;
    font-weight: bold;
    color: #2c3e50;
    padding: 20px;
}</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="departments_page">
          <layout class="QVBoxLayout" name="departments_layout">
           <item>
            <widget class="QLabel" name="departments_label">
             <property name="text">
              <string>🏢 إدارة الأقسام</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
             <property name="styleSheet">
              <string notr="true">QLabel {
    font-size: 20px;
    font-weight: bold;
    color: #2c3e50;
    padding: 20px;
}</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="salaries_page">
          <layout class="QVBoxLayout" name="salaries_layout">
           <item>
            <widget class="QLabel" name="salaries_label">
             <property name="text">
              <string>💰 إدارة الرواتب</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
             <property name="styleSheet">
              <string notr="true">QLabel {
    font-size: 20px;
    font-weight: bold;
    color: #2c3e50;
    padding: 20px;
}</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="reports_page">
          <layout class="QVBoxLayout" name="reports_layout">
           <item>
            <widget class="QLabel" name="reports_label">
             <property name="text">
              <string>📊 التقارير</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
             <property name="styleSheet">
              <string notr="true">QLabel {
    font-size: 20px;
    font-weight: bold;
    color: #2c3e50;
    padding: 20px;
}</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1400</width>
     <height>30</height>
    </rect>
   </property>
   <widget class="QMenu" name="menu_file">
    <property name="title">
     <string>📁 ملف</string>
    </property>
    <addaction name="action_new"/>
    <addaction name="action_open"/>
    <addaction name="action_save"/>
    <addaction name="separator"/>
    <addaction name="action_import"/>
    <addaction name="action_export"/>
    <addaction name="separator"/>
    <addaction name="action_exit"/>
   </widget>
   <widget class="QMenu" name="menu_edit">
    <property name="title">
     <string>✏️ تحرير</string>
    </property>
    <addaction name="action_undo"/>
    <addaction name="action_redo"/>
    <addaction name="separator"/>
    <addaction name="action_cut"/>
    <addaction name="action_copy"/>
    <addaction name="action_paste"/>
   </widget>
   <widget class="QMenu" name="menu_view">
    <property name="title">
     <string>👁️ عرض</string>
    </property>
    <addaction name="action_fullscreen"/>
    <addaction name="action_zoom_in"/>
    <addaction name="action_zoom_out"/>
    <addaction name="separator"/>
    <addaction name="action_theme_light"/>
    <addaction name="action_theme_dark"/>
   </widget>
   <widget class="QMenu" name="menu_tools">
    <property name="title">
     <string>🔧 أدوات</string>
    </property>
    <addaction name="action_backup"/>
    <addaction name="action_restore"/>
    <addaction name="separator"/>
    <addaction name="action_calculator"/>
    <addaction name="action_calendar"/>
   </widget>
   <widget class="QMenu" name="menu_help">
    <property name="title">
     <string>❓ مساعدة</string>
    </property>
    <addaction name="action_help"/>
    <addaction name="action_shortcuts"/>
    <addaction name="separator"/>
    <addaction name="action_about"/>
   </widget>
   <addaction name="menu_file"/>
   <addaction name="menu_edit"/>
   <addaction name="menu_view"/>
   <addaction name="menu_tools"/>
   <addaction name="menu_help"/>
  </widget>
  <widget class="QStatusBar" name="statusbar">
   <property name="styleSheet">
    <string notr="true">QStatusBar {
    background-color: #34495e;
    color: white;
    border-top: 2px solid #3498db;
    padding: 5px;
    font-size: 12px;
}

QStatusBar::item {
    border: none;
    padding: 2px 8px;
}</string>
   </property>
  </widget>
  <widget class="QToolBar" name="main_toolbar">
   <property name="windowTitle">
    <string>شريط الأدوات الرئيسي</string>
   </property>
   <property name="styleSheet">
    <string notr="true">QToolBar {
    background-color: #34495e;
    border: none;
    spacing: 5px;
    padding: 5px;
}

QToolBar::handle {
    background-color: #2c3e50;
    width: 10px;
    margin: 2px;
}

QToolButton {
    background-color: transparent;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px;
    margin: 2px;
    font-size: 12px;
}

QToolButton:hover {
    background-color: #3498db;
}

QToolButton:pressed {
    background-color: #2980b9;
}

QToolButton:checked {
    background-color: #27ae60;
}</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="action_new"/>
   <addaction name="action_save"/>
   <addaction name="separator"/>
   <addaction name="action_import"/>
   <addaction name="action_export"/>
   <addaction name="separator"/>
   <addaction name="action_backup"/>
   <addaction name="separator"/>
   <addaction name="action_help"/>
  </widget>
 </widget>
 <action name="action_new">
  <property name="text">
   <string>📄 جديد</string>
  </property>
  <property name="shortcut">
   <string>Ctrl+N</string>
  </property>
 </action>
 <action name="action_open">
  <property name="text">
   <string>📂 فتح</string>
  </property>
  <property name="shortcut">
   <string>Ctrl+O</string>
  </property>
 </action>
 <action name="action_save">
  <property name="text">
   <string>💾 حفظ</string>
  </property>
  <property name="shortcut">
   <string>Ctrl+S</string>
  </property>
 </action>
 <action name="action_import">
  <property name="text">
   <string>📥 استيراد</string>
  </property>
 </action>
 <action name="action_export">
  <property name="text">
   <string>📤 تصدير</string>
  </property>
 </action>
 <action name="action_exit">
  <property name="text">
   <string>🚪 خروج</string>
  </property>
  <property name="shortcut">
   <string>Ctrl+Q</string>
  </property>
 </action>
 <action name="action_undo">
  <property name="text">
   <string>↶ تراجع</string>
  </property>
  <property name="shortcut">
   <string>Ctrl+Z</string>
  </property>
 </action>
 <action name="action_redo">
  <property name="text">
   <string>↷ إعادة</string>
  </property>
  <property name="shortcut">
   <string>Ctrl+Y</string>
  </property>
 </action>
 <action name="action_cut">
  <property name="text">
   <string>✂️ قص</string>
  </property>
  <property name="shortcut">
   <string>Ctrl+X</string>
  </property>
 </action>
 <action name="action_copy">
  <property name="text">
   <string>📋 نسخ</string>
  </property>
  <property name="shortcut">
   <string>Ctrl+C</string>
  </property>
 </action>
 <action name="action_paste">
  <property name="text">
   <string>📌 لصق</string>
  </property>
  <property name="shortcut">
   <string>Ctrl+V</string>
  </property>
 </action>
 <action name="action_fullscreen">
  <property name="text">
   <string>🖥️ ملء الشاشة</string>
  </property>
  <property name="shortcut">
   <string>F11</string>
  </property>
 </action>
 <action name="action_zoom_in">
  <property name="text">
   <string>🔍+ تكبير</string>
  </property>
  <property name="shortcut">
   <string>Ctrl+=</string>
  </property>
 </action>
 <action name="action_zoom_out">
  <property name="text">
   <string>🔍- تصغير</string>
  </property>
  <property name="shortcut">
   <string>Ctrl+-</string>
  </property>
 </action>
 <action name="action_theme_light">
  <property name="text">
   <string>☀️ مظهر فاتح</string>
  </property>
 </action>
 <action name="action_theme_dark">
  <property name="text">
   <string>🌙 مظهر داكن</string>
  </property>
 </action>
 <action name="action_backup">
  <property name="text">
   <string>💾 نسخ احتياطي</string>
  </property>
 </action>
 <action name="action_restore">
  <property name="text">
   <string>🔄 استعادة</string>
  </property>
 </action>
 <action name="action_calculator">
  <property name="text">
   <string>🧮 حاسبة</string>
  </property>
 </action>
 <action name="action_calendar">
  <property name="text">
   <string>📅 تقويم</string>
  </property>
 </action>
 <action name="action_help">
  <property name="text">
   <string>📖 دليل المستخدم</string>
  </property>
  <property name="shortcut">
   <string>F1</string>
  </property>
 </action>
 <action name="action_shortcuts">
  <property name="text">
   <string>⌨️ اختصارات لوحة المفاتيح</string>
  </property>
 </action>
 <action name="action_about">
  <property name="text">
   <string>ℹ️ حول البرنامج</string>
  </property>
 </action>
 <resources/>
 <connections/>
</ui>
