#!/usr/bin/env python3
"""
إنشاء بيانات تجريبية لنظام إدارة شؤون الموظفين
Create Sample Data for HR Management System
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.database import db_manager, get_db_session_context
from src.models import Department, JobTitle


def create_sample_departments():
    """إنشاء أقسام تجريبية"""
    departments_data = [
        {
            "code": "IT",
            "name": "قسم تقنية المعلومات",
            "description": "قسم مسؤول عن إدارة وتطوير الأنظمة التقنية"
        },
        {
            "code": "HR",
            "name": "قسم الموارد البشرية",
            "description": "قسم مسؤول عن إدارة شؤون الموظفين"
        },
        {
            "code": "FIN",
            "name": "قسم المالية",
            "description": "قسم مسؤول عن الشؤون المالية والمحاسبية"
        },
        {
            "code": "OPS",
            "name": "قسم العمليات",
            "description": "قسم مسؤول عن العمليات التشغيلية"
        },
        {
            "code": "MKT",
            "name": "قسم التسويق",
            "description": "قسم مسؤول عن التسويق والمبيعات"
        }
    ]
    
    with get_db_session_context() as session:
        for dept_data in departments_data:
            # التحقق من عدم وجود القسم مسبقاً
            existing = session.query(Department).filter_by(code=dept_data["code"]).first()
            if not existing:
                department = Department(**dept_data)
                session.add(department)
                print(f"✅ تم إنشاء القسم: {dept_data['name']}")
            else:
                print(f"⚠️ القسم موجود مسبقاً: {dept_data['name']}")


def create_sample_job_titles():
    """إنشاء عناوين وظيفية تجريبية"""
    job_titles_data = [
        {
            "code": "MGR",
            "title": "مدير",
            "description": "مدير القسم",
            "min_salary": 2000000,
            "max_salary": 4000000
        },
        {
            "code": "DEV",
            "title": "مطور برمجيات",
            "description": "مطور تطبيقات وأنظمة",
            "min_salary": 1200000,
            "max_salary": 2500000
        },
        {
            "code": "ACC",
            "title": "محاسب",
            "description": "محاسب مالي",
            "min_salary": 1000000,
            "max_salary": 2000000
        },
        {
            "code": "HR_SP",
            "title": "أخصائي موارد بشرية",
            "description": "أخصائي في إدارة الموارد البشرية",
            "min_salary": 1100000,
            "max_salary": 2200000
        },
        {
            "code": "SALES",
            "title": "مندوب مبيعات",
            "description": "مندوب مبيعات وتسويق",
            "min_salary": 800000,
            "max_salary": 1800000
        },
        {
            "code": "ADMIN",
            "title": "موظف إداري",
            "description": "موظف إداري عام",
            "min_salary": 700000,
            "max_salary": 1500000
        },
        {
            "code": "TECH",
            "title": "فني",
            "description": "فني صيانة وتشغيل",
            "min_salary": 600000,
            "max_salary": 1200000
        }
    ]
    
    with get_db_session_context() as session:
        for job_data in job_titles_data:
            # التحقق من عدم وجود العنوان الوظيفي مسبقاً
            existing = session.query(JobTitle).filter_by(code=job_data["code"]).first()
            if not existing:
                job_title = JobTitle(**job_data)
                session.add(job_title)
                print(f"✅ تم إنشاء العنوان الوظيفي: {job_data['title']}")
            else:
                print(f"⚠️ العنوان الوظيفي موجود مسبقاً: {job_data['title']}")


def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إنشاء البيانات التجريبية...")
    print("=" * 50)

    try:
        # تهيئة قاعدة البيانات
        print("🔧 تهيئة قاعدة البيانات...")
        db_manager.initialize()
        db_manager.create_tables()

        print("\n📁 إنشاء الأقسام...")
        create_sample_departments()

        print("\n💼 إنشاء العناوين الوظيفية...")
        create_sample_job_titles()

        print("\n" + "=" * 50)
        print("✅ تم إنشاء البيانات التجريبية بنجاح!")
        print("\nيمكنك الآن:")
        print("1. إضافة موظفين جدد")
        print("2. إدارة الأقسام والعناوين الوظيفية")
        print("3. تسجيل المعاملات المالية")

    except Exception as e:
        print(f"\n❌ خطأ في إنشاء البيانات التجريبية: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
