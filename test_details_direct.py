#!/usr/bin/env python3
"""
اختبار مباشر لنافذة تفاصيل الموظف
Direct Test for Employee Details Dialog
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication
from src.database import db_manager, get_db_session_context
from src.models import Employee


def test_direct_details():
    """اختبار مباشر لنافذة التفاصيل"""
    print("🧪 اختبار مباشر لنافذة تفاصيل الموظف...")
    
    try:
        # تهيئة قاعدة البيانات
        db_manager.initialize()
        
        # البحث عن موظف
        with get_db_session_context() as session:
            employee = session.query(Employee).filter_by(is_active=True).first()
            
            if not employee:
                print("❌ لا يوجد موظفين في النظام")
                return False
            
            print(f"✅ الموظف: {employee.full_name} (ID: {employee.id})")
            
            # إنشاء التطبيق
            app = QApplication(sys.argv)
            
            # تطبيق التخطيط من اليمين إلى اليسار
            app.setLayoutDirection(app.RightToLeft)
            
            # تحميل الأنماط
            try:
                styles_dir = Path("src/styles")
                main_style_file = styles_dir / "main.qss"
                if main_style_file.exists():
                    with open(main_style_file, 'r', encoding='utf-8') as f:
                        app.setStyleSheet(f.read())
                    print("✅ تم تحميل الأنماط")
            except Exception as e:
                print(f"⚠️ فشل في تحميل الأنماط: {e}")
            
            # استيراد وإنشاء نافذة التفاصيل
            print("🔧 إنشاء نافذة التفاصيل...")
            from src.views.employee_details import EmployeeDetailsDialog
            
            dialog = EmployeeDetailsDialog(employee.id)
            
            # عرض النافذة
            print("🖥️ عرض النافذة...")
            dialog.show()
            
            print("✅ تم فتح نافذة تفاصيل الموظف!")
            print("\nالنافذة مفتوحة الآن. يمكنك:")
            print("1. مراجعة المعلومات الأساسية")
            print("2. مراجعة المعاملات المالية")
            print("3. مراجعة سجل الرواتب")
            print("4. إغلاق النافذة")
            print("\nاضغط Ctrl+C لإنهاء الاختبار")
            
            # تشغيل التطبيق
            return app.exec()
            
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1


def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار مباشر لنافذة تفاصيل الموظف")
    print("=" * 50)
    
    result = test_direct_details()
    
    if result == 0:
        print("✅ الاختبار نجح!")
    else:
        print("💥 فشل الاختبار!")
    
    return result


if __name__ == "__main__":
    sys.exit(main())
