# هيكل المشروع النهائي
# Final Project Structure

## نظام إدارة شؤون الموظفين
### HR Management System

```
HR/
├── src/                          # المجلد الرئيسي للكود المصدري
│   ├── __init__.py
│   ├── main.py                   # نقطة دخول التطبيق الرئيسية
│   ├── config.py                 # إعدادات التطبيق
│   │
│   ├── config/                   # ملفات التكوين
│   │   ├── __init__.py
│   │   └── database.py           # إعدادات قاعدة البيانات
│   │
│   ├── database/                 # طبقة قاعدة البيانات
│   │   ├── __init__.py
│   │   ├── connection.py         # اتصال قاعدة البيانات
│   │   ├── session.py            # إدارة الجلسات
│   │   └── migrations.py         # ترحيل قاعدة البيانات
│   │
│   ├── models/                   # نماذج البيانات (SQLAlchemy)
│   │   ├── __init__.py
│   │   ├── base.py               # النموذج الأساسي
│   │   ├── employee.py           # نموذج الموظف
│   │   ├── department.py         # نموذج القسم
│   │   ├── job_title.py          # نموذج العنوان الوظيفي
│   │   ├── financial_transaction.py  # نموذج المعاملات المالية
│   │   ├── salary_record.py      # نموذج سجل الراتب
│   │   ├── audit_log.py          # نموذج سجل التدقيق
│   │   └── enums.py              # التعدادات والثوابت
│   │
│   ├── views/                    # واجهات المستخدم (PySide6)
│   │   ├── __init__.py
│   │   ├── main_window.py        # النافذة الرئيسية
│   │   ├── dashboard.py          # لوحة التحكم
│   │   ├── employees_view.py     # واجهة إدارة الموظفين
│   │   ├── employee_form.py      # نموذج إضافة/تعديل الموظف
│   │   ├── employee_details.py   # تفاصيل الموظف
│   │   ├── departments_view.py   # واجهة إدارة الأقسام
│   │   ├── department_form.py    # نموذج إضافة/تعديل القسم
│   │   ├── job_titles_view.py    # واجهة إدارة العناوين الوظيفية
│   │   ├── job_title_form.py     # نموذج إضافة/تعديل العنوان الوظيفي
│   │   ├── financial_view.py     # واجهة المعاملات المالية
│   │   ├── financial_form.py     # نموذج إضافة/تعديل المعاملة المالية
│   │   ├── salaries_view.py      # واجهة إدارة الرواتب
│   │   ├── reports_view.py       # واجهة التقارير
│   │   ├── backup_view.py        # واجهة النسخ الاحتياطي
│   │   └── audit_view.py         # واجهة سجلات التدقيق
│   │
│   ├── utils/                    # الأدوات المساعدة
│   │   ├── __init__.py
│   │   ├── helpers.py            # دوال مساعدة عامة
│   │   ├── ui_helpers.py         # أدوات مساعدة للواجهة
│   │   ├── ui_improvements.py    # تحسينات الواجهة الشاملة
│   │   ├── validation.py         # التحقق من صحة البيانات
│   │   ├── search_filter.py      # نظام البحث والتصفية
│   │   ├── report_generator.py   # مولد التقارير
│   │   ├── backup_manager.py     # مدير النسخ الاحتياطي
│   │   └── audit_logger.py       # مسجل أنشطة التدقيق
│   │
│   └── styles/                   # ملفات التصميم (QSS)
│       ├── __init__.py
│       ├── main.qss              # النمط الرئيسي
│       ├── dark_theme.qss        # النمط المظلم
│       └── light_theme.qss       # النمط الفاتح
│
├── tests/                        # الاختبارات
│   ├── __init__.py
│   ├── conftest.py               # إعدادات الاختبارات
│   ├── test_models.py            # اختبارات النماذج
│   ├── test_services.py          # اختبارات الخدمات
│   └── test_views.py             # اختبارات الواجهات
│
├── docs/                         # التوثيق
│   ├── user_guide.md             # دليل المستخدم
│   ├── developer_guide.md        # دليل المطور
│   └── api_reference.md          # مرجع API
│
├── config/                       # ملفات التكوين
│   └── config.json               # إعدادات التطبيق
│
├── backups/                      # النسخ الاحتياطية
│   └── (ملفات النسخ الاحتياطية)
│
├── logs/                         # ملفات السجلات
│   └── hr_system.log             # سجل النظام
│
├── migrations/                   # ترحيلات قاعدة البيانات
│   └── (ملفات الترحيل)
│
├── .gitignore                    # ملفات Git المتجاهلة
├── pyproject.toml                # إعدادات Poetry
├── requirements.txt              # متطلبات Python
├── README.md                     # وصف المشروع
├── CHANGELOG.md                  # سجل التغييرات
├── DATABASE_SETUP.md             # دليل إعداد قاعدة البيانات
├── SUMMARY.md                    # ملخص المشروع
├── PROJECT_STRUCTURE.md          # هيكل المشروع (هذا الملف)
├── Makefile                      # أوامر البناء والتشغيل
├── start.bat                     # ملف تشغيل Windows
├── start.sh                      # ملف تشغيل Linux/Mac
└── cleanup_project.py            # سكريبت تنظيف المشروع
```

## الميزات الرئيسية

### 🏢 إدارة الموظفين
- إضافة وتعديل وحذف الموظفين
- البحث والتصفية المتقدمة
- عرض تفاصيل الموظف الشاملة
- إدارة الأقسام والعناوين الوظيفية

### 💰 النظام المالي
- إدارة السلف والخصومات
- تسجيل المكافآت
- متابعة ديون الماركت
- كشف حساب الموظف

### 📊 نظام الرواتب
- حساب الرواتب الشهرية
- مسير الرواتب
- صرف الرواتب
- تقارير الرواتب

### 📈 التقارير
- تقارير الموظفين
- تقارير الرواتب
- تقارير المعاملات المالية
- كشف حساب الموظف
- تصدير إلى CSV

### 🔒 الأمان والتدقيق
- تسجيل جميع العمليات
- النسخ الاحتياطي التلقائي
- استعادة البيانات
- سجلات التدقيق

### 🎨 الواجهة
- تصميم حديث ومتجاوب
- دعم اللغة العربية (RTL)
- نمط فاتح ومظلم
- واجهة سهلة الاستخدام

## التقنيات المستخدمة

- **Python 3.8+**: لغة البرمجة الأساسية
- **PySide6**: واجهة المستخدم الرسومية
- **SQLAlchemy**: ORM لقاعدة البيانات
- **SQLite**: قاعدة البيانات المحلية
- **Poetry**: إدارة التبعيات
- **pytest**: إطار الاختبارات

## كيفية التشغيل

### Windows
```bash
start.bat
```

### Linux/Mac
```bash
./start.sh
```

### يدوياً
```bash
cd src
python main.py
```

## المتطلبات

- Python 3.8 أو أحدث
- PySide6
- SQLAlchemy
- جميع المتطلبات في `requirements.txt`

## الترخيص

هذا المشروع مطور خصيصاً لإدارة شؤون الموظفين ويمكن استخدامه وتعديله حسب الحاجة.
