#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الوضع الليلي المحسن
Enhanced Dark Mode Testing
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_dark_mode_styles():
    """اختبار تحميل ملفات الوضع الليلي"""
    print("🌙 اختبار ملفات الوضع الليلي...")
    
    styles_dir = project_root / "src" / "styles"
    dark_file = styles_dir / "dark.qss"
    dashboard_dark_file = styles_dir / "enhanced_dashboard_dark.qss"
    
    # اختبار وجود الملفات
    if not dark_file.exists():
        print(f"❌ ملف الوضع الليلي غير موجود: {dark_file}")
        return False
    
    if not dashboard_dark_file.exists():
        print(f"❌ ملف لوحة التحكم الليلية غير موجود: {dashboard_dark_file}")
        return False
    
    # اختبار قراءة الملفات
    try:
        with open(dark_file, 'r', encoding='utf-8') as f:
            dark_content = f.read()
        
        with open(dashboard_dark_file, 'r', encoding='utf-8') as f:
            dashboard_content = f.read()
        
        print(f"✅ تم تحميل ملف الوضع الليلي ({len(dark_content)} حرف)")
        print(f"✅ تم تحميل ملف لوحة التحكم الليلية ({len(dashboard_content)} حرف)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قراءة ملفات الوضع الليلي: {e}")
        return False


def create_dark_mode_test_window():
    """إنشاء نافذة اختبار الوضع الليلي المحسن"""
    
    class DarkModeTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("🌙 اختبار الوضع الليلي المحسن")
            self.setGeometry(100, 100, 1200, 800)
            self.setMinimumSize(800, 600)
            
            # تطبيق الوضع الليلي
            self.apply_dark_mode()
            
            # إعداد الواجهة
            self.setup_ui()
            
        def apply_dark_mode(self):
            """تطبيق الوضع الليلي المحسن"""
            try:
                styles_dir = Path("src/styles")
                dark_file = styles_dir / "dark.qss"
                
                if dark_file.exists():
                    with open(dark_file, 'r', encoding='utf-8') as f:
                        stylesheet = f.read()
                    
                    self.setStyleSheet(stylesheet)
                    print("✅ تم تطبيق الوضع الليلي المحسن")
                else:
                    print("❌ ملف الوضع الليلي غير موجود")
                    
            except Exception as e:
                print(f"❌ خطأ في تطبيق الوضع الليلي: {e}")
        
        def setup_ui(self):
            """إعداد واجهة الاختبار"""
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # العنوان الرئيسي
            title = QLabel("🌙 اختبار الوضع الليلي المحسن")
            title.setObjectName("app_title")
            title.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(title)
            
            # أزرار التحكم
            controls_frame = self.create_controls_frame()
            main_layout.addWidget(controls_frame)
            
            # التبويبات
            tabs = self.create_tabs()
            main_layout.addWidget(tabs)
            
            # شريط الحالة
            self.create_status_bar()
        
        def create_controls_frame(self):
            """إنشاء إطار أزرار التحكم"""
            frame = QFrame()
            frame.setObjectName("header")
            layout = QHBoxLayout(frame)
            
            # زر تبديل الوضع
            toggle_btn = QPushButton("🔄 تبديل للوضع النهاري")
            toggle_btn.setObjectName("quick_action_button")
            toggle_btn.clicked.connect(self.toggle_theme)
            layout.addWidget(toggle_btn)
            
            # زر إعادة تحميل الأنماط
            reload_btn = QPushButton("🔄 إعادة تحميل الأنماط")
            reload_btn.setObjectName("success_button")
            reload_btn.clicked.connect(self.reload_styles)
            layout.addWidget(reload_btn)
            
            # زر اختبار التأثيرات
            effects_btn = QPushButton("✨ اختبار التأثيرات")
            effects_btn.setObjectName("info_button")
            effects_btn.clicked.connect(self.test_effects)
            layout.addWidget(effects_btn)
            
            layout.addStretch()
            
            # معلومات المستخدم
            user_info = QLabel("👤 مستخدم الاختبار")
            user_info.setObjectName("user_info")
            layout.addWidget(user_info)
            
            return frame
        
        def create_tabs(self):
            """إنشاء التبويبات"""
            tabs = QTabWidget()
            
            # تبويب العناصر الأساسية
            basic_tab = self.create_basic_elements_tab()
            tabs.addTab(basic_tab, "العناصر الأساسية")
            
            # تبويب الجداول
            tables_tab = self.create_tables_tab()
            tabs.addTab(tables_tab, "الجداول")
            
            # تبويب البطاقات الإحصائية
            cards_tab = self.create_cards_tab()
            tabs.addTab(cards_tab, "البطاقات الإحصائية")
            
            # تبويب النماذج
            forms_tab = self.create_forms_tab()
            tabs.addTab(forms_tab, "النماذج")
            
            return tabs
        
        def create_basic_elements_tab(self):
            """إنشاء تبويب العناصر الأساسية"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # مجموعة الأزرار
            buttons_group = QGroupBox("الأزرار")
            buttons_layout = QHBoxLayout(buttons_group)
            
            primary_btn = QPushButton("زر أساسي")
            success_btn = QPushButton("زر نجاح")
            success_btn.setObjectName("success_button")
            danger_btn = QPushButton("زر خطر")
            danger_btn.setObjectName("danger_button")
            warning_btn = QPushButton("زر تحذير")
            warning_btn.setObjectName("warning_button")
            info_btn = QPushButton("زر معلومات")
            info_btn.setObjectName("info_button")
            
            for btn in [primary_btn, success_btn, danger_btn, warning_btn, info_btn]:
                buttons_layout.addWidget(btn)
            
            layout.addWidget(buttons_group)
            
            # مجموعة الحقول
            inputs_group = QGroupBox("حقول الإدخال")
            inputs_layout = QFormLayout(inputs_group)
            
            line_edit = QLineEdit("نص تجريبي")
            combo_box = QComboBox()
            combo_box.addItems(["خيار 1", "خيار 2", "خيار 3"])
            date_edit = QDateEdit(QDate.currentDate())
            text_edit = QTextEdit("نص متعدد الأسطر\nللاختبار")
            text_edit.setMaximumHeight(100)
            
            inputs_layout.addRow("حقل نص:", line_edit)
            inputs_layout.addRow("قائمة منسدلة:", combo_box)
            inputs_layout.addRow("تاريخ:", date_edit)
            inputs_layout.addRow("نص متعدد:", text_edit)
            
            layout.addWidget(inputs_group)
            
            # مجموعة خانات الاختيار
            checkboxes_group = QGroupBox("خانات الاختيار")
            checkboxes_layout = QVBoxLayout(checkboxes_group)
            
            checkbox1 = QCheckBox("خيار أول")
            checkbox1.setChecked(True)
            checkbox2 = QCheckBox("خيار ثاني")
            checkbox3 = QCheckBox("خيار ثالث معطل")
            checkbox3.setEnabled(False)
            
            radio1 = QRadioButton("راديو أول")
            radio1.setChecked(True)
            radio2 = QRadioButton("راديو ثاني")
            
            for widget in [checkbox1, checkbox2, checkbox3, radio1, radio2]:
                checkboxes_layout.addWidget(widget)
            
            layout.addWidget(checkboxes_group)
            
            layout.addStretch()
            return widget
        
        def create_tables_tab(self):
            """إنشاء تبويب الجداول"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # جدول تجريبي
            table = QTableWidget(5, 4)
            table.setHorizontalHeaderLabels(["الاسم", "القسم", "الراتب", "الحالة"])
            
            # بيانات تجريبية
            data = [
                ["أحمد محمد", "تقنية المعلومات", "5000", "نشط"],
                ["فاطمة علي", "الموارد البشرية", "4500", "نشط"],
                ["محمد أحمد", "المحاسبة", "4000", "معطل"],
                ["سارة محمود", "التسويق", "3500", "نشط"],
                ["علي حسن", "المبيعات", "3000", "نشط"]
            ]
            
            for row, row_data in enumerate(data):
                for col, value in enumerate(row_data):
                    table.setItem(row, col, QTableWidgetItem(value))
            
            # تنسيق الجدول
            table.resizeColumnsToContents()
            table.setAlternatingRowColors(True)
            table.setSelectionBehavior(QAbstractItemView.SelectRows)
            
            layout.addWidget(QLabel("جدول تجريبي:"))
            layout.addWidget(table)
            
            return widget
        
        def create_cards_tab(self):
            """إنشاء تبويب البطاقات الإحصائية"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # شبكة البطاقات
            cards_layout = QGridLayout()
            
            # بيانات البطاقات
            cards_data = [
                ("الموظفين", "150", "موظف", "employees_card"),
                ("الرواتب", "750,000", "ريال", "salaries_card"),
                ("السلف", "25,000", "ريال", "advances_card"),
                ("الديون", "15,000", "ريال", "debts_card"),
                ("المكافآت", "50,000", "ريال", "bonuses_card"),
                ("الأقسام", "8", "قسم", "departments_card")
            ]
            
            for i, (title, value, subtitle, object_name) in enumerate(cards_data):
                card = self.create_stat_card(title, value, subtitle, object_name)
                row = i // 3
                col = i % 3
                cards_layout.addWidget(card, row, col)
            
            layout.addLayout(cards_layout)
            layout.addStretch()
            
            return widget
        
        def create_stat_card(self, title, value, subtitle, object_name):
            """إنشاء بطاقة إحصائية"""
            card = QFrame()
            card.setObjectName(object_name)
            card.setFixedSize(300, 200)
            
            layout = QVBoxLayout(card)
            layout.setAlignment(Qt.AlignCenter)
            
            title_label = QLabel(title)
            title_label.setObjectName("card_title")
            title_label.setAlignment(Qt.AlignCenter)
            
            value_label = QLabel(value)
            value_label.setObjectName("card_value")
            value_label.setAlignment(Qt.AlignCenter)
            
            subtitle_label = QLabel(subtitle)
            subtitle_label.setObjectName("card_subtitle")
            subtitle_label.setAlignment(Qt.AlignCenter)
            
            layout.addWidget(title_label)
            layout.addWidget(value_label)
            layout.addWidget(subtitle_label)
            
            return card
        
        def create_forms_tab(self):
            """إنشاء تبويب النماذج"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # نموذج تجريبي
            form_group = QGroupBox("نموذج إضافة موظف")
            form_layout = QFormLayout(form_group)
            
            name_edit = QLineEdit()
            name_edit.setPlaceholderText("أدخل اسم الموظف")
            
            department_combo = QComboBox()
            department_combo.addItems(["تقنية المعلومات", "الموارد البشرية", "المحاسبة", "التسويق"])
            
            salary_spin = QSpinBox()
            salary_spin.setRange(1000, 50000)
            salary_spin.setValue(5000)
            salary_spin.setSuffix(" ريال")
            
            hire_date = QDateEdit(QDate.currentDate())
            
            notes_text = QTextEdit()
            notes_text.setPlaceholderText("ملاحظات إضافية...")
            notes_text.setMaximumHeight(100)
            
            active_check = QCheckBox("موظف نشط")
            active_check.setChecked(True)
            
            form_layout.addRow("الاسم:", name_edit)
            form_layout.addRow("القسم:", department_combo)
            form_layout.addRow("الراتب:", salary_spin)
            form_layout.addRow("تاريخ التوظيف:", hire_date)
            form_layout.addRow("ملاحظات:", notes_text)
            form_layout.addRow("", active_check)
            
            # أزرار النموذج
            buttons_layout = QHBoxLayout()
            save_btn = QPushButton("💾 حفظ")
            save_btn.setObjectName("success_button")
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.setObjectName("danger_button")
            
            buttons_layout.addWidget(save_btn)
            buttons_layout.addWidget(cancel_btn)
            buttons_layout.addStretch()
            
            layout.addWidget(form_group)
            layout.addLayout(buttons_layout)
            layout.addStretch()
            
            return widget
        
        def create_status_bar(self):
            """إنشاء شريط الحالة"""
            status_bar = self.statusBar()
            status_bar.showMessage("🌙 الوضع الليلي المحسن نشط | جاهز للاختبار")
        
        def toggle_theme(self):
            """تبديل الوضع"""
            try:
                styles_dir = Path("src/styles")
                light_file = styles_dir / "main.qss"
                
                if light_file.exists():
                    with open(light_file, 'r', encoding='utf-8') as f:
                        stylesheet = f.read()
                    
                    self.setStyleSheet(stylesheet)
                    self.statusBar().showMessage("☀️ تم التبديل للوضع النهاري")
                    print("☀️ تم التبديل للوضع النهاري")
                    
            except Exception as e:
                print(f"❌ خطأ في تبديل الوضع: {e}")
        
        def reload_styles(self):
            """إعادة تحميل الأنماط"""
            self.apply_dark_mode()
            self.statusBar().showMessage("🔄 تم إعادة تحميل الوضع الليلي")
            print("🔄 تم إعادة تحميل الوضع الليلي")
        
        def test_effects(self):
            """اختبار التأثيرات"""
            QMessageBox.information(self, "✨ التأثيرات", 
                                   "تم اختبار التأثيرات البصرية!\n"
                                   "لاحظ الظلال والتدرجات والانتقالات السلسة.")
    
    return DarkModeTestWindow()


def main():
    """الدالة الرئيسية"""
    print("🌙 اختبار الوضع الليلي المحسن")
    print("=" * 50)
    
    # اختبار ملفات الأنماط أولاً
    if not test_dark_mode_styles():
        print("💥 فشل في اختبار ملفات الأنماط")
        return 1
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء نافذة الاختبار
        window = create_dark_mode_test_window()
        window.show()
        
        print("✅ تم فتح نافذة اختبار الوضع الليلي المحسن!")
        print("\n🎯 يمكنك الآن:")
        print("1. اختبار جميع العناصر في الوضع الليلي")
        print("2. تبديل بين الوضع الليلي والنهاري")
        print("3. إعادة تحميل الأنماط")
        print("4. اختبار التأثيرات البصرية")
        print("5. مراجعة البطاقات الإحصائية والجداول")
        print("\nاضغط Ctrl+C لإنهاء الاختبار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار")
        return 0
    except Exception as e:
        print(f"💥 خطأ في تشغيل الاختبار: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
