"""
نموذج العناوين الوظيفية
Job Title model
"""

from sqlalchemy import Column, String, Text, Numeric
from sqlalchemy.orm import relationship
from .base import BaseModel


class JobTitle(BaseModel):
    """نموذج العناوين الوظيفية"""
    
    __tablename__ = "job_titles"
    
    title = Column(String(100), nullable=False, unique=True, comment="العنوان الوظيفي")
    description = Column(Text, nullable=True, comment="وصف الوظيفة")
    code = Column(String(10), nullable=False, unique=True, comment="رمز الوظيفة")
    min_salary = Column(Numeric(12, 2), nullable=True, comment="الحد الأدنى للراتب")
    max_salary = Column(Numeric(12, 2), nullable=True, comment="الحد الأقصى للراتب")
    
    # العلاقات
    employees = relationship("Employee", back_populates="job_title", lazy="dynamic")
    
    def __repr__(self) -> str:
        return f"<JobTitle(id={self.id}, title='{self.title}', code='{self.code}')>"
    
    def __str__(self) -> str:
        return self.title
    
    @property
    def employee_count(self) -> int:
        """عدد الموظفين في هذه الوظيفة"""
        return self.employees.filter_by(is_active=True).count()
    
    def get_active_employees(self):
        """الحصول على الموظفين النشطين في هذه الوظيفة"""
        return self.employees.filter_by(is_active=True).all()
    
    def is_salary_in_range(self, salary: float) -> bool:
        """التحقق من أن الراتب ضمن النطاق المحدد"""
        if self.min_salary and salary < float(self.min_salary):
            return False
        if self.max_salary and salary > float(self.max_salary):
            return False
        return True
